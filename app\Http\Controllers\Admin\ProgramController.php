<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ProgramController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Program::with('event');

        // Search functionality
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('instructor', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by level
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Filter by active status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        // Filter by online/offline
        if ($request->filled('is_online')) {
            $query->where('is_online', $request->is_online);
        }

        // Filter by event linkage
        if ($request->filled('has_event')) {
            if ($request->has_event === '1') {
                $query->whereNotNull('event_id');
            } else {
                $query->whereNull('event_id');
            }
        }

        $programs = $query->orderBy('sort_order', 'asc')
                         ->orderBy('created_at', 'desc')
                         ->paginate(15);

        return view('admin.programs.index', compact('programs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $events = Event::active()->orderBy('event_date', 'asc')->get();
        return view('admin.programs.create', compact('events'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255|unique:programs,title',
            'slug' => 'nullable|string|max:255|unique:programs,slug',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price' => 'nullable|numeric|min:0',
            'duration' => 'nullable|string|max:100',
            'max_participants' => 'nullable|integer|min:1',
            'schedule' => 'nullable|array',
            'level' => 'nullable|in:beginner,intermediate,advanced',
            'requirements' => 'nullable|string',
            'objectives' => 'nullable|string',
            'instructor' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'is_online' => 'boolean',
            'is_active' => 'boolean',
            'status' => 'required|in:upcoming,ongoing,completed,cancelled',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'event_id' => 'nullable|exists:events,id'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('programs', 'public');
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle schedule data
        if ($request->filled('schedule_days') || $request->filled('schedule_time')) {
            $validated['schedule'] = [
                'days' => $request->input('schedule_days', []),
                'time' => $request->input('schedule_time'),
                'timezone' => $request->input('schedule_timezone', 'UTC')
            ];
        }

        Program::create($validated);

        return redirect()->route('admin.programs.index')
                        ->with('success', 'Program created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Program $program)
    {
        $program->load('event');
        return view('admin.programs.show', compact('program'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Program $program)
    {
        $events = Event::active()->orderBy('event_date', 'asc')->get();
        return view('admin.programs.edit', compact('program', 'events'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Program $program)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255|unique:programs,title,' . $program->id,
            'slug' => 'nullable|string|max:255|unique:programs,slug,' . $program->id,
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price' => 'nullable|numeric|min:0',
            'duration' => 'nullable|string|max:100',
            'max_participants' => 'nullable|integer|min:1',
            'schedule' => 'nullable|array',
            'level' => 'nullable|in:beginner,intermediate,advanced',
            'requirements' => 'nullable|string',
            'objectives' => 'nullable|string',
            'instructor' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'is_online' => 'boolean',
            'is_active' => 'boolean',
            'status' => 'required|in:upcoming,ongoing,completed,cancelled',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'event_id' => 'nullable|exists:events,id'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($program->image) {
                Storage::disk('public')->delete($program->image);
            }
            $validated['image'] = $request->file('image')->store('programs', 'public');
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle schedule data
        if ($request->filled('schedule_days') || $request->filled('schedule_time')) {
            $validated['schedule'] = [
                'days' => $request->input('schedule_days', []),
                'time' => $request->input('schedule_time'),
                'timezone' => $request->input('schedule_timezone', 'UTC')
            ];
        }

        $program->update($validated);

        return redirect()->route('admin.programs.index')
                        ->with('success', 'Program updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Program $program)
    {
        // Delete image if exists
        if ($program->image) {
            Storage::disk('public')->delete($program->image);
        }

        $program->delete();

        return redirect()->route('admin.programs.index')
                        ->with('success', 'Program deleted successfully.');
    }

    /**
     * Toggle program active status
     */
    public function toggleStatus(Program $program)
    {
        $program->update(['is_active' => !$program->is_active]);
        
        $status = $program->is_active ? 'activated' : 'deactivated';
        return response()->json([
            'success' => true,
            'message' => "Program {$status} successfully.",
            'is_active' => $program->is_active
        ]);
    }

    /**
     * Duplicate a program
     */
    public function duplicate(Program $program)
    {
        $newProgram = $program->replicate();
        $newProgram->title = $program->title . ' (Copy)';
        $newProgram->slug = Str::slug($newProgram->title);
        $newProgram->is_active = false;
        $newProgram->save();

        return redirect()->route('admin.programs.edit', $newProgram)
                        ->with('success', 'Program duplicated successfully. Please review and update the details.');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'programs' => 'required|array',
            'programs.*' => 'exists:programs,id'
        ]);

        $programs = Program::whereIn('id', $request->programs);

        switch ($request->action) {
            case 'activate':
                $programs->update(['is_active' => true]);
                $message = 'Programs activated successfully.';
                break;
            case 'deactivate':
                $programs->update(['is_active' => false]);
                $message = 'Programs deactivated successfully.';
                break;
            case 'delete':
                // Delete images for programs being deleted
                foreach ($programs->get() as $program) {
                    if ($program->image) {
                        Storage::disk('public')->delete($program->image);
                    }
                }
                $programs->delete();
                $message = 'Programs deleted successfully.';
                break;
        }

        return redirect()->route('admin.programs.index')
                        ->with('success', $message);
    }
}