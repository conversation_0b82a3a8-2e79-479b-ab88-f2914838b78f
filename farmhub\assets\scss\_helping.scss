//page scroll bar add
::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}
/* Track */
::-webkit-scrollbar-track {
	box-shadow: inset 0 0 5px $p2-clr;
	border-radius: 5px;
}
/* Handle */
::-webkit-scrollbar-thumb {
	background: $p2-clr;
	border-radius: 10px;
}
//page scroll bar add

//Basic Code Start
.fix {
	overflow: hidden;
}

.ralt {
	position: relative;
}

.ml-100 {
	margin-left: 100px;
}
//Basic Code End

//pagination default

.cust-swiper {
	width: 60px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(42, 185, 57, 0.1);
	color: $p1-clr;
	border-radius: 50%;
	transition: all 0.5s;
	&.active,
	&:hover {
		background-color: $p1-clr;
		color: $white-clr;
	}
	@include breakpoint(max-lg){
		width: 44px;
		height: 44px;
	}
}

//pagination default
.swiper-dot {
	margin-bottom: 2px;
	position: relative;

	&::before {
		position: absolute;
		bottom: 13px;
		left: 37%;
		transform: translate(-50%, -50%);
		width: 105px;
		height: 2px;
		background: linear-gradient(
			90deg,
			#f39f5f 4.85%,
			rgba(255, 255, 255, 0) 96.39%
		);
		content: "";
		transform: rotate(-180deg);

		@include breakpoint(max-xxl) {
			display: none;
		}
	}

	&::after {
		position: absolute;
		bottom: 13px;
		right: 37%;
		width: 105px;
		height: 2px;
		background: linear-gradient(
			90deg,
			#f39f5f 4.85%,
			rgba(255, 255, 255, 0) 96.39%
		);
		content: "";

		@include breakpoint(max-xxl) {
			display: none;
		}
	}
	.swiper-pagination-bullet {
		width: 10px;
		height: 10px;
		transition: 0.6s;
		background-color: $p2-clr;
		opacity: 1;
		border-radius: 10px;
		&:not(:last-child) {
			margin-right: 15px;
		}
	}
	.swiper-pagination-bullet.swiper-pagination-bullet-active {
		background-color: $p2-clr;
		transition: 0.6s;
		position: relative;

		&::before {
			position: absolute;
			width: 30px;
			height: 30px;
			line-height: 30px;
			top: -10px;
			left: -10px;
			border-radius: 50%;
			background-color: transparent;
			border: 2px solid $p2-clr;
			content: "";
		}
	}
}

.dot-cmn {
	.swiper-pagination-bullet {
		width: 10px;
		height: 10px;
		border-radius: 10px;
		background: $p200-clr;
		opacity: 1;
	}
	.swiper-pagination-bullet-active {
		width: 18px;
		height: 18px;
		border: 5px solid $p1-clr;
		background: $white-clr !important;
	}
}

.array-button {
	@include flex;
	gap: 15px;

	.array-prev {
		width: 61px;
		height: 56px;
		line-height: 56px;
		text-align: center;
		background-color: $white-clr;
		color: $p700-clr;
		border-radius: 22px;
		@include transition;

		&:hover {
			background-color: $p2-clr;
			color: $white-clr;
		}
	}

	.array-next {
		width: 61px;
		height: 56px;
		line-height: 56px;
		text-align: center;
		background-color: $p2-clr;
		color: $white-clr;
		border-radius: 22px;
		@include transition;

		&:hover {
			background-color: $white-clr;
			color: $p2-clr;
		}
	}
}

//pagination default

.mt-10 {
	margin-top: 10px;
}

br {
	@include breakpoint(max-md) {
		display: none;
	}
}

.nice-select {
	background-color: transparent;
	border: transparent;
	float: initial;
	overflow: initial;
	height: initial;
	padding: 0;
	display: inline-flex;
	align-items: center;
	line-height: 150%;
	width: 100%;
	border: none;

	&:focus,
	&:hover {
		border-color: transparent;
	}

	&::after {
		height: 8px;
		width: 8px;
		right: -25px;
		top: 15px;
		border-color: $p700-clr;
		border-bottom: 2px solid $p700-clr;
		border-right: 2px solid $p700-clr;
	}

	.list {
		width: initial;
		background-color: $p2-clr;
		box-shadow: none;
		overflow: initial;
		box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 3px 0px;
		width: 100%;
		top: 100%;
		padding: 0;
		max-height: 50vh;
		overflow-x: auto;
		right: -50px;
		&::-webkit-scrollbar {
			width: 2px;
			opacity: 1;
			display: block;
		}
		&::-webkit-scrollbar-button,
		&::-webkit-scrollbar-thumb {
			background: $p700-clr;
		}
	}
	.option {
		background-color: transparent;
		font-size: 16px;
		line-height: 150%;
		padding: 4px 5px;
		min-height: initial;
		font-weight: 500;
		&:hover,
		&:focus,
		&.selected.focus {
			background-color: transparent;
		}
	}
	.current {
		font-weight: 500;
		color: $p700-clr;
	}
}
//>>>>> Nice Select Css End <<<<<//

.scroll-up {
	cursor: pointer;
	display: block;
	border-radius: 50px;
	box-shadow: inset 0 0 0 2px var(--border);
	z-index: 99;
	opacity: 0;
	visibility: hidden;
	transform: translateY(15px);
	position: fixed;
	right: 25px;
	bottom: 35px;
	height: 50px;
	width: 50px;
	@include transition;
}

.scroll-up::after {
	position: absolute;
	font-family: "Font Awesome 6 free";
	content: "\f062";
	text-align: center;
	line-height: 50px;
	font-weight: 700;
	font-size: 18px;
	color: $p2-clr;
	left: 0;
	top: 0;
	height: 50px;
	width: 50px;
	cursor: pointer;
	display: block;
	z-index: 1;
	@include transition;
}

.scroll-up svg path {
	fill: none;
}

.scroll-up svg.scroll-circle path {
	stroke: $p1-clr;
	stroke-width: 4px;
	box-sizing: border-box;
	@include transition;
}

.scroll-up.active-scroll {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
}

.page-nav-wrap {
	ul {
		li {
			display: inline-block;

			.page-numbers {
				&.current {
					background-color: $p2-clr;
					color: $white-clr;
				}

				display: inline-block;
				width: 50px;
				height: 50px;
				line-height: 50px;
				background: transparent;
				font-weight: 600;
				transition: all 0.3s ease-in-out;
				margin: 0 2px;
				border: 1px solid $p1-clr;
				color: $p200-clr;
				border-radius: 50%;

				@media (max-width: 767px) {
					margin-top: 10px;
					width: 50px;
					height: 50px;
					line-height: 50px;
					font-size: 14px;
				}

				i {
					margin-top: 2px;
				}
				&:hover {
					background-color: $p2-clr;
					color: $white-clr;
					border: 1px solid transparent;
				}
			}
		}
	}
}

.box-color-1 {
	background-color: rgba(248, 184, 31, 0.15);
	color: #f8b81f;
}

.box-color-2 {
	background-color: rgba(88, 102, 235, 0.15);
	color: #5866eb;
}

.box-color-3 {
	background-color: rgba(57, 192, 250, 0.15);
	color: #39c0fa;
}

.box-color-4 {
	background-color: rgba(249, 37, 150, 0.15);
	color: #f92596;
}

.border-none {
	border: none !important;
}

.box-shadow {
	box-shadow: $shadow-clr;
}

.bor-1 {
	border: 1px solid $p2-clr;
}

.mb-55 {
	margin-bottom: 55px !important;
}

.border-array-style {
	border: 1px solid $p2-clr;
}

.pt-80 {
	padding-top: 80px;
}

.fz-40 {
	font-size: 40px;
}
