<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Gallery extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'alt_text',
        'type',
        'category',
        'file_path',
        'file_size',
        'mime_type',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'file_size' => 'integer',
        'sort_order' => 'integer',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Scope a query to only include images.
     */
    public function scopeImages($query)
    {
        return $query->where('type', 'image');
    }

    /**
     * Scope a query to only include videos.
     */
    public function scopeVideos($query)
    {
        return $query->where('type', 'video');
    }

    /**
     * Scope a query to only include featured items.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the file URL.
     */
    public function getFileUrlAttribute()
    {
        if ($this->type === 'video') {
            return $this->file_path; // For videos, this is the URL
        }
        
        if ($this->file_path) {
            return asset('storage/' . $this->file_path);
        }
        
        return null;
    }

    /**
     * Get the thumbnail URL for images.
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->type === 'image' && $this->file_path) {
            return asset('storage/' . $this->file_path);
        }
        
        if ($this->type === 'video') {
            // For videos, you might want to generate thumbnails or use a default
            return asset('assets/img/video-thumbnail.jpg');
        }
        
        return asset('assets/img/default-thumbnail.jpg');
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get type badge class.
     */
    public function getTypeBadgeClassAttribute()
    {
        switch ($this->type) {
            case 'image':
                return 'badge-primary';
            case 'video':
                return 'badge-info';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get type label.
     */
    public function getTypeLabelAttribute()
    {
        switch ($this->type) {
            case 'image':
                return 'Image';
            case 'video':
                return 'Video';
            default:
                return 'Unknown';
        }
    }

    /**
     * Check if the item is an image.
     */
    public function getIsImageAttribute()
    {
        return $this->type === 'image';
    }

    /**
     * Check if the item is a video.
     */
    public function getIsVideoAttribute()
    {
        return $this->type === 'video';
    }

    /**
     * Get video embed code for supported platforms.
     */
    public function getVideoEmbedAttribute()
    {
        if ($this->type !== 'video') {
            return null;
        }

        $url = $this->file_path;

        // YouTube
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe width="560" height="315" src="https://www.youtube.com/embed/' . $videoId . '" frameborder="0" allowfullscreen></iframe>';
        }

        // Vimeo
        if (preg_match('/vimeo\.com\/(\d+)/', $url, $matches)) {
            $videoId = $matches[1];
            return '<iframe src="https://player.vimeo.com/video/' . $videoId . '" width="560" height="315" frameborder="0" allowfullscreen></iframe>';
        }

        // For other video URLs, return a simple video tag
        return '<video width="560" height="315" controls><source src="' . $url . '" type="video/mp4">Your browser does not support the video tag.</video>';
    }

    /**
     * Get all unique categories.
     */
    public static function getCategories()
    {
        return self::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->orderBy('category')
            ->pluck('category');
    }
}
