/*=================Scss Indexing=============
1.variables
2.typography
3.spacing
4.reset
5.forms
6.mixins
7.shortcode
8.animations
9.text-animation
10.sal
11.header
12.mobile-menu
13.button
14.nav
15.banner
16.swiper
17.funfacts
18.cta
19.about
20.common
21.service
22.projects
23.working-process
24.blog
25.blog-details
26.footer
27.search-input
28./side-bar
29.team
30.testimonials
31.faq
32.pricing
33.date-picker
34.time-picker
35.appoinment
36.awesome-feedback
37.contact
38.pre-loader.scss
39.back-to-top



==============================================  */
/* Default  */
@import url("https://db.onlinewebfonts.com/c/d95bf2f30035a050142565e03d44da71?family=Aeonik");
:root {
  --color-primary: #0077AD;
  --color-secondary: #1F1F25;
  --color-body: #262626;
  --color-heading-1: #262626;
  --color-white: #fff;
  --color-border: #DDD8F9;
  --border-width: 1px;
  --radius: 10px;
  --color-success: #3EB75E;
  --color-danger: #FF0003;
  --color-warning: #FF8F3C;
  --color-info: #1BA2DB;
  --color-facebook: #3B5997;
  --color-twitter: #1BA1F2;
  --color-youtube: #ED4141;
  --color-linkedin: #0077B5;
  --color-pinterest: #E60022;
  --color-instagram: #C231A1;
  --color-vimeo: #00ADEF;
  --color-twitch: #6441A3;
  --color-discord: #7289da;
  --p-light: 300;
  --p-regular: 400;
  --p-medium: 500;
  --p-semi-bold: 600;
  --p-bold: 700;
  --p-extra-bold: 800;
  --p-black: 900;
  --s-light: 300;
  --s-regular: 400;
  --s-medium: 400;
  --s-semi-bold: 400;
  --s-bold: 400;
  --s-extra-bold: 400;
  --s-black: 400;
  --transition: 0.3s;
  --font-primary: "Aeonik", sans-serif;
  --font-medium: "6500595b785e358dcc2a2f3a_AeonikMedium", sans-serif;
  --font-secondary: "Aeonik", sans-serif;
  --font-3: "fontawesome";
  --font-size-b1: 16px;
  --font-size-b2: 16px;
  --font-size-b3: 22px;
  --line-height-b1: 26px;
  --line-height-b2: 26px;
  --line-height-b3: 1.7;
  --h1: 60px;
  --h2: 48px;
  --h3: 30px;
  --h4: 26px;
  --h5: 24px;
  --h6: 18px;
}

@font-face {
  font-family: "6500595b785e358dcc2a2f3a_AeonikMedium";
  src: url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.eot");
  src: url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.eot") format("embedded-opentype"), url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.woff2") format("woff2"), url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.woff") format("woff"), url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.ttf") format("truetype"), url("../fonts/6500595b785e358dcc2a2f3a_AeonikMedium.svg#6500595b785e358dcc2a2f3a_AeonikMedium") format("svg");
  font-weight: 500;
}
* {
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  font-size: 10px;
  overflow: hidden;
  overflow-y: auto;
  scroll-behavior: auto !important;
}

body {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: "Aeonik", sans-serif;
  color: var(--color-body);
  font-weight: var(--p-regular);
  position: relative;
  overflow-x: hidden;
  margin: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  body {
    overflow: hidden;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  body {
    overflow: hidden;
  }
}
@media only screen and (max-width: 767px) {
  body {
    overflow: hidden;
  }
}
body::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  opacity: 0.05;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
address,
p,
pre,
blockquote,
menu,
ol,
ul,
table,
hr {
  margin: 0;
  margin-bottom: 20px;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  word-break: break-word;
  font-family: "Aeonik", sans-serif;
  line-height: 1.4074;
  color: var(--color-heading-1);
}

h1,
.h1 {
  font-size: var(--h1);
  line-height: 1.3;
  font-weight: 700;
}

h2,
.h2 {
  font-size: var(--h2);
  line-height: 1.1;
}

h3,
.h3 {
  font-size: var(--h3);
  line-height: 1.2;
}

h4,
.h4 {
  font-size: var(--h4);
  line-height: 1.2;
}

h5,
.h5 {
  font-size: var(--h5);
  line-height: 1.2;
}

h6,
.h6 {
  font-size: var(--h6);
  line-height: 1.2;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
.h1 a,
.h2 a,
.h3 a,
.h4 a,
.h5 a,
.h6 a {
  color: inherit;
}

.bg-color-tertiary h1,
.bg-color-tertiary h2,
.bg-color-tertiary h3,
.bg-color-tertiary h4,
.bg-color-tertiary h5,
.bg-color-tertiary h6,
.bg-color-tertiary .h1,
.bg-color-tertiary .h2,
.bg-color-tertiary .h3,
.bg-color-tertiary .h4,
.bg-color-tertiary .h5,
.bg-color-tertiary .h6 {
  color: #fff;
}
.bg-color-tertiary p {
  color: #6c7279;
}
.bg-color-tertiary a {
  color: #6c7279;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  h1,
  .h1 {
    font-size: 38px;
  }
  h2,
  .h2 {
    font-size: 32px;
  }
  h3,
  .h3 {
    font-size: 28px;
  }
  h4,
  .h4 {
    font-size: 24px;
  }
  h5,
  .h5 {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  h1,
  .h1 {
    font-size: 34px;
  }
  h2,
  .h2 {
    font-size: 28px;
  }
  h3,
  .h3 {
    font-size: 24px;
  }
  h4,
  .h4 {
    font-size: 20px;
  }
  h5,
  .h5 {
    font-size: 20px;
  }
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
  font-weight: var(--s-bold);
}

h4,
.h4,
h5,
.h5 {
  font-weight: var(--s-bold);
}

h6,
.h6 {
  font-weight: var(--s-bold);
}

p {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
  font-weight: var(--p-regular);
  color: var(--color-body);
  margin: 0 0 40px;
}
@media only screen and (max-width: 767px) {
  p {
    margin: 0 0 20px;
    font-size: 16px;
    line-height: 28px;
  }
}
p.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}
p.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}
p.has-large-font-size {
  line-height: 1.5;
  font-size: 36px;
}
p.has-medium-font-size {
  font-size: 24px;
  line-height: 36px;
}
p.has-small-font-size {
  font-size: 13px;
}
p.has-very-light-gray-color {
  color: var(--color-white);
}
p.has-background {
  padding: 20px 30px;
}
p.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}
p.b2 {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
}
p.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}
p:last-child {
  margin-bottom: 0;
}

.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}

.b2 {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
}

.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}

.b4 {
  font-size: var(--font-size-b4);
  line-height: var(--line-height-b4);
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0 0 20px;
  width: 100%;
}

table a,
table a:link,
table a:visited {
  text-decoration: none;
}

cite,
.wp-block-pullquote cite,
.wp-block-pullquote.is-style-solid-color blockquote cite,
.wp-block-quote cite {
  color: var(--color-heading);
}

var {
  font-family: "Syne", sans-serif;
}

/*---------------------------
	List Style
---------------------------*/
ul,
ol {
  padding-left: 18px;
}

ul {
  list-style: square;
  margin-bottom: 30px;
  padding-left: 20px;
}
ul.liststyle.bullet li {
  font-size: 18px;
  line-height: 30px;
  color: var(--color-body);
  position: relative;
  padding-left: 30px;
}
@media only screen and (max-width: 767px) {
  ul.liststyle.bullet li {
    padding-left: 19px;
  }
}
ul.liststyle.bullet li::before {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background: var(--color-body);
  left: 0;
  top: 10px;
}
ul.liststyle.bullet li + li {
  margin-top: 8px;
}
ul li {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  margin-top: 10px;
  margin-bottom: 10px;
  color: var(--color-body);
}
ul li a {
  text-decoration: none;
  color: var(--color-gray);
}
ul li a:hover {
  color: var(--color-primary);
}
ul ul {
  margin-bottom: 0;
}

ol {
  margin-bottom: 30px;
}
ol li {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  color: var(--color-body);
  margin-top: 10px;
  margin-bottom: 10px;
}
ol li a {
  color: var(--color-heading);
  text-decoration: none;
}
ol li a:hover {
  color: var(--color-primary);
}
ol ul {
  padding-left: 30px;
}

.typo-title-area .title {
  margin-top: 0;
}

.paragraph-area p.disc {
  margin-bottom: 20px;
  color: #fff;
}

@media only screen and (max-width: 1199px) {
  h1 {
    font-size: 1.3;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h1 {
    font-size: 54px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h1 {
    font-size: 40px;
    line-height: 1.4;
  }
}
@media only screen and (max-width: 767px) {
  h1 {
    font-size: 30px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  h1 {
    font-size: 28px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 479px) {
  h1 {
    font-size: 26px;
    line-height: 1.3;
  }
}

@media only screen and (max-width: 1199px) {
  h2 {
    font-size: 54px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h2 {
    font-size: 44px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h2 {
    font-size: 36px;
    line-height: 1.4;
  }
}
@media only screen and (max-width: 767px) {
  h2 {
    font-size: 32px;
    line-height: 1.4;
  }
}
@media only screen and (max-width: 575px) {
  h2 {
    font-size: 32px;
    line-height: 1.4;
  }
}
@media only screen and (max-width: 479px) {
  h2 {
    font-size: 26px;
    line-height: 1.4;
  }
}

@media only screen and (max-width: 1199px) {
  h3 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h3 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h3 {
    font-size: 30px;
    line-height: 56px;
  }
}
@media only screen and (max-width: 767px) {
  h3 {
    font-size: 30px;
    line-height: 45px;
  }
}
@media only screen and (max-width: 575px) {
  h3 {
    font-size: 24px;
    line-height: 36px;
  }
}
@media only screen and (max-width: 479px) {
  h3 {
    font-size: 22px;
    line-height: 30px;
  }
}

/*=========================
    Section Separation
==========================*/
.mb_dec--25 {
  margin-bottom: -25px;
}

.mb_dec--30 {
  margin-bottom: -30px;
}

.m--0 {
  margin: 0;
}

.rts-section-gap {
  padding: 130px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap {
    padding: 80px 0;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap {
    padding: 60px 0;
  }
}

.rts-section-gapBottom {
  padding-bottom: 130px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gapBottom {
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gapBottom {
    padding-bottom: 60px;
  }
}

.rts-section-gapTop {
  padding-top: 130px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gapTop {
    padding-top: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gapTop {
    padding-top: 60px;
  }
}

.rts-section-gap2 {
  padding: 100px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2 {
    padding: 80px 0;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2 {
    padding: 60px 0;
  }
}

.rts-section-gap2Bottom {
  padding-bottom: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2Bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2Bottom {
    padding-bottom: 60px;
  }
}

.rts-section-gap2Top {
  padding-top: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2Top {
    padding-top: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2Top {
    padding-top: 60px;
  }
}

.rts-section-gap3 {
  padding: 150px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap3 {
    padding: 80px 0;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap3 {
    padding: 60px 0;
  }
}

.rts-section-gap3Bottom {
  padding-bottom: 150px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap3Bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap3Bottom {
    padding-bottom: 60px;
  }
}

.rts-section-gap3Top {
  padding-top: 150px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap3Top {
    padding-top: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap3Top {
    padding-top: 60px;
  }
}

.pl--0 {
  padding-left: 0 !important;
}

.pr--0 {
  padding-right: 0 !important;
}

.pt--0 {
  padding-top: 0 !important;
}

.pb--0 {
  padding-bottom: 0 !important;
}

.mr--0 {
  margin-right: 0 !important;
}

.ml--0 {
  margin-left: 0 !important;
}

.mt--0 {
  margin-top: 0 !important;
}

.mb--0 {
  margin-bottom: 0 !important;
}

.ptb--5 {
  padding: 5px 0 !important;
}

.plr--5 {
  padding: 0 5px !important;
}

.pt--5 {
  padding-top: 5px !important;
}

.pb--5 {
  padding-bottom: 5px !important;
}

.pl--5 {
  padding-left: 5px !important;
}

.pr--5 {
  padding-right: 5px !important;
}

.mt--5 {
  margin-top: 5px !important;
}

.mb--5 {
  margin-bottom: 5px !important;
}

.mr--5 {
  margin-right: 5px !important;
}

.ml--5 {
  margin-left: 5px !important;
}

.ptb--10 {
  padding: 10px 0 !important;
}

.plr--10 {
  padding: 0 10px !important;
}

.pt--10 {
  padding-top: 10px !important;
}

.pb--10 {
  padding-bottom: 10px !important;
}

.pl--10 {
  padding-left: 10px !important;
}

.pr--10 {
  padding-right: 10px !important;
}

.mt--10 {
  margin-top: 10px !important;
}

.mb--10 {
  margin-bottom: 10px !important;
}

.mr--10 {
  margin-right: 10px !important;
}

.ml--10 {
  margin-left: 10px !important;
}

.ptb--15 {
  padding: 15px 0 !important;
}

.plr--15 {
  padding: 0 15px !important;
}

.pt--15 {
  padding-top: 15px !important;
}

.pb--15 {
  padding-bottom: 15px !important;
}

.pl--15 {
  padding-left: 15px !important;
}

.pr--15 {
  padding-right: 15px !important;
}

.mt--15 {
  margin-top: 15px !important;
}

.mb--15 {
  margin-bottom: 15px !important;
}

.mr--15 {
  margin-right: 15px !important;
}

.ml--15 {
  margin-left: 15px !important;
}

.ptb--20 {
  padding: 20px 0 !important;
}

.plr--20 {
  padding: 0 20px !important;
}

.pt--20 {
  padding-top: 20px !important;
}

.pb--20 {
  padding-bottom: 20px !important;
}

.pl--20 {
  padding-left: 20px !important;
}

.pr--20 {
  padding-right: 20px !important;
}

.mt--20 {
  margin-top: 20px !important;
}

.mb--20 {
  margin-bottom: 20px !important;
}

.mr--20 {
  margin-right: 20px !important;
}

.ml--20 {
  margin-left: 20px !important;
}

.ptb--25 {
  padding: 25px 0 !important;
}

.plr--25 {
  padding: 0 25px !important;
}

.pt--25 {
  padding-top: 25px !important;
}

.pb--25 {
  padding-bottom: 25px !important;
}

.pl--25 {
  padding-left: 25px !important;
}

.pr--25 {
  padding-right: 25px !important;
}

.mt--25 {
  margin-top: 25px !important;
}

.mb--25 {
  margin-bottom: 25px !important;
}

.mr--25 {
  margin-right: 25px !important;
}

.ml--25 {
  margin-left: 25px !important;
}

.ptb--30 {
  padding: 30px 0 !important;
}

.plr--30 {
  padding: 0 30px !important;
}

.pt--30 {
  padding-top: 30px !important;
}

.pb--30 {
  padding-bottom: 30px !important;
}

.pl--30 {
  padding-left: 30px !important;
}

.pr--30 {
  padding-right: 30px !important;
}

.mt--30 {
  margin-top: 30px !important;
}

.mb--30 {
  margin-bottom: 30px !important;
}

.mr--30 {
  margin-right: 30px !important;
}

.ml--30 {
  margin-left: 30px !important;
}

.ptb--35 {
  padding: 35px 0 !important;
}

.plr--35 {
  padding: 0 35px !important;
}

.pt--35 {
  padding-top: 35px !important;
}

.pb--35 {
  padding-bottom: 35px !important;
}

.pl--35 {
  padding-left: 35px !important;
}

.pr--35 {
  padding-right: 35px !important;
}

.mt--35 {
  margin-top: 35px !important;
}

.mb--35 {
  margin-bottom: 35px !important;
}

.mr--35 {
  margin-right: 35px !important;
}

.ml--35 {
  margin-left: 35px !important;
}

.ptb--40 {
  padding: 40px 0 !important;
}

.plr--40 {
  padding: 0 40px !important;
}

.pt--40 {
  padding-top: 40px !important;
}

.pb--40 {
  padding-bottom: 40px !important;
}

.pl--40 {
  padding-left: 40px !important;
}

.pr--40 {
  padding-right: 40px !important;
}

.mt--40 {
  margin-top: 40px !important;
}

.mb--40 {
  margin-bottom: 40px !important;
}

.mr--40 {
  margin-right: 40px !important;
}

.ml--40 {
  margin-left: 40px !important;
}

.ptb--45 {
  padding: 45px 0 !important;
}

.plr--45 {
  padding: 0 45px !important;
}

.pt--45 {
  padding-top: 45px !important;
}

.pb--45 {
  padding-bottom: 45px !important;
}

.pl--45 {
  padding-left: 45px !important;
}

.pr--45 {
  padding-right: 45px !important;
}

.mt--45 {
  margin-top: 45px !important;
}

.mb--45 {
  margin-bottom: 45px !important;
}

.mr--45 {
  margin-right: 45px !important;
}

.ml--45 {
  margin-left: 45px !important;
}

.ptb--50 {
  padding: 50px 0 !important;
}

.plr--50 {
  padding: 0 50px !important;
}

.pt--50 {
  padding-top: 50px !important;
}

.pb--50 {
  padding-bottom: 50px !important;
}

.pl--50 {
  padding-left: 50px !important;
}

.pr--50 {
  padding-right: 50px !important;
}

.mt--50 {
  margin-top: 50px !important;
}

.mb--50 {
  margin-bottom: 50px !important;
}

.mr--50 {
  margin-right: 50px !important;
}

.ml--50 {
  margin-left: 50px !important;
}

.ptb--55 {
  padding: 55px 0 !important;
}

.plr--55 {
  padding: 0 55px !important;
}

.pt--55 {
  padding-top: 55px !important;
}

.pb--55 {
  padding-bottom: 55px !important;
}

.pl--55 {
  padding-left: 55px !important;
}

.pr--55 {
  padding-right: 55px !important;
}

.mt--55 {
  margin-top: 55px !important;
}

.mb--55 {
  margin-bottom: 55px !important;
}

.mr--55 {
  margin-right: 55px !important;
}

.ml--55 {
  margin-left: 55px !important;
}

.ptb--60 {
  padding: 60px 0 !important;
}

.plr--60 {
  padding: 0 60px !important;
}

.pt--60 {
  padding-top: 60px !important;
}

.pb--60 {
  padding-bottom: 60px !important;
}

.pl--60 {
  padding-left: 60px !important;
}

.pr--60 {
  padding-right: 60px !important;
}

.mt--60 {
  margin-top: 60px !important;
}

.mb--60 {
  margin-bottom: 60px !important;
}

.mr--60 {
  margin-right: 60px !important;
}

.ml--60 {
  margin-left: 60px !important;
}

.ptb--65 {
  padding: 65px 0 !important;
}

.plr--65 {
  padding: 0 65px !important;
}

.pt--65 {
  padding-top: 65px !important;
}

.pb--65 {
  padding-bottom: 65px !important;
}

.pl--65 {
  padding-left: 65px !important;
}

.pr--65 {
  padding-right: 65px !important;
}

.mt--65 {
  margin-top: 65px !important;
}

.mb--65 {
  margin-bottom: 65px !important;
}

.mr--65 {
  margin-right: 65px !important;
}

.ml--65 {
  margin-left: 65px !important;
}

.ptb--70 {
  padding: 70px 0 !important;
}

.plr--70 {
  padding: 0 70px !important;
}

.pt--70 {
  padding-top: 70px !important;
}

.pb--70 {
  padding-bottom: 70px !important;
}

.pl--70 {
  padding-left: 70px !important;
}

.pr--70 {
  padding-right: 70px !important;
}

.mt--70 {
  margin-top: 70px !important;
}

.mb--70 {
  margin-bottom: 70px !important;
}

.mr--70 {
  margin-right: 70px !important;
}

.ml--70 {
  margin-left: 70px !important;
}

.ptb--75 {
  padding: 75px 0 !important;
}

.plr--75 {
  padding: 0 75px !important;
}

.pt--75 {
  padding-top: 75px !important;
}

.pb--75 {
  padding-bottom: 75px !important;
}

.pl--75 {
  padding-left: 75px !important;
}

.pr--75 {
  padding-right: 75px !important;
}

.mt--75 {
  margin-top: 75px !important;
}

.mb--75 {
  margin-bottom: 75px !important;
}

.mr--75 {
  margin-right: 75px !important;
}

.ml--75 {
  margin-left: 75px !important;
}

.ptb--80 {
  padding: 80px 0 !important;
}

.plr--80 {
  padding: 0 80px !important;
}

.pt--80 {
  padding-top: 80px !important;
}

.pb--80 {
  padding-bottom: 80px !important;
}

.pl--80 {
  padding-left: 80px !important;
}

.pr--80 {
  padding-right: 80px !important;
}

.mt--80 {
  margin-top: 80px !important;
}

.mb--80 {
  margin-bottom: 80px !important;
}

.mr--80 {
  margin-right: 80px !important;
}

.ml--80 {
  margin-left: 80px !important;
}

.ptb--85 {
  padding: 85px 0 !important;
}

.plr--85 {
  padding: 0 85px !important;
}

.pt--85 {
  padding-top: 85px !important;
}

.pb--85 {
  padding-bottom: 85px !important;
}

.pl--85 {
  padding-left: 85px !important;
}

.pr--85 {
  padding-right: 85px !important;
}

.mt--85 {
  margin-top: 85px !important;
}

.mb--85 {
  margin-bottom: 85px !important;
}

.mr--85 {
  margin-right: 85px !important;
}

.ml--85 {
  margin-left: 85px !important;
}

.ptb--90 {
  padding: 90px 0 !important;
}

.plr--90 {
  padding: 0 90px !important;
}

.pt--90 {
  padding-top: 90px !important;
}

.pb--90 {
  padding-bottom: 90px !important;
}

.pl--90 {
  padding-left: 90px !important;
}

.pr--90 {
  padding-right: 90px !important;
}

.mt--90 {
  margin-top: 90px !important;
}

.mb--90 {
  margin-bottom: 90px !important;
}

.mr--90 {
  margin-right: 90px !important;
}

.ml--90 {
  margin-left: 90px !important;
}

.ptb--95 {
  padding: 95px 0 !important;
}

.plr--95 {
  padding: 0 95px !important;
}

.pt--95 {
  padding-top: 95px !important;
}

.pb--95 {
  padding-bottom: 95px !important;
}

.pl--95 {
  padding-left: 95px !important;
}

.pr--95 {
  padding-right: 95px !important;
}

.mt--95 {
  margin-top: 95px !important;
}

.mb--95 {
  margin-bottom: 95px !important;
}

.mr--95 {
  margin-right: 95px !important;
}

.ml--95 {
  margin-left: 95px !important;
}

.ptb--100 {
  padding: 100px 0 !important;
}

.plr--100 {
  padding: 0 100px !important;
}

.pt--100 {
  padding-top: 100px !important;
}

.pb--100 {
  padding-bottom: 100px !important;
}

.pl--100 {
  padding-left: 100px !important;
}

.pr--100 {
  padding-right: 100px !important;
}

.mt--100 {
  margin-top: 100px !important;
}

.mb--100 {
  margin-bottom: 100px !important;
}

.mr--100 {
  margin-right: 100px !important;
}

.ml--100 {
  margin-left: 100px !important;
}

.ptb--105 {
  padding: 105px 0 !important;
}

.plr--105 {
  padding: 0 105px !important;
}

.pt--105 {
  padding-top: 105px !important;
}

.pb--105 {
  padding-bottom: 105px !important;
}

.pl--105 {
  padding-left: 105px !important;
}

.pr--105 {
  padding-right: 105px !important;
}

.mt--105 {
  margin-top: 105px !important;
}

.mb--105 {
  margin-bottom: 105px !important;
}

.mr--105 {
  margin-right: 105px !important;
}

.ml--105 {
  margin-left: 105px !important;
}

.ptb--110 {
  padding: 110px 0 !important;
}

.plr--110 {
  padding: 0 110px !important;
}

.pt--110 {
  padding-top: 110px !important;
}

.pb--110 {
  padding-bottom: 110px !important;
}

.pl--110 {
  padding-left: 110px !important;
}

.pr--110 {
  padding-right: 110px !important;
}

.mt--110 {
  margin-top: 110px !important;
}

.mb--110 {
  margin-bottom: 110px !important;
}

.mr--110 {
  margin-right: 110px !important;
}

.ml--110 {
  margin-left: 110px !important;
}

.ptb--115 {
  padding: 115px 0 !important;
}

.plr--115 {
  padding: 0 115px !important;
}

.pt--115 {
  padding-top: 115px !important;
}

.pb--115 {
  padding-bottom: 115px !important;
}

.pl--115 {
  padding-left: 115px !important;
}

.pr--115 {
  padding-right: 115px !important;
}

.mt--115 {
  margin-top: 115px !important;
}

.mb--115 {
  margin-bottom: 115px !important;
}

.mr--115 {
  margin-right: 115px !important;
}

.ml--115 {
  margin-left: 115px !important;
}

.ptb--120 {
  padding: 120px 0 !important;
}

.plr--120 {
  padding: 0 120px !important;
}

.pt--120 {
  padding-top: 120px !important;
}

.pb--120 {
  padding-bottom: 120px !important;
}

.pl--120 {
  padding-left: 120px !important;
}

.pr--120 {
  padding-right: 120px !important;
}

.mt--120 {
  margin-top: 120px !important;
}

.mb--120 {
  margin-bottom: 120px !important;
}

.mr--120 {
  margin-right: 120px !important;
}

.ml--120 {
  margin-left: 120px !important;
}

.ptb--125 {
  padding: 125px 0 !important;
}

.plr--125 {
  padding: 0 125px !important;
}

.pt--125 {
  padding-top: 125px !important;
}

.pb--125 {
  padding-bottom: 125px !important;
}

.pl--125 {
  padding-left: 125px !important;
}

.pr--125 {
  padding-right: 125px !important;
}

.mt--125 {
  margin-top: 125px !important;
}

.mb--125 {
  margin-bottom: 125px !important;
}

.mr--125 {
  margin-right: 125px !important;
}

.ml--125 {
  margin-left: 125px !important;
}

.ptb--130 {
  padding: 130px 0 !important;
}

.plr--130 {
  padding: 0 130px !important;
}

.pt--130 {
  padding-top: 130px !important;
}

.pb--130 {
  padding-bottom: 130px !important;
}

.pl--130 {
  padding-left: 130px !important;
}

.pr--130 {
  padding-right: 130px !important;
}

.mt--130 {
  margin-top: 130px !important;
}

.mb--130 {
  margin-bottom: 130px !important;
}

.mr--130 {
  margin-right: 130px !important;
}

.ml--130 {
  margin-left: 130px !important;
}

.ptb--135 {
  padding: 135px 0 !important;
}

.plr--135 {
  padding: 0 135px !important;
}

.pt--135 {
  padding-top: 135px !important;
}

.pb--135 {
  padding-bottom: 135px !important;
}

.pl--135 {
  padding-left: 135px !important;
}

.pr--135 {
  padding-right: 135px !important;
}

.mt--135 {
  margin-top: 135px !important;
}

.mb--135 {
  margin-bottom: 135px !important;
}

.mr--135 {
  margin-right: 135px !important;
}

.ml--135 {
  margin-left: 135px !important;
}

.ptb--140 {
  padding: 140px 0 !important;
}

.plr--140 {
  padding: 0 140px !important;
}

.pt--140 {
  padding-top: 140px !important;
}

.pb--140 {
  padding-bottom: 140px !important;
}

.pl--140 {
  padding-left: 140px !important;
}

.pr--140 {
  padding-right: 140px !important;
}

.mt--140 {
  margin-top: 140px !important;
}

.mb--140 {
  margin-bottom: 140px !important;
}

.mr--140 {
  margin-right: 140px !important;
}

.ml--140 {
  margin-left: 140px !important;
}

.ptb--145 {
  padding: 145px 0 !important;
}

.plr--145 {
  padding: 0 145px !important;
}

.pt--145 {
  padding-top: 145px !important;
}

.pb--145 {
  padding-bottom: 145px !important;
}

.pl--145 {
  padding-left: 145px !important;
}

.pr--145 {
  padding-right: 145px !important;
}

.mt--145 {
  margin-top: 145px !important;
}

.mb--145 {
  margin-bottom: 145px !important;
}

.mr--145 {
  margin-right: 145px !important;
}

.ml--145 {
  margin-left: 145px !important;
}

.ptb--150 {
  padding: 150px 0 !important;
}

.plr--150 {
  padding: 0 150px !important;
}

.pt--150 {
  padding-top: 150px !important;
}

.pb--150 {
  padding-bottom: 150px !important;
}

.pl--150 {
  padding-left: 150px !important;
}

.pr--150 {
  padding-right: 150px !important;
}

.mt--150 {
  margin-top: 150px !important;
}

.mb--150 {
  margin-bottom: 150px !important;
}

.mr--150 {
  margin-right: 150px !important;
}

.ml--150 {
  margin-left: 150px !important;
}

.ptb--155 {
  padding: 155px 0 !important;
}

.plr--155 {
  padding: 0 155px !important;
}

.pt--155 {
  padding-top: 155px !important;
}

.pb--155 {
  padding-bottom: 155px !important;
}

.pl--155 {
  padding-left: 155px !important;
}

.pr--155 {
  padding-right: 155px !important;
}

.mt--155 {
  margin-top: 155px !important;
}

.mb--155 {
  margin-bottom: 155px !important;
}

.mr--155 {
  margin-right: 155px !important;
}

.ml--155 {
  margin-left: 155px !important;
}

.ptb--160 {
  padding: 160px 0 !important;
}

.plr--160 {
  padding: 0 160px !important;
}

.pt--160 {
  padding-top: 160px !important;
}

.pb--160 {
  padding-bottom: 160px !important;
}

.pl--160 {
  padding-left: 160px !important;
}

.pr--160 {
  padding-right: 160px !important;
}

.mt--160 {
  margin-top: 160px !important;
}

.mb--160 {
  margin-bottom: 160px !important;
}

.mr--160 {
  margin-right: 160px !important;
}

.ml--160 {
  margin-left: 160px !important;
}

.ptb--165 {
  padding: 165px 0 !important;
}

.plr--165 {
  padding: 0 165px !important;
}

.pt--165 {
  padding-top: 165px !important;
}

.pb--165 {
  padding-bottom: 165px !important;
}

.pl--165 {
  padding-left: 165px !important;
}

.pr--165 {
  padding-right: 165px !important;
}

.mt--165 {
  margin-top: 165px !important;
}

.mb--165 {
  margin-bottom: 165px !important;
}

.mr--165 {
  margin-right: 165px !important;
}

.ml--165 {
  margin-left: 165px !important;
}

.ptb--170 {
  padding: 170px 0 !important;
}

.plr--170 {
  padding: 0 170px !important;
}

.pt--170 {
  padding-top: 170px !important;
}

.pb--170 {
  padding-bottom: 170px !important;
}

.pl--170 {
  padding-left: 170px !important;
}

.pr--170 {
  padding-right: 170px !important;
}

.mt--170 {
  margin-top: 170px !important;
}

.mb--170 {
  margin-bottom: 170px !important;
}

.mr--170 {
  margin-right: 170px !important;
}

.ml--170 {
  margin-left: 170px !important;
}

.ptb--175 {
  padding: 175px 0 !important;
}

.plr--175 {
  padding: 0 175px !important;
}

.pt--175 {
  padding-top: 175px !important;
}

.pb--175 {
  padding-bottom: 175px !important;
}

.pl--175 {
  padding-left: 175px !important;
}

.pr--175 {
  padding-right: 175px !important;
}

.mt--175 {
  margin-top: 175px !important;
}

.mb--175 {
  margin-bottom: 175px !important;
}

.mr--175 {
  margin-right: 175px !important;
}

.ml--175 {
  margin-left: 175px !important;
}

.ptb--180 {
  padding: 180px 0 !important;
}

.plr--180 {
  padding: 0 180px !important;
}

.pt--180 {
  padding-top: 180px !important;
}

.pb--180 {
  padding-bottom: 180px !important;
}

.pl--180 {
  padding-left: 180px !important;
}

.pr--180 {
  padding-right: 180px !important;
}

.mt--180 {
  margin-top: 180px !important;
}

.mb--180 {
  margin-bottom: 180px !important;
}

.mr--180 {
  margin-right: 180px !important;
}

.ml--180 {
  margin-left: 180px !important;
}

.ptb--185 {
  padding: 185px 0 !important;
}

.plr--185 {
  padding: 0 185px !important;
}

.pt--185 {
  padding-top: 185px !important;
}

.pb--185 {
  padding-bottom: 185px !important;
}

.pl--185 {
  padding-left: 185px !important;
}

.pr--185 {
  padding-right: 185px !important;
}

.mt--185 {
  margin-top: 185px !important;
}

.mb--185 {
  margin-bottom: 185px !important;
}

.mr--185 {
  margin-right: 185px !important;
}

.ml--185 {
  margin-left: 185px !important;
}

.ptb--190 {
  padding: 190px 0 !important;
}

.plr--190 {
  padding: 0 190px !important;
}

.pt--190 {
  padding-top: 190px !important;
}

.pb--190 {
  padding-bottom: 190px !important;
}

.pl--190 {
  padding-left: 190px !important;
}

.pr--190 {
  padding-right: 190px !important;
}

.mt--190 {
  margin-top: 190px !important;
}

.mb--190 {
  margin-bottom: 190px !important;
}

.mr--190 {
  margin-right: 190px !important;
}

.ml--190 {
  margin-left: 190px !important;
}

.ptb--195 {
  padding: 195px 0 !important;
}

.plr--195 {
  padding: 0 195px !important;
}

.pt--195 {
  padding-top: 195px !important;
}

.pb--195 {
  padding-bottom: 195px !important;
}

.pl--195 {
  padding-left: 195px !important;
}

.pr--195 {
  padding-right: 195px !important;
}

.mt--195 {
  margin-top: 195px !important;
}

.mb--195 {
  margin-bottom: 195px !important;
}

.mr--195 {
  margin-right: 195px !important;
}

.ml--195 {
  margin-left: 195px !important;
}

.ptb--200 {
  padding: 200px 0 !important;
}

.plr--200 {
  padding: 0 200px !important;
}

.pt--200 {
  padding-top: 200px !important;
}

.pb--200 {
  padding-bottom: 200px !important;
}

.pl--200 {
  padding-left: 200px !important;
}

.pr--200 {
  padding-right: 200px !important;
}

.mt--200 {
  margin-top: 200px !important;
}

.mb--200 {
  margin-bottom: 200px !important;
}

.mr--200 {
  margin-right: 200px !important;
}

.ml--200 {
  margin-left: 200px !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .ptb_lp--5 {
    padding: 5px 0;
  }
  .plr_lp--5 {
    padding: 0 5px;
  }
  .pt_lp--5 {
    padding-top: 5px;
  }
  .pb_lp--5 {
    padding-bottom: 5px;
  }
  .pl_lp--5 {
    padding-left: 5px;
  }
  .pr_lp--5 {
    padding-right: 5px;
  }
  .mt_lp--5 {
    margin-top: 5px;
  }
  .mb_lp--5 {
    margin-bottom: 5px;
  }
  .ptb_lp--10 {
    padding: 10px 0;
  }
  .plr_lp--10 {
    padding: 0 10px;
  }
  .pt_lp--10 {
    padding-top: 10px;
  }
  .pb_lp--10 {
    padding-bottom: 10px;
  }
  .pl_lp--10 {
    padding-left: 10px;
  }
  .pr_lp--10 {
    padding-right: 10px;
  }
  .mt_lp--10 {
    margin-top: 10px;
  }
  .mb_lp--10 {
    margin-bottom: 10px;
  }
  .ptb_lp--15 {
    padding: 15px 0;
  }
  .plr_lp--15 {
    padding: 0 15px;
  }
  .pt_lp--15 {
    padding-top: 15px;
  }
  .pb_lp--15 {
    padding-bottom: 15px;
  }
  .pl_lp--15 {
    padding-left: 15px;
  }
  .pr_lp--15 {
    padding-right: 15px;
  }
  .mt_lp--15 {
    margin-top: 15px;
  }
  .mb_lp--15 {
    margin-bottom: 15px;
  }
  .ptb_lp--20 {
    padding: 20px 0;
  }
  .plr_lp--20 {
    padding: 0 20px;
  }
  .pt_lp--20 {
    padding-top: 20px;
  }
  .pb_lp--20 {
    padding-bottom: 20px;
  }
  .pl_lp--20 {
    padding-left: 20px;
  }
  .pr_lp--20 {
    padding-right: 20px;
  }
  .mt_lp--20 {
    margin-top: 20px;
  }
  .mb_lp--20 {
    margin-bottom: 20px;
  }
  .ptb_lp--25 {
    padding: 25px 0;
  }
  .plr_lp--25 {
    padding: 0 25px;
  }
  .pt_lp--25 {
    padding-top: 25px;
  }
  .pb_lp--25 {
    padding-bottom: 25px;
  }
  .pl_lp--25 {
    padding-left: 25px;
  }
  .pr_lp--25 {
    padding-right: 25px;
  }
  .mt_lp--25 {
    margin-top: 25px;
  }
  .mb_lp--25 {
    margin-bottom: 25px;
  }
  .ptb_lp--30 {
    padding: 30px 0;
  }
  .plr_lp--30 {
    padding: 0 30px;
  }
  .pt_lp--30 {
    padding-top: 30px;
  }
  .pb_lp--30 {
    padding-bottom: 30px;
  }
  .pl_lp--30 {
    padding-left: 30px;
  }
  .pr_lp--30 {
    padding-right: 30px;
  }
  .mt_lp--30 {
    margin-top: 30px;
  }
  .mb_lp--30 {
    margin-bottom: 30px;
  }
  .ptb_lp--35 {
    padding: 35px 0;
  }
  .plr_lp--35 {
    padding: 0 35px;
  }
  .pt_lp--35 {
    padding-top: 35px;
  }
  .pb_lp--35 {
    padding-bottom: 35px;
  }
  .pl_lp--35 {
    padding-left: 35px;
  }
  .pr_lp--35 {
    padding-right: 35px;
  }
  .mt_lp--35 {
    margin-top: 35px;
  }
  .mb_lp--35 {
    margin-bottom: 35px;
  }
  .ptb_lp--40 {
    padding: 40px 0;
  }
  .plr_lp--40 {
    padding: 0 40px;
  }
  .pt_lp--40 {
    padding-top: 40px;
  }
  .pb_lp--40 {
    padding-bottom: 40px;
  }
  .pl_lp--40 {
    padding-left: 40px;
  }
  .pr_lp--40 {
    padding-right: 40px;
  }
  .mt_lp--40 {
    margin-top: 40px;
  }
  .mb_lp--40 {
    margin-bottom: 40px;
  }
  .ptb_lp--45 {
    padding: 45px 0;
  }
  .plr_lp--45 {
    padding: 0 45px;
  }
  .pt_lp--45 {
    padding-top: 45px;
  }
  .pb_lp--45 {
    padding-bottom: 45px;
  }
  .pl_lp--45 {
    padding-left: 45px;
  }
  .pr_lp--45 {
    padding-right: 45px;
  }
  .mt_lp--45 {
    margin-top: 45px;
  }
  .mb_lp--45 {
    margin-bottom: 45px;
  }
  .ptb_lp--50 {
    padding: 50px 0;
  }
  .plr_lp--50 {
    padding: 0 50px;
  }
  .pt_lp--50 {
    padding-top: 50px;
  }
  .pb_lp--50 {
    padding-bottom: 50px;
  }
  .pl_lp--50 {
    padding-left: 50px;
  }
  .pr_lp--50 {
    padding-right: 50px;
  }
  .mt_lp--50 {
    margin-top: 50px;
  }
  .mb_lp--50 {
    margin-bottom: 50px;
  }
  .ptb_lp--55 {
    padding: 55px 0;
  }
  .plr_lp--55 {
    padding: 0 55px;
  }
  .pt_lp--55 {
    padding-top: 55px;
  }
  .pb_lp--55 {
    padding-bottom: 55px;
  }
  .pl_lp--55 {
    padding-left: 55px;
  }
  .pr_lp--55 {
    padding-right: 55px;
  }
  .mt_lp--55 {
    margin-top: 55px;
  }
  .mb_lp--55 {
    margin-bottom: 55px;
  }
  .ptb_lp--60 {
    padding: 60px 0;
  }
  .plr_lp--60 {
    padding: 0 60px;
  }
  .pt_lp--60 {
    padding-top: 60px;
  }
  .pb_lp--60 {
    padding-bottom: 60px;
  }
  .pl_lp--60 {
    padding-left: 60px;
  }
  .pr_lp--60 {
    padding-right: 60px;
  }
  .mt_lp--60 {
    margin-top: 60px;
  }
  .mb_lp--60 {
    margin-bottom: 60px;
  }
  .ptb_lp--65 {
    padding: 65px 0;
  }
  .plr_lp--65 {
    padding: 0 65px;
  }
  .pt_lp--65 {
    padding-top: 65px;
  }
  .pb_lp--65 {
    padding-bottom: 65px;
  }
  .pl_lp--65 {
    padding-left: 65px;
  }
  .pr_lp--65 {
    padding-right: 65px;
  }
  .mt_lp--65 {
    margin-top: 65px;
  }
  .mb_lp--65 {
    margin-bottom: 65px;
  }
  .ptb_lp--70 {
    padding: 70px 0;
  }
  .plr_lp--70 {
    padding: 0 70px;
  }
  .pt_lp--70 {
    padding-top: 70px;
  }
  .pb_lp--70 {
    padding-bottom: 70px;
  }
  .pl_lp--70 {
    padding-left: 70px;
  }
  .pr_lp--70 {
    padding-right: 70px;
  }
  .mt_lp--70 {
    margin-top: 70px;
  }
  .mb_lp--70 {
    margin-bottom: 70px;
  }
  .ptb_lp--75 {
    padding: 75px 0;
  }
  .plr_lp--75 {
    padding: 0 75px;
  }
  .pt_lp--75 {
    padding-top: 75px;
  }
  .pb_lp--75 {
    padding-bottom: 75px;
  }
  .pl_lp--75 {
    padding-left: 75px;
  }
  .pr_lp--75 {
    padding-right: 75px;
  }
  .mt_lp--75 {
    margin-top: 75px;
  }
  .mb_lp--75 {
    margin-bottom: 75px;
  }
  .ptb_lp--80 {
    padding: 80px 0;
  }
  .plr_lp--80 {
    padding: 0 80px;
  }
  .pt_lp--80 {
    padding-top: 80px;
  }
  .pb_lp--80 {
    padding-bottom: 80px;
  }
  .pl_lp--80 {
    padding-left: 80px;
  }
  .pr_lp--80 {
    padding-right: 80px;
  }
  .mt_lp--80 {
    margin-top: 80px;
  }
  .mb_lp--80 {
    margin-bottom: 80px;
  }
  .ptb_lp--85 {
    padding: 85px 0;
  }
  .plr_lp--85 {
    padding: 0 85px;
  }
  .pt_lp--85 {
    padding-top: 85px;
  }
  .pb_lp--85 {
    padding-bottom: 85px;
  }
  .pl_lp--85 {
    padding-left: 85px;
  }
  .pr_lp--85 {
    padding-right: 85px;
  }
  .mt_lp--85 {
    margin-top: 85px;
  }
  .mb_lp--85 {
    margin-bottom: 85px;
  }
  .ptb_lp--90 {
    padding: 90px 0;
  }
  .plr_lp--90 {
    padding: 0 90px;
  }
  .pt_lp--90 {
    padding-top: 90px;
  }
  .pb_lp--90 {
    padding-bottom: 90px;
  }
  .pl_lp--90 {
    padding-left: 90px;
  }
  .pr_lp--90 {
    padding-right: 90px;
  }
  .mt_lp--90 {
    margin-top: 90px;
  }
  .mb_lp--90 {
    margin-bottom: 90px;
  }
  .ptb_lp--95 {
    padding: 95px 0;
  }
  .plr_lp--95 {
    padding: 0 95px;
  }
  .pt_lp--95 {
    padding-top: 95px;
  }
  .pb_lp--95 {
    padding-bottom: 95px;
  }
  .pl_lp--95 {
    padding-left: 95px;
  }
  .pr_lp--95 {
    padding-right: 95px;
  }
  .mt_lp--95 {
    margin-top: 95px;
  }
  .mb_lp--95 {
    margin-bottom: 95px;
  }
  .ptb_lp--100 {
    padding: 100px 0;
  }
  .plr_lp--100 {
    padding: 0 100px;
  }
  .pt_lp--100 {
    padding-top: 100px;
  }
  .pb_lp--100 {
    padding-bottom: 100px;
  }
  .pl_lp--100 {
    padding-left: 100px;
  }
  .pr_lp--100 {
    padding-right: 100px;
  }
  .mt_lp--100 {
    margin-top: 100px;
  }
  .mb_lp--100 {
    margin-bottom: 100px;
  }
  .ptb_lp--105 {
    padding: 105px 0;
  }
  .plr_lp--105 {
    padding: 0 105px;
  }
  .pt_lp--105 {
    padding-top: 105px;
  }
  .pb_lp--105 {
    padding-bottom: 105px;
  }
  .pl_lp--105 {
    padding-left: 105px;
  }
  .pr_lp--105 {
    padding-right: 105px;
  }
  .mt_lp--105 {
    margin-top: 105px;
  }
  .mb_lp--105 {
    margin-bottom: 105px;
  }
  .ptb_lp--110 {
    padding: 110px 0;
  }
  .plr_lp--110 {
    padding: 0 110px;
  }
  .pt_lp--110 {
    padding-top: 110px;
  }
  .pb_lp--110 {
    padding-bottom: 110px;
  }
  .pl_lp--110 {
    padding-left: 110px;
  }
  .pr_lp--110 {
    padding-right: 110px;
  }
  .mt_lp--110 {
    margin-top: 110px;
  }
  .mb_lp--110 {
    margin-bottom: 110px;
  }
  .ptb_lp--115 {
    padding: 115px 0;
  }
  .plr_lp--115 {
    padding: 0 115px;
  }
  .pt_lp--115 {
    padding-top: 115px;
  }
  .pb_lp--115 {
    padding-bottom: 115px;
  }
  .pl_lp--115 {
    padding-left: 115px;
  }
  .pr_lp--115 {
    padding-right: 115px;
  }
  .mt_lp--115 {
    margin-top: 115px;
  }
  .mb_lp--115 {
    margin-bottom: 115px;
  }
  .ptb_lp--120 {
    padding: 120px 0;
  }
  .plr_lp--120 {
    padding: 0 120px;
  }
  .pt_lp--120 {
    padding-top: 120px;
  }
  .pb_lp--120 {
    padding-bottom: 120px;
  }
  .pl_lp--120 {
    padding-left: 120px;
  }
  .pr_lp--120 {
    padding-right: 120px;
  }
  .mt_lp--120 {
    margin-top: 120px;
  }
  .mb_lp--120 {
    margin-bottom: 120px;
  }
  .ptb_lp--125 {
    padding: 125px 0;
  }
  .plr_lp--125 {
    padding: 0 125px;
  }
  .pt_lp--125 {
    padding-top: 125px;
  }
  .pb_lp--125 {
    padding-bottom: 125px;
  }
  .pl_lp--125 {
    padding-left: 125px;
  }
  .pr_lp--125 {
    padding-right: 125px;
  }
  .mt_lp--125 {
    margin-top: 125px;
  }
  .mb_lp--125 {
    margin-bottom: 125px;
  }
  .ptb_lp--130 {
    padding: 130px 0;
  }
  .plr_lp--130 {
    padding: 0 130px;
  }
  .pt_lp--130 {
    padding-top: 130px;
  }
  .pb_lp--130 {
    padding-bottom: 130px;
  }
  .pl_lp--130 {
    padding-left: 130px;
  }
  .pr_lp--130 {
    padding-right: 130px;
  }
  .mt_lp--130 {
    margin-top: 130px;
  }
  .mb_lp--130 {
    margin-bottom: 130px;
  }
  .ptb_lp--135 {
    padding: 135px 0;
  }
  .plr_lp--135 {
    padding: 0 135px;
  }
  .pt_lp--135 {
    padding-top: 135px;
  }
  .pb_lp--135 {
    padding-bottom: 135px;
  }
  .pl_lp--135 {
    padding-left: 135px;
  }
  .pr_lp--135 {
    padding-right: 135px;
  }
  .mt_lp--135 {
    margin-top: 135px;
  }
  .mb_lp--135 {
    margin-bottom: 135px;
  }
  .ptb_lp--140 {
    padding: 140px 0;
  }
  .plr_lp--140 {
    padding: 0 140px;
  }
  .pt_lp--140 {
    padding-top: 140px;
  }
  .pb_lp--140 {
    padding-bottom: 140px;
  }
  .pl_lp--140 {
    padding-left: 140px;
  }
  .pr_lp--140 {
    padding-right: 140px;
  }
  .mt_lp--140 {
    margin-top: 140px;
  }
  .mb_lp--140 {
    margin-bottom: 140px;
  }
  .ptb_lp--145 {
    padding: 145px 0;
  }
  .plr_lp--145 {
    padding: 0 145px;
  }
  .pt_lp--145 {
    padding-top: 145px;
  }
  .pb_lp--145 {
    padding-bottom: 145px;
  }
  .pl_lp--145 {
    padding-left: 145px;
  }
  .pr_lp--145 {
    padding-right: 145px;
  }
  .mt_lp--145 {
    margin-top: 145px;
  }
  .mb_lp--145 {
    margin-bottom: 145px;
  }
  .ptb_lp--150 {
    padding: 150px 0;
  }
  .plr_lp--150 {
    padding: 0 150px;
  }
  .pt_lp--150 {
    padding-top: 150px;
  }
  .pb_lp--150 {
    padding-bottom: 150px;
  }
  .pl_lp--150 {
    padding-left: 150px;
  }
  .pr_lp--150 {
    padding-right: 150px;
  }
  .mt_lp--150 {
    margin-top: 150px;
  }
  .mb_lp--150 {
    margin-bottom: 150px;
  }
  .ptb_lp--155 {
    padding: 155px 0;
  }
  .plr_lp--155 {
    padding: 0 155px;
  }
  .pt_lp--155 {
    padding-top: 155px;
  }
  .pb_lp--155 {
    padding-bottom: 155px;
  }
  .pl_lp--155 {
    padding-left: 155px;
  }
  .pr_lp--155 {
    padding-right: 155px;
  }
  .mt_lp--155 {
    margin-top: 155px;
  }
  .mb_lp--155 {
    margin-bottom: 155px;
  }
  .ptb_lp--160 {
    padding: 160px 0;
  }
  .plr_lp--160 {
    padding: 0 160px;
  }
  .pt_lp--160 {
    padding-top: 160px;
  }
  .pb_lp--160 {
    padding-bottom: 160px;
  }
  .pl_lp--160 {
    padding-left: 160px;
  }
  .pr_lp--160 {
    padding-right: 160px;
  }
  .mt_lp--160 {
    margin-top: 160px;
  }
  .mb_lp--160 {
    margin-bottom: 160px;
  }
  .ptb_lp--165 {
    padding: 165px 0;
  }
  .plr_lp--165 {
    padding: 0 165px;
  }
  .pt_lp--165 {
    padding-top: 165px;
  }
  .pb_lp--165 {
    padding-bottom: 165px;
  }
  .pl_lp--165 {
    padding-left: 165px;
  }
  .pr_lp--165 {
    padding-right: 165px;
  }
  .mt_lp--165 {
    margin-top: 165px;
  }
  .mb_lp--165 {
    margin-bottom: 165px;
  }
  .ptb_lp--170 {
    padding: 170px 0;
  }
  .plr_lp--170 {
    padding: 0 170px;
  }
  .pt_lp--170 {
    padding-top: 170px;
  }
  .pb_lp--170 {
    padding-bottom: 170px;
  }
  .pl_lp--170 {
    padding-left: 170px;
  }
  .pr_lp--170 {
    padding-right: 170px;
  }
  .mt_lp--170 {
    margin-top: 170px;
  }
  .mb_lp--170 {
    margin-bottom: 170px;
  }
  .ptb_lp--175 {
    padding: 175px 0;
  }
  .plr_lp--175 {
    padding: 0 175px;
  }
  .pt_lp--175 {
    padding-top: 175px;
  }
  .pb_lp--175 {
    padding-bottom: 175px;
  }
  .pl_lp--175 {
    padding-left: 175px;
  }
  .pr_lp--175 {
    padding-right: 175px;
  }
  .mt_lp--175 {
    margin-top: 175px;
  }
  .mb_lp--175 {
    margin-bottom: 175px;
  }
  .ptb_lp--180 {
    padding: 180px 0;
  }
  .plr_lp--180 {
    padding: 0 180px;
  }
  .pt_lp--180 {
    padding-top: 180px;
  }
  .pb_lp--180 {
    padding-bottom: 180px;
  }
  .pl_lp--180 {
    padding-left: 180px;
  }
  .pr_lp--180 {
    padding-right: 180px;
  }
  .mt_lp--180 {
    margin-top: 180px;
  }
  .mb_lp--180 {
    margin-bottom: 180px;
  }
  .ptb_lp--185 {
    padding: 185px 0;
  }
  .plr_lp--185 {
    padding: 0 185px;
  }
  .pt_lp--185 {
    padding-top: 185px;
  }
  .pb_lp--185 {
    padding-bottom: 185px;
  }
  .pl_lp--185 {
    padding-left: 185px;
  }
  .pr_lp--185 {
    padding-right: 185px;
  }
  .mt_lp--185 {
    margin-top: 185px;
  }
  .mb_lp--185 {
    margin-bottom: 185px;
  }
  .ptb_lp--190 {
    padding: 190px 0;
  }
  .plr_lp--190 {
    padding: 0 190px;
  }
  .pt_lp--190 {
    padding-top: 190px;
  }
  .pb_lp--190 {
    padding-bottom: 190px;
  }
  .pl_lp--190 {
    padding-left: 190px;
  }
  .pr_lp--190 {
    padding-right: 190px;
  }
  .mt_lp--190 {
    margin-top: 190px;
  }
  .mb_lp--190 {
    margin-bottom: 190px;
  }
  .ptb_lp--195 {
    padding: 195px 0;
  }
  .plr_lp--195 {
    padding: 0 195px;
  }
  .pt_lp--195 {
    padding-top: 195px;
  }
  .pb_lp--195 {
    padding-bottom: 195px;
  }
  .pl_lp--195 {
    padding-left: 195px;
  }
  .pr_lp--195 {
    padding-right: 195px;
  }
  .mt_lp--195 {
    margin-top: 195px;
  }
  .mb_lp--195 {
    margin-bottom: 195px;
  }
  .ptb_lp--200 {
    padding: 200px 0;
  }
  .plr_lp--200 {
    padding: 0 200px;
  }
  .pt_lp--200 {
    padding-top: 200px;
  }
  .pb_lp--200 {
    padding-bottom: 200px;
  }
  .pl_lp--200 {
    padding-left: 200px;
  }
  .pr_lp--200 {
    padding-right: 200px;
  }
  .mt_lp--200 {
    margin-top: 200px;
  }
  .mb_lp--200 {
    margin-bottom: 200px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .ptb_lg--5 {
    padding: 5px 0 !important;
  }
  .plr_lg--5 {
    padding: 0 5px !important;
  }
  .pt_lg--5 {
    padding-top: 5px !important;
  }
  .pb_lg--5 {
    padding-bottom: 5px !important;
  }
  .pl_lg--5 {
    padding-left: 5px !important;
  }
  .pr_lg--5 {
    padding-right: 5px !important;
  }
  .mt_lg--5 {
    margin-top: 5px !important;
  }
  .mb_lg--5 {
    margin-bottom: 5px !important;
  }
  .ml_lg--5 {
    margin-left: 5px !important;
  }
  .ptb_lg--10 {
    padding: 10px 0 !important;
  }
  .plr_lg--10 {
    padding: 0 10px !important;
  }
  .pt_lg--10 {
    padding-top: 10px !important;
  }
  .pb_lg--10 {
    padding-bottom: 10px !important;
  }
  .pl_lg--10 {
    padding-left: 10px !important;
  }
  .pr_lg--10 {
    padding-right: 10px !important;
  }
  .mt_lg--10 {
    margin-top: 10px !important;
  }
  .mb_lg--10 {
    margin-bottom: 10px !important;
  }
  .ml_lg--10 {
    margin-left: 10px !important;
  }
  .ptb_lg--15 {
    padding: 15px 0 !important;
  }
  .plr_lg--15 {
    padding: 0 15px !important;
  }
  .pt_lg--15 {
    padding-top: 15px !important;
  }
  .pb_lg--15 {
    padding-bottom: 15px !important;
  }
  .pl_lg--15 {
    padding-left: 15px !important;
  }
  .pr_lg--15 {
    padding-right: 15px !important;
  }
  .mt_lg--15 {
    margin-top: 15px !important;
  }
  .mb_lg--15 {
    margin-bottom: 15px !important;
  }
  .ml_lg--15 {
    margin-left: 15px !important;
  }
  .ptb_lg--20 {
    padding: 20px 0 !important;
  }
  .plr_lg--20 {
    padding: 0 20px !important;
  }
  .pt_lg--20 {
    padding-top: 20px !important;
  }
  .pb_lg--20 {
    padding-bottom: 20px !important;
  }
  .pl_lg--20 {
    padding-left: 20px !important;
  }
  .pr_lg--20 {
    padding-right: 20px !important;
  }
  .mt_lg--20 {
    margin-top: 20px !important;
  }
  .mb_lg--20 {
    margin-bottom: 20px !important;
  }
  .ml_lg--20 {
    margin-left: 20px !important;
  }
  .ptb_lg--25 {
    padding: 25px 0 !important;
  }
  .plr_lg--25 {
    padding: 0 25px !important;
  }
  .pt_lg--25 {
    padding-top: 25px !important;
  }
  .pb_lg--25 {
    padding-bottom: 25px !important;
  }
  .pl_lg--25 {
    padding-left: 25px !important;
  }
  .pr_lg--25 {
    padding-right: 25px !important;
  }
  .mt_lg--25 {
    margin-top: 25px !important;
  }
  .mb_lg--25 {
    margin-bottom: 25px !important;
  }
  .ml_lg--25 {
    margin-left: 25px !important;
  }
  .ptb_lg--30 {
    padding: 30px 0 !important;
  }
  .plr_lg--30 {
    padding: 0 30px !important;
  }
  .pt_lg--30 {
    padding-top: 30px !important;
  }
  .pb_lg--30 {
    padding-bottom: 30px !important;
  }
  .pl_lg--30 {
    padding-left: 30px !important;
  }
  .pr_lg--30 {
    padding-right: 30px !important;
  }
  .mt_lg--30 {
    margin-top: 30px !important;
  }
  .mb_lg--30 {
    margin-bottom: 30px !important;
  }
  .ml_lg--30 {
    margin-left: 30px !important;
  }
  .ptb_lg--35 {
    padding: 35px 0 !important;
  }
  .plr_lg--35 {
    padding: 0 35px !important;
  }
  .pt_lg--35 {
    padding-top: 35px !important;
  }
  .pb_lg--35 {
    padding-bottom: 35px !important;
  }
  .pl_lg--35 {
    padding-left: 35px !important;
  }
  .pr_lg--35 {
    padding-right: 35px !important;
  }
  .mt_lg--35 {
    margin-top: 35px !important;
  }
  .mb_lg--35 {
    margin-bottom: 35px !important;
  }
  .ml_lg--35 {
    margin-left: 35px !important;
  }
  .ptb_lg--40 {
    padding: 40px 0 !important;
  }
  .plr_lg--40 {
    padding: 0 40px !important;
  }
  .pt_lg--40 {
    padding-top: 40px !important;
  }
  .pb_lg--40 {
    padding-bottom: 40px !important;
  }
  .pl_lg--40 {
    padding-left: 40px !important;
  }
  .pr_lg--40 {
    padding-right: 40px !important;
  }
  .mt_lg--40 {
    margin-top: 40px !important;
  }
  .mb_lg--40 {
    margin-bottom: 40px !important;
  }
  .ml_lg--40 {
    margin-left: 40px !important;
  }
  .ptb_lg--45 {
    padding: 45px 0 !important;
  }
  .plr_lg--45 {
    padding: 0 45px !important;
  }
  .pt_lg--45 {
    padding-top: 45px !important;
  }
  .pb_lg--45 {
    padding-bottom: 45px !important;
  }
  .pl_lg--45 {
    padding-left: 45px !important;
  }
  .pr_lg--45 {
    padding-right: 45px !important;
  }
  .mt_lg--45 {
    margin-top: 45px !important;
  }
  .mb_lg--45 {
    margin-bottom: 45px !important;
  }
  .ml_lg--45 {
    margin-left: 45px !important;
  }
  .ptb_lg--50 {
    padding: 50px 0 !important;
  }
  .plr_lg--50 {
    padding: 0 50px !important;
  }
  .pt_lg--50 {
    padding-top: 50px !important;
  }
  .pb_lg--50 {
    padding-bottom: 50px !important;
  }
  .pl_lg--50 {
    padding-left: 50px !important;
  }
  .pr_lg--50 {
    padding-right: 50px !important;
  }
  .mt_lg--50 {
    margin-top: 50px !important;
  }
  .mb_lg--50 {
    margin-bottom: 50px !important;
  }
  .ml_lg--50 {
    margin-left: 50px !important;
  }
  .ptb_lg--55 {
    padding: 55px 0 !important;
  }
  .plr_lg--55 {
    padding: 0 55px !important;
  }
  .pt_lg--55 {
    padding-top: 55px !important;
  }
  .pb_lg--55 {
    padding-bottom: 55px !important;
  }
  .pl_lg--55 {
    padding-left: 55px !important;
  }
  .pr_lg--55 {
    padding-right: 55px !important;
  }
  .mt_lg--55 {
    margin-top: 55px !important;
  }
  .mb_lg--55 {
    margin-bottom: 55px !important;
  }
  .ml_lg--55 {
    margin-left: 55px !important;
  }
  .ptb_lg--60 {
    padding: 60px 0 !important;
  }
  .plr_lg--60 {
    padding: 0 60px !important;
  }
  .pt_lg--60 {
    padding-top: 60px !important;
  }
  .pb_lg--60 {
    padding-bottom: 60px !important;
  }
  .pl_lg--60 {
    padding-left: 60px !important;
  }
  .pr_lg--60 {
    padding-right: 60px !important;
  }
  .mt_lg--60 {
    margin-top: 60px !important;
  }
  .mb_lg--60 {
    margin-bottom: 60px !important;
  }
  .ml_lg--60 {
    margin-left: 60px !important;
  }
  .ptb_lg--65 {
    padding: 65px 0 !important;
  }
  .plr_lg--65 {
    padding: 0 65px !important;
  }
  .pt_lg--65 {
    padding-top: 65px !important;
  }
  .pb_lg--65 {
    padding-bottom: 65px !important;
  }
  .pl_lg--65 {
    padding-left: 65px !important;
  }
  .pr_lg--65 {
    padding-right: 65px !important;
  }
  .mt_lg--65 {
    margin-top: 65px !important;
  }
  .mb_lg--65 {
    margin-bottom: 65px !important;
  }
  .ml_lg--65 {
    margin-left: 65px !important;
  }
  .ptb_lg--70 {
    padding: 70px 0 !important;
  }
  .plr_lg--70 {
    padding: 0 70px !important;
  }
  .pt_lg--70 {
    padding-top: 70px !important;
  }
  .pb_lg--70 {
    padding-bottom: 70px !important;
  }
  .pl_lg--70 {
    padding-left: 70px !important;
  }
  .pr_lg--70 {
    padding-right: 70px !important;
  }
  .mt_lg--70 {
    margin-top: 70px !important;
  }
  .mb_lg--70 {
    margin-bottom: 70px !important;
  }
  .ml_lg--70 {
    margin-left: 70px !important;
  }
  .ptb_lg--75 {
    padding: 75px 0 !important;
  }
  .plr_lg--75 {
    padding: 0 75px !important;
  }
  .pt_lg--75 {
    padding-top: 75px !important;
  }
  .pb_lg--75 {
    padding-bottom: 75px !important;
  }
  .pl_lg--75 {
    padding-left: 75px !important;
  }
  .pr_lg--75 {
    padding-right: 75px !important;
  }
  .mt_lg--75 {
    margin-top: 75px !important;
  }
  .mb_lg--75 {
    margin-bottom: 75px !important;
  }
  .ml_lg--75 {
    margin-left: 75px !important;
  }
  .ptb_lg--80 {
    padding: 80px 0 !important;
  }
  .plr_lg--80 {
    padding: 0 80px !important;
  }
  .pt_lg--80 {
    padding-top: 80px !important;
  }
  .pb_lg--80 {
    padding-bottom: 80px !important;
  }
  .pl_lg--80 {
    padding-left: 80px !important;
  }
  .pr_lg--80 {
    padding-right: 80px !important;
  }
  .mt_lg--80 {
    margin-top: 80px !important;
  }
  .mb_lg--80 {
    margin-bottom: 80px !important;
  }
  .ml_lg--80 {
    margin-left: 80px !important;
  }
  .ptb_lg--85 {
    padding: 85px 0 !important;
  }
  .plr_lg--85 {
    padding: 0 85px !important;
  }
  .pt_lg--85 {
    padding-top: 85px !important;
  }
  .pb_lg--85 {
    padding-bottom: 85px !important;
  }
  .pl_lg--85 {
    padding-left: 85px !important;
  }
  .pr_lg--85 {
    padding-right: 85px !important;
  }
  .mt_lg--85 {
    margin-top: 85px !important;
  }
  .mb_lg--85 {
    margin-bottom: 85px !important;
  }
  .ml_lg--85 {
    margin-left: 85px !important;
  }
  .ptb_lg--90 {
    padding: 90px 0 !important;
  }
  .plr_lg--90 {
    padding: 0 90px !important;
  }
  .pt_lg--90 {
    padding-top: 90px !important;
  }
  .pb_lg--90 {
    padding-bottom: 90px !important;
  }
  .pl_lg--90 {
    padding-left: 90px !important;
  }
  .pr_lg--90 {
    padding-right: 90px !important;
  }
  .mt_lg--90 {
    margin-top: 90px !important;
  }
  .mb_lg--90 {
    margin-bottom: 90px !important;
  }
  .ml_lg--90 {
    margin-left: 90px !important;
  }
  .ptb_lg--95 {
    padding: 95px 0 !important;
  }
  .plr_lg--95 {
    padding: 0 95px !important;
  }
  .pt_lg--95 {
    padding-top: 95px !important;
  }
  .pb_lg--95 {
    padding-bottom: 95px !important;
  }
  .pl_lg--95 {
    padding-left: 95px !important;
  }
  .pr_lg--95 {
    padding-right: 95px !important;
  }
  .mt_lg--95 {
    margin-top: 95px !important;
  }
  .mb_lg--95 {
    margin-bottom: 95px !important;
  }
  .ml_lg--95 {
    margin-left: 95px !important;
  }
  .ptb_lg--100 {
    padding: 100px 0 !important;
  }
  .plr_lg--100 {
    padding: 0 100px !important;
  }
  .pt_lg--100 {
    padding-top: 100px !important;
  }
  .pb_lg--100 {
    padding-bottom: 100px !important;
  }
  .pl_lg--100 {
    padding-left: 100px !important;
  }
  .pr_lg--100 {
    padding-right: 100px !important;
  }
  .mt_lg--100 {
    margin-top: 100px !important;
  }
  .mb_lg--100 {
    margin-bottom: 100px !important;
  }
  .ml_lg--100 {
    margin-left: 100px !important;
  }
  .ptb_lg--105 {
    padding: 105px 0 !important;
  }
  .plr_lg--105 {
    padding: 0 105px !important;
  }
  .pt_lg--105 {
    padding-top: 105px !important;
  }
  .pb_lg--105 {
    padding-bottom: 105px !important;
  }
  .pl_lg--105 {
    padding-left: 105px !important;
  }
  .pr_lg--105 {
    padding-right: 105px !important;
  }
  .mt_lg--105 {
    margin-top: 105px !important;
  }
  .mb_lg--105 {
    margin-bottom: 105px !important;
  }
  .ml_lg--105 {
    margin-left: 105px !important;
  }
  .ptb_lg--110 {
    padding: 110px 0 !important;
  }
  .plr_lg--110 {
    padding: 0 110px !important;
  }
  .pt_lg--110 {
    padding-top: 110px !important;
  }
  .pb_lg--110 {
    padding-bottom: 110px !important;
  }
  .pl_lg--110 {
    padding-left: 110px !important;
  }
  .pr_lg--110 {
    padding-right: 110px !important;
  }
  .mt_lg--110 {
    margin-top: 110px !important;
  }
  .mb_lg--110 {
    margin-bottom: 110px !important;
  }
  .ml_lg--110 {
    margin-left: 110px !important;
  }
  .ptb_lg--115 {
    padding: 115px 0 !important;
  }
  .plr_lg--115 {
    padding: 0 115px !important;
  }
  .pt_lg--115 {
    padding-top: 115px !important;
  }
  .pb_lg--115 {
    padding-bottom: 115px !important;
  }
  .pl_lg--115 {
    padding-left: 115px !important;
  }
  .pr_lg--115 {
    padding-right: 115px !important;
  }
  .mt_lg--115 {
    margin-top: 115px !important;
  }
  .mb_lg--115 {
    margin-bottom: 115px !important;
  }
  .ml_lg--115 {
    margin-left: 115px !important;
  }
  .ptb_lg--120 {
    padding: 120px 0 !important;
  }
  .plr_lg--120 {
    padding: 0 120px !important;
  }
  .pt_lg--120 {
    padding-top: 120px !important;
  }
  .pb_lg--120 {
    padding-bottom: 120px !important;
  }
  .pl_lg--120 {
    padding-left: 120px !important;
  }
  .pr_lg--120 {
    padding-right: 120px !important;
  }
  .mt_lg--120 {
    margin-top: 120px !important;
  }
  .mb_lg--120 {
    margin-bottom: 120px !important;
  }
  .ml_lg--120 {
    margin-left: 120px !important;
  }
  .ptb_lg--125 {
    padding: 125px 0 !important;
  }
  .plr_lg--125 {
    padding: 0 125px !important;
  }
  .pt_lg--125 {
    padding-top: 125px !important;
  }
  .pb_lg--125 {
    padding-bottom: 125px !important;
  }
  .pl_lg--125 {
    padding-left: 125px !important;
  }
  .pr_lg--125 {
    padding-right: 125px !important;
  }
  .mt_lg--125 {
    margin-top: 125px !important;
  }
  .mb_lg--125 {
    margin-bottom: 125px !important;
  }
  .ml_lg--125 {
    margin-left: 125px !important;
  }
  .ptb_lg--130 {
    padding: 130px 0 !important;
  }
  .plr_lg--130 {
    padding: 0 130px !important;
  }
  .pt_lg--130 {
    padding-top: 130px !important;
  }
  .pb_lg--130 {
    padding-bottom: 130px !important;
  }
  .pl_lg--130 {
    padding-left: 130px !important;
  }
  .pr_lg--130 {
    padding-right: 130px !important;
  }
  .mt_lg--130 {
    margin-top: 130px !important;
  }
  .mb_lg--130 {
    margin-bottom: 130px !important;
  }
  .ml_lg--130 {
    margin-left: 130px !important;
  }
  .ptb_lg--135 {
    padding: 135px 0 !important;
  }
  .plr_lg--135 {
    padding: 0 135px !important;
  }
  .pt_lg--135 {
    padding-top: 135px !important;
  }
  .pb_lg--135 {
    padding-bottom: 135px !important;
  }
  .pl_lg--135 {
    padding-left: 135px !important;
  }
  .pr_lg--135 {
    padding-right: 135px !important;
  }
  .mt_lg--135 {
    margin-top: 135px !important;
  }
  .mb_lg--135 {
    margin-bottom: 135px !important;
  }
  .ml_lg--135 {
    margin-left: 135px !important;
  }
  .ptb_lg--140 {
    padding: 140px 0 !important;
  }
  .plr_lg--140 {
    padding: 0 140px !important;
  }
  .pt_lg--140 {
    padding-top: 140px !important;
  }
  .pb_lg--140 {
    padding-bottom: 140px !important;
  }
  .pl_lg--140 {
    padding-left: 140px !important;
  }
  .pr_lg--140 {
    padding-right: 140px !important;
  }
  .mt_lg--140 {
    margin-top: 140px !important;
  }
  .mb_lg--140 {
    margin-bottom: 140px !important;
  }
  .ml_lg--140 {
    margin-left: 140px !important;
  }
  .ptb_lg--145 {
    padding: 145px 0 !important;
  }
  .plr_lg--145 {
    padding: 0 145px !important;
  }
  .pt_lg--145 {
    padding-top: 145px !important;
  }
  .pb_lg--145 {
    padding-bottom: 145px !important;
  }
  .pl_lg--145 {
    padding-left: 145px !important;
  }
  .pr_lg--145 {
    padding-right: 145px !important;
  }
  .mt_lg--145 {
    margin-top: 145px !important;
  }
  .mb_lg--145 {
    margin-bottom: 145px !important;
  }
  .ml_lg--145 {
    margin-left: 145px !important;
  }
  .ptb_lg--150 {
    padding: 150px 0 !important;
  }
  .plr_lg--150 {
    padding: 0 150px !important;
  }
  .pt_lg--150 {
    padding-top: 150px !important;
  }
  .pb_lg--150 {
    padding-bottom: 150px !important;
  }
  .pl_lg--150 {
    padding-left: 150px !important;
  }
  .pr_lg--150 {
    padding-right: 150px !important;
  }
  .mt_lg--150 {
    margin-top: 150px !important;
  }
  .mb_lg--150 {
    margin-bottom: 150px !important;
  }
  .ml_lg--150 {
    margin-left: 150px !important;
  }
  .ptb_lg--155 {
    padding: 155px 0 !important;
  }
  .plr_lg--155 {
    padding: 0 155px !important;
  }
  .pt_lg--155 {
    padding-top: 155px !important;
  }
  .pb_lg--155 {
    padding-bottom: 155px !important;
  }
  .pl_lg--155 {
    padding-left: 155px !important;
  }
  .pr_lg--155 {
    padding-right: 155px !important;
  }
  .mt_lg--155 {
    margin-top: 155px !important;
  }
  .mb_lg--155 {
    margin-bottom: 155px !important;
  }
  .ml_lg--155 {
    margin-left: 155px !important;
  }
  .ptb_lg--160 {
    padding: 160px 0 !important;
  }
  .plr_lg--160 {
    padding: 0 160px !important;
  }
  .pt_lg--160 {
    padding-top: 160px !important;
  }
  .pb_lg--160 {
    padding-bottom: 160px !important;
  }
  .pl_lg--160 {
    padding-left: 160px !important;
  }
  .pr_lg--160 {
    padding-right: 160px !important;
  }
  .mt_lg--160 {
    margin-top: 160px !important;
  }
  .mb_lg--160 {
    margin-bottom: 160px !important;
  }
  .ml_lg--160 {
    margin-left: 160px !important;
  }
  .ptb_lg--165 {
    padding: 165px 0 !important;
  }
  .plr_lg--165 {
    padding: 0 165px !important;
  }
  .pt_lg--165 {
    padding-top: 165px !important;
  }
  .pb_lg--165 {
    padding-bottom: 165px !important;
  }
  .pl_lg--165 {
    padding-left: 165px !important;
  }
  .pr_lg--165 {
    padding-right: 165px !important;
  }
  .mt_lg--165 {
    margin-top: 165px !important;
  }
  .mb_lg--165 {
    margin-bottom: 165px !important;
  }
  .ml_lg--165 {
    margin-left: 165px !important;
  }
  .ptb_lg--170 {
    padding: 170px 0 !important;
  }
  .plr_lg--170 {
    padding: 0 170px !important;
  }
  .pt_lg--170 {
    padding-top: 170px !important;
  }
  .pb_lg--170 {
    padding-bottom: 170px !important;
  }
  .pl_lg--170 {
    padding-left: 170px !important;
  }
  .pr_lg--170 {
    padding-right: 170px !important;
  }
  .mt_lg--170 {
    margin-top: 170px !important;
  }
  .mb_lg--170 {
    margin-bottom: 170px !important;
  }
  .ml_lg--170 {
    margin-left: 170px !important;
  }
  .ptb_lg--175 {
    padding: 175px 0 !important;
  }
  .plr_lg--175 {
    padding: 0 175px !important;
  }
  .pt_lg--175 {
    padding-top: 175px !important;
  }
  .pb_lg--175 {
    padding-bottom: 175px !important;
  }
  .pl_lg--175 {
    padding-left: 175px !important;
  }
  .pr_lg--175 {
    padding-right: 175px !important;
  }
  .mt_lg--175 {
    margin-top: 175px !important;
  }
  .mb_lg--175 {
    margin-bottom: 175px !important;
  }
  .ml_lg--175 {
    margin-left: 175px !important;
  }
  .ptb_lg--180 {
    padding: 180px 0 !important;
  }
  .plr_lg--180 {
    padding: 0 180px !important;
  }
  .pt_lg--180 {
    padding-top: 180px !important;
  }
  .pb_lg--180 {
    padding-bottom: 180px !important;
  }
  .pl_lg--180 {
    padding-left: 180px !important;
  }
  .pr_lg--180 {
    padding-right: 180px !important;
  }
  .mt_lg--180 {
    margin-top: 180px !important;
  }
  .mb_lg--180 {
    margin-bottom: 180px !important;
  }
  .ml_lg--180 {
    margin-left: 180px !important;
  }
  .ptb_lg--185 {
    padding: 185px 0 !important;
  }
  .plr_lg--185 {
    padding: 0 185px !important;
  }
  .pt_lg--185 {
    padding-top: 185px !important;
  }
  .pb_lg--185 {
    padding-bottom: 185px !important;
  }
  .pl_lg--185 {
    padding-left: 185px !important;
  }
  .pr_lg--185 {
    padding-right: 185px !important;
  }
  .mt_lg--185 {
    margin-top: 185px !important;
  }
  .mb_lg--185 {
    margin-bottom: 185px !important;
  }
  .ml_lg--185 {
    margin-left: 185px !important;
  }
  .ptb_lg--190 {
    padding: 190px 0 !important;
  }
  .plr_lg--190 {
    padding: 0 190px !important;
  }
  .pt_lg--190 {
    padding-top: 190px !important;
  }
  .pb_lg--190 {
    padding-bottom: 190px !important;
  }
  .pl_lg--190 {
    padding-left: 190px !important;
  }
  .pr_lg--190 {
    padding-right: 190px !important;
  }
  .mt_lg--190 {
    margin-top: 190px !important;
  }
  .mb_lg--190 {
    margin-bottom: 190px !important;
  }
  .ml_lg--190 {
    margin-left: 190px !important;
  }
  .ptb_lg--195 {
    padding: 195px 0 !important;
  }
  .plr_lg--195 {
    padding: 0 195px !important;
  }
  .pt_lg--195 {
    padding-top: 195px !important;
  }
  .pb_lg--195 {
    padding-bottom: 195px !important;
  }
  .pl_lg--195 {
    padding-left: 195px !important;
  }
  .pr_lg--195 {
    padding-right: 195px !important;
  }
  .mt_lg--195 {
    margin-top: 195px !important;
  }
  .mb_lg--195 {
    margin-bottom: 195px !important;
  }
  .ml_lg--195 {
    margin-left: 195px !important;
  }
  .ptb_lg--200 {
    padding: 200px 0 !important;
  }
  .plr_lg--200 {
    padding: 0 200px !important;
  }
  .pt_lg--200 {
    padding-top: 200px !important;
  }
  .pb_lg--200 {
    padding-bottom: 200px !important;
  }
  .pl_lg--200 {
    padding-left: 200px !important;
  }
  .pr_lg--200 {
    padding-right: 200px !important;
  }
  .mt_lg--200 {
    margin-top: 200px !important;
  }
  .mb_lg--200 {
    margin-bottom: 200px !important;
  }
  .ml_lg--200 {
    margin-left: 200px !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ptb_md--0 {
    padding: 0 !important;
  }
  .pl_md--0 {
    padding-left: 0 !important;
  }
  .pr_md--0 {
    padding-right: 0 !important;
  }
  .pt_md--0 {
    padding-top: 0 !important;
  }
  .pb_md--0 {
    padding-bottom: 0 !important;
  }
  .mr_md--0 {
    margin-right: 0 !important;
  }
  .ml_md--0 {
    margin-left: 0 !important;
  }
  .mt_md--0 {
    margin-top: 0 !important;
  }
  .mb_md--0 {
    margin-bottom: 0 !important;
  }
  .ptb_md--250 {
    padding: 250px 0 !important;
  }
  .ptb_md--5 {
    padding: 5px 0 !important;
  }
  .plr_md--5 {
    padding: 0 5px !important;
  }
  .pt_md--5 {
    padding-top: 5px !important;
  }
  .pb_md--5 {
    padding-bottom: 5px !important;
  }
  .pl_md--5 {
    padding-left: 5px !important;
  }
  .pr_md--5 {
    padding-right: 5px !important;
  }
  .mt_md--5 {
    margin-top: 5px !important;
  }
  .mb_md--5 {
    margin-bottom: 5px !important;
  }
  .ptb_md--10 {
    padding: 10px 0 !important;
  }
  .plr_md--10 {
    padding: 0 10px !important;
  }
  .pt_md--10 {
    padding-top: 10px !important;
  }
  .pb_md--10 {
    padding-bottom: 10px !important;
  }
  .pl_md--10 {
    padding-left: 10px !important;
  }
  .pr_md--10 {
    padding-right: 10px !important;
  }
  .mt_md--10 {
    margin-top: 10px !important;
  }
  .mb_md--10 {
    margin-bottom: 10px !important;
  }
  .ptb_md--15 {
    padding: 15px 0 !important;
  }
  .plr_md--15 {
    padding: 0 15px !important;
  }
  .pt_md--15 {
    padding-top: 15px !important;
  }
  .pb_md--15 {
    padding-bottom: 15px !important;
  }
  .pl_md--15 {
    padding-left: 15px !important;
  }
  .pr_md--15 {
    padding-right: 15px !important;
  }
  .mt_md--15 {
    margin-top: 15px !important;
  }
  .mb_md--15 {
    margin-bottom: 15px !important;
  }
  .ptb_md--20 {
    padding: 20px 0 !important;
  }
  .plr_md--20 {
    padding: 0 20px !important;
  }
  .pt_md--20 {
    padding-top: 20px !important;
  }
  .pb_md--20 {
    padding-bottom: 20px !important;
  }
  .pl_md--20 {
    padding-left: 20px !important;
  }
  .pr_md--20 {
    padding-right: 20px !important;
  }
  .mt_md--20 {
    margin-top: 20px !important;
  }
  .mb_md--20 {
    margin-bottom: 20px !important;
  }
  .ptb_md--25 {
    padding: 25px 0 !important;
  }
  .plr_md--25 {
    padding: 0 25px !important;
  }
  .pt_md--25 {
    padding-top: 25px !important;
  }
  .pb_md--25 {
    padding-bottom: 25px !important;
  }
  .pl_md--25 {
    padding-left: 25px !important;
  }
  .pr_md--25 {
    padding-right: 25px !important;
  }
  .mt_md--25 {
    margin-top: 25px !important;
  }
  .mb_md--25 {
    margin-bottom: 25px !important;
  }
  .ptb_md--30 {
    padding: 30px 0 !important;
  }
  .plr_md--30 {
    padding: 0 30px !important;
  }
  .pt_md--30 {
    padding-top: 30px !important;
  }
  .pb_md--30 {
    padding-bottom: 30px !important;
  }
  .pl_md--30 {
    padding-left: 30px !important;
  }
  .pr_md--30 {
    padding-right: 30px !important;
  }
  .mt_md--30 {
    margin-top: 30px !important;
  }
  .mb_md--30 {
    margin-bottom: 30px !important;
  }
  .ptb_md--35 {
    padding: 35px 0 !important;
  }
  .plr_md--35 {
    padding: 0 35px !important;
  }
  .pt_md--35 {
    padding-top: 35px !important;
  }
  .pb_md--35 {
    padding-bottom: 35px !important;
  }
  .pl_md--35 {
    padding-left: 35px !important;
  }
  .pr_md--35 {
    padding-right: 35px !important;
  }
  .mt_md--35 {
    margin-top: 35px !important;
  }
  .mb_md--35 {
    margin-bottom: 35px !important;
  }
  .ptb_md--40 {
    padding: 40px 0 !important;
  }
  .plr_md--40 {
    padding: 0 40px !important;
  }
  .pt_md--40 {
    padding-top: 40px !important;
  }
  .pb_md--40 {
    padding-bottom: 40px !important;
  }
  .pl_md--40 {
    padding-left: 40px !important;
  }
  .pr_md--40 {
    padding-right: 40px !important;
  }
  .mt_md--40 {
    margin-top: 40px !important;
  }
  .mb_md--40 {
    margin-bottom: 40px !important;
  }
  .ptb_md--45 {
    padding: 45px 0 !important;
  }
  .plr_md--45 {
    padding: 0 45px !important;
  }
  .pt_md--45 {
    padding-top: 45px !important;
  }
  .pb_md--45 {
    padding-bottom: 45px !important;
  }
  .pl_md--45 {
    padding-left: 45px !important;
  }
  .pr_md--45 {
    padding-right: 45px !important;
  }
  .mt_md--45 {
    margin-top: 45px !important;
  }
  .mb_md--45 {
    margin-bottom: 45px !important;
  }
  .ptb_md--50 {
    padding: 50px 0 !important;
  }
  .plr_md--50 {
    padding: 0 50px !important;
  }
  .pt_md--50 {
    padding-top: 50px !important;
  }
  .pb_md--50 {
    padding-bottom: 50px !important;
  }
  .pl_md--50 {
    padding-left: 50px !important;
  }
  .pr_md--50 {
    padding-right: 50px !important;
  }
  .mt_md--50 {
    margin-top: 50px !important;
  }
  .mb_md--50 {
    margin-bottom: 50px !important;
  }
  .ptb_md--55 {
    padding: 55px 0 !important;
  }
  .plr_md--55 {
    padding: 0 55px !important;
  }
  .pt_md--55 {
    padding-top: 55px !important;
  }
  .pb_md--55 {
    padding-bottom: 55px !important;
  }
  .pl_md--55 {
    padding-left: 55px !important;
  }
  .pr_md--55 {
    padding-right: 55px !important;
  }
  .mt_md--55 {
    margin-top: 55px !important;
  }
  .mb_md--55 {
    margin-bottom: 55px !important;
  }
  .ptb_md--60 {
    padding: 60px 0 !important;
  }
  .plr_md--60 {
    padding: 0 60px !important;
  }
  .pt_md--60 {
    padding-top: 60px !important;
  }
  .pb_md--60 {
    padding-bottom: 60px !important;
  }
  .pl_md--60 {
    padding-left: 60px !important;
  }
  .pr_md--60 {
    padding-right: 60px !important;
  }
  .mt_md--60 {
    margin-top: 60px !important;
  }
  .mb_md--60 {
    margin-bottom: 60px !important;
  }
  .ptb_md--65 {
    padding: 65px 0 !important;
  }
  .plr_md--65 {
    padding: 0 65px !important;
  }
  .pt_md--65 {
    padding-top: 65px !important;
  }
  .pb_md--65 {
    padding-bottom: 65px !important;
  }
  .pl_md--65 {
    padding-left: 65px !important;
  }
  .pr_md--65 {
    padding-right: 65px !important;
  }
  .mt_md--65 {
    margin-top: 65px !important;
  }
  .mb_md--65 {
    margin-bottom: 65px !important;
  }
  .ptb_md--70 {
    padding: 70px 0 !important;
  }
  .plr_md--70 {
    padding: 0 70px !important;
  }
  .pt_md--70 {
    padding-top: 70px !important;
  }
  .pb_md--70 {
    padding-bottom: 70px !important;
  }
  .pl_md--70 {
    padding-left: 70px !important;
  }
  .pr_md--70 {
    padding-right: 70px !important;
  }
  .mt_md--70 {
    margin-top: 70px !important;
  }
  .mb_md--70 {
    margin-bottom: 70px !important;
  }
  .ptb_md--75 {
    padding: 75px 0 !important;
  }
  .plr_md--75 {
    padding: 0 75px !important;
  }
  .pt_md--75 {
    padding-top: 75px !important;
  }
  .pb_md--75 {
    padding-bottom: 75px !important;
  }
  .pl_md--75 {
    padding-left: 75px !important;
  }
  .pr_md--75 {
    padding-right: 75px !important;
  }
  .mt_md--75 {
    margin-top: 75px !important;
  }
  .mb_md--75 {
    margin-bottom: 75px !important;
  }
  .ptb_md--80 {
    padding: 80px 0 !important;
  }
  .plr_md--80 {
    padding: 0 80px !important;
  }
  .pt_md--80 {
    padding-top: 80px !important;
  }
  .pb_md--80 {
    padding-bottom: 80px !important;
  }
  .pl_md--80 {
    padding-left: 80px !important;
  }
  .pr_md--80 {
    padding-right: 80px !important;
  }
  .mt_md--80 {
    margin-top: 80px !important;
  }
  .mb_md--80 {
    margin-bottom: 80px !important;
  }
  .ptb_md--85 {
    padding: 85px 0 !important;
  }
  .plr_md--85 {
    padding: 0 85px !important;
  }
  .pt_md--85 {
    padding-top: 85px !important;
  }
  .pb_md--85 {
    padding-bottom: 85px !important;
  }
  .pl_md--85 {
    padding-left: 85px !important;
  }
  .pr_md--85 {
    padding-right: 85px !important;
  }
  .mt_md--85 {
    margin-top: 85px !important;
  }
  .mb_md--85 {
    margin-bottom: 85px !important;
  }
  .ptb_md--90 {
    padding: 90px 0 !important;
  }
  .plr_md--90 {
    padding: 0 90px !important;
  }
  .pt_md--90 {
    padding-top: 90px !important;
  }
  .pb_md--90 {
    padding-bottom: 90px !important;
  }
  .pl_md--90 {
    padding-left: 90px !important;
  }
  .pr_md--90 {
    padding-right: 90px !important;
  }
  .mt_md--90 {
    margin-top: 90px !important;
  }
  .mb_md--90 {
    margin-bottom: 90px !important;
  }
  .ptb_md--95 {
    padding: 95px 0 !important;
  }
  .plr_md--95 {
    padding: 0 95px !important;
  }
  .pt_md--95 {
    padding-top: 95px !important;
  }
  .pb_md--95 {
    padding-bottom: 95px !important;
  }
  .pl_md--95 {
    padding-left: 95px !important;
  }
  .pr_md--95 {
    padding-right: 95px !important;
  }
  .mt_md--95 {
    margin-top: 95px !important;
  }
  .mb_md--95 {
    margin-bottom: 95px !important;
  }
  .ptb_md--100 {
    padding: 100px 0 !important;
  }
  .plr_md--100 {
    padding: 0 100px !important;
  }
  .pt_md--100 {
    padding-top: 100px !important;
  }
  .pb_md--100 {
    padding-bottom: 100px !important;
  }
  .pl_md--100 {
    padding-left: 100px !important;
  }
  .pr_md--100 {
    padding-right: 100px !important;
  }
  .mt_md--100 {
    margin-top: 100px !important;
  }
  .mb_md--100 {
    margin-bottom: 100px !important;
  }
  .ptb_md--105 {
    padding: 105px 0 !important;
  }
  .plr_md--105 {
    padding: 0 105px !important;
  }
  .pt_md--105 {
    padding-top: 105px !important;
  }
  .pb_md--105 {
    padding-bottom: 105px !important;
  }
  .pl_md--105 {
    padding-left: 105px !important;
  }
  .pr_md--105 {
    padding-right: 105px !important;
  }
  .mt_md--105 {
    margin-top: 105px !important;
  }
  .mb_md--105 {
    margin-bottom: 105px !important;
  }
  .ptb_md--110 {
    padding: 110px 0 !important;
  }
  .plr_md--110 {
    padding: 0 110px !important;
  }
  .pt_md--110 {
    padding-top: 110px !important;
  }
  .pb_md--110 {
    padding-bottom: 110px !important;
  }
  .pl_md--110 {
    padding-left: 110px !important;
  }
  .pr_md--110 {
    padding-right: 110px !important;
  }
  .mt_md--110 {
    margin-top: 110px !important;
  }
  .mb_md--110 {
    margin-bottom: 110px !important;
  }
  .ptb_md--115 {
    padding: 115px 0 !important;
  }
  .plr_md--115 {
    padding: 0 115px !important;
  }
  .pt_md--115 {
    padding-top: 115px !important;
  }
  .pb_md--115 {
    padding-bottom: 115px !important;
  }
  .pl_md--115 {
    padding-left: 115px !important;
  }
  .pr_md--115 {
    padding-right: 115px !important;
  }
  .mt_md--115 {
    margin-top: 115px !important;
  }
  .mb_md--115 {
    margin-bottom: 115px !important;
  }
  .ptb_md--120 {
    padding: 120px 0 !important;
  }
  .plr_md--120 {
    padding: 0 120px !important;
  }
  .pt_md--120 {
    padding-top: 120px !important;
  }
  .pb_md--120 {
    padding-bottom: 120px !important;
  }
  .pl_md--120 {
    padding-left: 120px !important;
  }
  .pr_md--120 {
    padding-right: 120px !important;
  }
  .mt_md--120 {
    margin-top: 120px !important;
  }
  .mb_md--120 {
    margin-bottom: 120px !important;
  }
  .ptb_md--125 {
    padding: 125px 0 !important;
  }
  .plr_md--125 {
    padding: 0 125px !important;
  }
  .pt_md--125 {
    padding-top: 125px !important;
  }
  .pb_md--125 {
    padding-bottom: 125px !important;
  }
  .pl_md--125 {
    padding-left: 125px !important;
  }
  .pr_md--125 {
    padding-right: 125px !important;
  }
  .mt_md--125 {
    margin-top: 125px !important;
  }
  .mb_md--125 {
    margin-bottom: 125px !important;
  }
  .ptb_md--130 {
    padding: 130px 0 !important;
  }
  .plr_md--130 {
    padding: 0 130px !important;
  }
  .pt_md--130 {
    padding-top: 130px !important;
  }
  .pb_md--130 {
    padding-bottom: 130px !important;
  }
  .pl_md--130 {
    padding-left: 130px !important;
  }
  .pr_md--130 {
    padding-right: 130px !important;
  }
  .mt_md--130 {
    margin-top: 130px !important;
  }
  .mb_md--130 {
    margin-bottom: 130px !important;
  }
  .ptb_md--135 {
    padding: 135px 0 !important;
  }
  .plr_md--135 {
    padding: 0 135px !important;
  }
  .pt_md--135 {
    padding-top: 135px !important;
  }
  .pb_md--135 {
    padding-bottom: 135px !important;
  }
  .pl_md--135 {
    padding-left: 135px !important;
  }
  .pr_md--135 {
    padding-right: 135px !important;
  }
  .mt_md--135 {
    margin-top: 135px !important;
  }
  .mb_md--135 {
    margin-bottom: 135px !important;
  }
  .ptb_md--140 {
    padding: 140px 0 !important;
  }
  .plr_md--140 {
    padding: 0 140px !important;
  }
  .pt_md--140 {
    padding-top: 140px !important;
  }
  .pb_md--140 {
    padding-bottom: 140px !important;
  }
  .pl_md--140 {
    padding-left: 140px !important;
  }
  .pr_md--140 {
    padding-right: 140px !important;
  }
  .mt_md--140 {
    margin-top: 140px !important;
  }
  .mb_md--140 {
    margin-bottom: 140px !important;
  }
  .ptb_md--145 {
    padding: 145px 0 !important;
  }
  .plr_md--145 {
    padding: 0 145px !important;
  }
  .pt_md--145 {
    padding-top: 145px !important;
  }
  .pb_md--145 {
    padding-bottom: 145px !important;
  }
  .pl_md--145 {
    padding-left: 145px !important;
  }
  .pr_md--145 {
    padding-right: 145px !important;
  }
  .mt_md--145 {
    margin-top: 145px !important;
  }
  .mb_md--145 {
    margin-bottom: 145px !important;
  }
  .ptb_md--150 {
    padding: 150px 0 !important;
  }
  .plr_md--150 {
    padding: 0 150px !important;
  }
  .pt_md--150 {
    padding-top: 150px !important;
  }
  .pb_md--150 {
    padding-bottom: 150px !important;
  }
  .pl_md--150 {
    padding-left: 150px !important;
  }
  .pr_md--150 {
    padding-right: 150px !important;
  }
  .mt_md--150 {
    margin-top: 150px !important;
  }
  .mb_md--150 {
    margin-bottom: 150px !important;
  }
  .ptb_md--155 {
    padding: 155px 0 !important;
  }
  .plr_md--155 {
    padding: 0 155px !important;
  }
  .pt_md--155 {
    padding-top: 155px !important;
  }
  .pb_md--155 {
    padding-bottom: 155px !important;
  }
  .pl_md--155 {
    padding-left: 155px !important;
  }
  .pr_md--155 {
    padding-right: 155px !important;
  }
  .mt_md--155 {
    margin-top: 155px !important;
  }
  .mb_md--155 {
    margin-bottom: 155px !important;
  }
  .ptb_md--160 {
    padding: 160px 0 !important;
  }
  .plr_md--160 {
    padding: 0 160px !important;
  }
  .pt_md--160 {
    padding-top: 160px !important;
  }
  .pb_md--160 {
    padding-bottom: 160px !important;
  }
  .pl_md--160 {
    padding-left: 160px !important;
  }
  .pr_md--160 {
    padding-right: 160px !important;
  }
  .mt_md--160 {
    margin-top: 160px !important;
  }
  .mb_md--160 {
    margin-bottom: 160px !important;
  }
  .ptb_md--165 {
    padding: 165px 0 !important;
  }
  .plr_md--165 {
    padding: 0 165px !important;
  }
  .pt_md--165 {
    padding-top: 165px !important;
  }
  .pb_md--165 {
    padding-bottom: 165px !important;
  }
  .pl_md--165 {
    padding-left: 165px !important;
  }
  .pr_md--165 {
    padding-right: 165px !important;
  }
  .mt_md--165 {
    margin-top: 165px !important;
  }
  .mb_md--165 {
    margin-bottom: 165px !important;
  }
  .ptb_md--170 {
    padding: 170px 0 !important;
  }
  .plr_md--170 {
    padding: 0 170px !important;
  }
  .pt_md--170 {
    padding-top: 170px !important;
  }
  .pb_md--170 {
    padding-bottom: 170px !important;
  }
  .pl_md--170 {
    padding-left: 170px !important;
  }
  .pr_md--170 {
    padding-right: 170px !important;
  }
  .mt_md--170 {
    margin-top: 170px !important;
  }
  .mb_md--170 {
    margin-bottom: 170px !important;
  }
  .ptb_md--175 {
    padding: 175px 0 !important;
  }
  .plr_md--175 {
    padding: 0 175px !important;
  }
  .pt_md--175 {
    padding-top: 175px !important;
  }
  .pb_md--175 {
    padding-bottom: 175px !important;
  }
  .pl_md--175 {
    padding-left: 175px !important;
  }
  .pr_md--175 {
    padding-right: 175px !important;
  }
  .mt_md--175 {
    margin-top: 175px !important;
  }
  .mb_md--175 {
    margin-bottom: 175px !important;
  }
  .ptb_md--180 {
    padding: 180px 0 !important;
  }
  .plr_md--180 {
    padding: 0 180px !important;
  }
  .pt_md--180 {
    padding-top: 180px !important;
  }
  .pb_md--180 {
    padding-bottom: 180px !important;
  }
  .pl_md--180 {
    padding-left: 180px !important;
  }
  .pr_md--180 {
    padding-right: 180px !important;
  }
  .mt_md--180 {
    margin-top: 180px !important;
  }
  .mb_md--180 {
    margin-bottom: 180px !important;
  }
  .ptb_md--185 {
    padding: 185px 0 !important;
  }
  .plr_md--185 {
    padding: 0 185px !important;
  }
  .pt_md--185 {
    padding-top: 185px !important;
  }
  .pb_md--185 {
    padding-bottom: 185px !important;
  }
  .pl_md--185 {
    padding-left: 185px !important;
  }
  .pr_md--185 {
    padding-right: 185px !important;
  }
  .mt_md--185 {
    margin-top: 185px !important;
  }
  .mb_md--185 {
    margin-bottom: 185px !important;
  }
  .ptb_md--190 {
    padding: 190px 0 !important;
  }
  .plr_md--190 {
    padding: 0 190px !important;
  }
  .pt_md--190 {
    padding-top: 190px !important;
  }
  .pb_md--190 {
    padding-bottom: 190px !important;
  }
  .pl_md--190 {
    padding-left: 190px !important;
  }
  .pr_md--190 {
    padding-right: 190px !important;
  }
  .mt_md--190 {
    margin-top: 190px !important;
  }
  .mb_md--190 {
    margin-bottom: 190px !important;
  }
  .ptb_md--195 {
    padding: 195px 0 !important;
  }
  .plr_md--195 {
    padding: 0 195px !important;
  }
  .pt_md--195 {
    padding-top: 195px !important;
  }
  .pb_md--195 {
    padding-bottom: 195px !important;
  }
  .pl_md--195 {
    padding-left: 195px !important;
  }
  .pr_md--195 {
    padding-right: 195px !important;
  }
  .mt_md--195 {
    margin-top: 195px !important;
  }
  .mb_md--195 {
    margin-bottom: 195px !important;
  }
  .ptb_md--200 {
    padding: 200px 0 !important;
  }
  .plr_md--200 {
    padding: 0 200px !important;
  }
  .pt_md--200 {
    padding-top: 200px !important;
  }
  .pb_md--200 {
    padding-bottom: 200px !important;
  }
  .pl_md--200 {
    padding-left: 200px !important;
  }
  .pr_md--200 {
    padding-right: 200px !important;
  }
  .mt_md--200 {
    margin-top: 200px !important;
  }
  .mb_md--200 {
    margin-bottom: 200px !important;
  }
}
@media only screen and (max-width: 767px) {
  .ptb_sm--250 {
    padding: 250px 0 !important;
  }
  .ptb_sm--0 {
    padding: 0 !important;
  }
  .pl_sm--0 {
    padding-left: 0 !important;
  }
  .pr_sm--0 {
    padding-right: 0 !important;
  }
  .pt_sm--0 {
    padding-top: 0 !important;
  }
  .pb_sm--0 {
    padding-bottom: 0 !important;
  }
  .mr_sm--0 {
    margin-right: 0 !important;
  }
  .ml_sm--0 {
    margin-left: 0 !important;
  }
  .mt_sm--0 {
    margin-top: 0 !important;
  }
  .mb_sm--0 {
    margin-bottom: 0 !important;
  }
  .pt_sm--150 {
    padding-top: 150px !important;
  }
  .pb_sm--110 {
    padding-bottom: 110px !important;
  }
  .ptb_sm--5 {
    padding: 5px 0 !important;
  }
  .plr_sm--5 {
    padding: 0 5px !important;
  }
  .pt_sm--5 {
    padding-top: 5px !important;
  }
  .pb_sm--5 {
    padding-bottom: 5px !important;
  }
  .pl_sm--5 {
    padding-left: 5px !important;
  }
  .pr_sm--5 {
    padding-right: 5px !important;
  }
  .mt_sm--5 {
    margin-top: 5px !important;
  }
  .ml_sm--5 {
    margin-left: 5px !important;
  }
  .mr_sm--5 {
    margin-right: 5px !important;
  }
  .mb_sm--5 {
    margin-bottom: 5px !important;
  }
  .ptb_sm--10 {
    padding: 10px 0 !important;
  }
  .plr_sm--10 {
    padding: 0 10px !important;
  }
  .pt_sm--10 {
    padding-top: 10px !important;
  }
  .pb_sm--10 {
    padding-bottom: 10px !important;
  }
  .pl_sm--10 {
    padding-left: 10px !important;
  }
  .pr_sm--10 {
    padding-right: 10px !important;
  }
  .mt_sm--10 {
    margin-top: 10px !important;
  }
  .ml_sm--10 {
    margin-left: 10px !important;
  }
  .mr_sm--10 {
    margin-right: 10px !important;
  }
  .mb_sm--10 {
    margin-bottom: 10px !important;
  }
  .ptb_sm--15 {
    padding: 15px 0 !important;
  }
  .plr_sm--15 {
    padding: 0 15px !important;
  }
  .pt_sm--15 {
    padding-top: 15px !important;
  }
  .pb_sm--15 {
    padding-bottom: 15px !important;
  }
  .pl_sm--15 {
    padding-left: 15px !important;
  }
  .pr_sm--15 {
    padding-right: 15px !important;
  }
  .mt_sm--15 {
    margin-top: 15px !important;
  }
  .ml_sm--15 {
    margin-left: 15px !important;
  }
  .mr_sm--15 {
    margin-right: 15px !important;
  }
  .mb_sm--15 {
    margin-bottom: 15px !important;
  }
  .ptb_sm--20 {
    padding: 20px 0 !important;
  }
  .plr_sm--20 {
    padding: 0 20px !important;
  }
  .pt_sm--20 {
    padding-top: 20px !important;
  }
  .pb_sm--20 {
    padding-bottom: 20px !important;
  }
  .pl_sm--20 {
    padding-left: 20px !important;
  }
  .pr_sm--20 {
    padding-right: 20px !important;
  }
  .mt_sm--20 {
    margin-top: 20px !important;
  }
  .ml_sm--20 {
    margin-left: 20px !important;
  }
  .mr_sm--20 {
    margin-right: 20px !important;
  }
  .mb_sm--20 {
    margin-bottom: 20px !important;
  }
  .ptb_sm--25 {
    padding: 25px 0 !important;
  }
  .plr_sm--25 {
    padding: 0 25px !important;
  }
  .pt_sm--25 {
    padding-top: 25px !important;
  }
  .pb_sm--25 {
    padding-bottom: 25px !important;
  }
  .pl_sm--25 {
    padding-left: 25px !important;
  }
  .pr_sm--25 {
    padding-right: 25px !important;
  }
  .mt_sm--25 {
    margin-top: 25px !important;
  }
  .ml_sm--25 {
    margin-left: 25px !important;
  }
  .mr_sm--25 {
    margin-right: 25px !important;
  }
  .mb_sm--25 {
    margin-bottom: 25px !important;
  }
  .ptb_sm--30 {
    padding: 30px 0 !important;
  }
  .plr_sm--30 {
    padding: 0 30px !important;
  }
  .pt_sm--30 {
    padding-top: 30px !important;
  }
  .pb_sm--30 {
    padding-bottom: 30px !important;
  }
  .pl_sm--30 {
    padding-left: 30px !important;
  }
  .pr_sm--30 {
    padding-right: 30px !important;
  }
  .mt_sm--30 {
    margin-top: 30px !important;
  }
  .ml_sm--30 {
    margin-left: 30px !important;
  }
  .mr_sm--30 {
    margin-right: 30px !important;
  }
  .mb_sm--30 {
    margin-bottom: 30px !important;
  }
  .ptb_sm--35 {
    padding: 35px 0 !important;
  }
  .plr_sm--35 {
    padding: 0 35px !important;
  }
  .pt_sm--35 {
    padding-top: 35px !important;
  }
  .pb_sm--35 {
    padding-bottom: 35px !important;
  }
  .pl_sm--35 {
    padding-left: 35px !important;
  }
  .pr_sm--35 {
    padding-right: 35px !important;
  }
  .mt_sm--35 {
    margin-top: 35px !important;
  }
  .ml_sm--35 {
    margin-left: 35px !important;
  }
  .mr_sm--35 {
    margin-right: 35px !important;
  }
  .mb_sm--35 {
    margin-bottom: 35px !important;
  }
  .ptb_sm--40 {
    padding: 40px 0 !important;
  }
  .plr_sm--40 {
    padding: 0 40px !important;
  }
  .pt_sm--40 {
    padding-top: 40px !important;
  }
  .pb_sm--40 {
    padding-bottom: 40px !important;
  }
  .pl_sm--40 {
    padding-left: 40px !important;
  }
  .pr_sm--40 {
    padding-right: 40px !important;
  }
  .mt_sm--40 {
    margin-top: 40px !important;
  }
  .ml_sm--40 {
    margin-left: 40px !important;
  }
  .mr_sm--40 {
    margin-right: 40px !important;
  }
  .mb_sm--40 {
    margin-bottom: 40px !important;
  }
  .ptb_sm--45 {
    padding: 45px 0 !important;
  }
  .plr_sm--45 {
    padding: 0 45px !important;
  }
  .pt_sm--45 {
    padding-top: 45px !important;
  }
  .pb_sm--45 {
    padding-bottom: 45px !important;
  }
  .pl_sm--45 {
    padding-left: 45px !important;
  }
  .pr_sm--45 {
    padding-right: 45px !important;
  }
  .mt_sm--45 {
    margin-top: 45px !important;
  }
  .ml_sm--45 {
    margin-left: 45px !important;
  }
  .mr_sm--45 {
    margin-right: 45px !important;
  }
  .mb_sm--45 {
    margin-bottom: 45px !important;
  }
  .ptb_sm--50 {
    padding: 50px 0 !important;
  }
  .plr_sm--50 {
    padding: 0 50px !important;
  }
  .pt_sm--50 {
    padding-top: 50px !important;
  }
  .pb_sm--50 {
    padding-bottom: 50px !important;
  }
  .pl_sm--50 {
    padding-left: 50px !important;
  }
  .pr_sm--50 {
    padding-right: 50px !important;
  }
  .mt_sm--50 {
    margin-top: 50px !important;
  }
  .ml_sm--50 {
    margin-left: 50px !important;
  }
  .mr_sm--50 {
    margin-right: 50px !important;
  }
  .mb_sm--50 {
    margin-bottom: 50px !important;
  }
  .ptb_sm--55 {
    padding: 55px 0 !important;
  }
  .plr_sm--55 {
    padding: 0 55px !important;
  }
  .pt_sm--55 {
    padding-top: 55px !important;
  }
  .pb_sm--55 {
    padding-bottom: 55px !important;
  }
  .pl_sm--55 {
    padding-left: 55px !important;
  }
  .pr_sm--55 {
    padding-right: 55px !important;
  }
  .mt_sm--55 {
    margin-top: 55px !important;
  }
  .ml_sm--55 {
    margin-left: 55px !important;
  }
  .mr_sm--55 {
    margin-right: 55px !important;
  }
  .mb_sm--55 {
    margin-bottom: 55px !important;
  }
  .ptb_sm--60 {
    padding: 60px 0 !important;
  }
  .plr_sm--60 {
    padding: 0 60px !important;
  }
  .pt_sm--60 {
    padding-top: 60px !important;
  }
  .pb_sm--60 {
    padding-bottom: 60px !important;
  }
  .pl_sm--60 {
    padding-left: 60px !important;
  }
  .pr_sm--60 {
    padding-right: 60px !important;
  }
  .mt_sm--60 {
    margin-top: 60px !important;
  }
  .ml_sm--60 {
    margin-left: 60px !important;
  }
  .mr_sm--60 {
    margin-right: 60px !important;
  }
  .mb_sm--60 {
    margin-bottom: 60px !important;
  }
  .ptb_sm--65 {
    padding: 65px 0 !important;
  }
  .plr_sm--65 {
    padding: 0 65px !important;
  }
  .pt_sm--65 {
    padding-top: 65px !important;
  }
  .pb_sm--65 {
    padding-bottom: 65px !important;
  }
  .pl_sm--65 {
    padding-left: 65px !important;
  }
  .pr_sm--65 {
    padding-right: 65px !important;
  }
  .mt_sm--65 {
    margin-top: 65px !important;
  }
  .ml_sm--65 {
    margin-left: 65px !important;
  }
  .mr_sm--65 {
    margin-right: 65px !important;
  }
  .mb_sm--65 {
    margin-bottom: 65px !important;
  }
  .ptb_sm--70 {
    padding: 70px 0 !important;
  }
  .plr_sm--70 {
    padding: 0 70px !important;
  }
  .pt_sm--70 {
    padding-top: 70px !important;
  }
  .pb_sm--70 {
    padding-bottom: 70px !important;
  }
  .pl_sm--70 {
    padding-left: 70px !important;
  }
  .pr_sm--70 {
    padding-right: 70px !important;
  }
  .mt_sm--70 {
    margin-top: 70px !important;
  }
  .ml_sm--70 {
    margin-left: 70px !important;
  }
  .mr_sm--70 {
    margin-right: 70px !important;
  }
  .mb_sm--70 {
    margin-bottom: 70px !important;
  }
  .ptb_sm--75 {
    padding: 75px 0 !important;
  }
  .plr_sm--75 {
    padding: 0 75px !important;
  }
  .pt_sm--75 {
    padding-top: 75px !important;
  }
  .pb_sm--75 {
    padding-bottom: 75px !important;
  }
  .pl_sm--75 {
    padding-left: 75px !important;
  }
  .pr_sm--75 {
    padding-right: 75px !important;
  }
  .mt_sm--75 {
    margin-top: 75px !important;
  }
  .ml_sm--75 {
    margin-left: 75px !important;
  }
  .mr_sm--75 {
    margin-right: 75px !important;
  }
  .mb_sm--75 {
    margin-bottom: 75px !important;
  }
  .ptb_sm--80 {
    padding: 80px 0 !important;
  }
  .plr_sm--80 {
    padding: 0 80px !important;
  }
  .pt_sm--80 {
    padding-top: 80px !important;
  }
  .pb_sm--80 {
    padding-bottom: 80px !important;
  }
  .pl_sm--80 {
    padding-left: 80px !important;
  }
  .pr_sm--80 {
    padding-right: 80px !important;
  }
  .mt_sm--80 {
    margin-top: 80px !important;
  }
  .ml_sm--80 {
    margin-left: 80px !important;
  }
  .mr_sm--80 {
    margin-right: 80px !important;
  }
  .mb_sm--80 {
    margin-bottom: 80px !important;
  }
  .ptb_sm--85 {
    padding: 85px 0 !important;
  }
  .plr_sm--85 {
    padding: 0 85px !important;
  }
  .pt_sm--85 {
    padding-top: 85px !important;
  }
  .pb_sm--85 {
    padding-bottom: 85px !important;
  }
  .pl_sm--85 {
    padding-left: 85px !important;
  }
  .pr_sm--85 {
    padding-right: 85px !important;
  }
  .mt_sm--85 {
    margin-top: 85px !important;
  }
  .ml_sm--85 {
    margin-left: 85px !important;
  }
  .mr_sm--85 {
    margin-right: 85px !important;
  }
  .mb_sm--85 {
    margin-bottom: 85px !important;
  }
  .ptb_sm--90 {
    padding: 90px 0 !important;
  }
  .plr_sm--90 {
    padding: 0 90px !important;
  }
  .pt_sm--90 {
    padding-top: 90px !important;
  }
  .pb_sm--90 {
    padding-bottom: 90px !important;
  }
  .pl_sm--90 {
    padding-left: 90px !important;
  }
  .pr_sm--90 {
    padding-right: 90px !important;
  }
  .mt_sm--90 {
    margin-top: 90px !important;
  }
  .ml_sm--90 {
    margin-left: 90px !important;
  }
  .mr_sm--90 {
    margin-right: 90px !important;
  }
  .mb_sm--90 {
    margin-bottom: 90px !important;
  }
  .ptb_sm--95 {
    padding: 95px 0 !important;
  }
  .plr_sm--95 {
    padding: 0 95px !important;
  }
  .pt_sm--95 {
    padding-top: 95px !important;
  }
  .pb_sm--95 {
    padding-bottom: 95px !important;
  }
  .pl_sm--95 {
    padding-left: 95px !important;
  }
  .pr_sm--95 {
    padding-right: 95px !important;
  }
  .mt_sm--95 {
    margin-top: 95px !important;
  }
  .ml_sm--95 {
    margin-left: 95px !important;
  }
  .mr_sm--95 {
    margin-right: 95px !important;
  }
  .mb_sm--95 {
    margin-bottom: 95px !important;
  }
  .ptb_sm--100 {
    padding: 100px 0 !important;
  }
  .plr_sm--100 {
    padding: 0 100px !important;
  }
  .pt_sm--100 {
    padding-top: 100px !important;
  }
  .pb_sm--100 {
    padding-bottom: 100px !important;
  }
  .pl_sm--100 {
    padding-left: 100px !important;
  }
  .pr_sm--100 {
    padding-right: 100px !important;
  }
  .mt_sm--100 {
    margin-top: 100px !important;
  }
  .ml_sm--100 {
    margin-left: 100px !important;
  }
  .mr_sm--100 {
    margin-right: 100px !important;
  }
  .mb_sm--100 {
    margin-bottom: 100px !important;
  }
  .ptb_sm--105 {
    padding: 105px 0 !important;
  }
  .plr_sm--105 {
    padding: 0 105px !important;
  }
  .pt_sm--105 {
    padding-top: 105px !important;
  }
  .pb_sm--105 {
    padding-bottom: 105px !important;
  }
  .pl_sm--105 {
    padding-left: 105px !important;
  }
  .pr_sm--105 {
    padding-right: 105px !important;
  }
  .mt_sm--105 {
    margin-top: 105px !important;
  }
  .ml_sm--105 {
    margin-left: 105px !important;
  }
  .mr_sm--105 {
    margin-right: 105px !important;
  }
  .mb_sm--105 {
    margin-bottom: 105px !important;
  }
  .ptb_sm--110 {
    padding: 110px 0 !important;
  }
  .plr_sm--110 {
    padding: 0 110px !important;
  }
  .pt_sm--110 {
    padding-top: 110px !important;
  }
  .pb_sm--110 {
    padding-bottom: 110px !important;
  }
  .pl_sm--110 {
    padding-left: 110px !important;
  }
  .pr_sm--110 {
    padding-right: 110px !important;
  }
  .mt_sm--110 {
    margin-top: 110px !important;
  }
  .ml_sm--110 {
    margin-left: 110px !important;
  }
  .mr_sm--110 {
    margin-right: 110px !important;
  }
  .mb_sm--110 {
    margin-bottom: 110px !important;
  }
  .ptb_sm--115 {
    padding: 115px 0 !important;
  }
  .plr_sm--115 {
    padding: 0 115px !important;
  }
  .pt_sm--115 {
    padding-top: 115px !important;
  }
  .pb_sm--115 {
    padding-bottom: 115px !important;
  }
  .pl_sm--115 {
    padding-left: 115px !important;
  }
  .pr_sm--115 {
    padding-right: 115px !important;
  }
  .mt_sm--115 {
    margin-top: 115px !important;
  }
  .ml_sm--115 {
    margin-left: 115px !important;
  }
  .mr_sm--115 {
    margin-right: 115px !important;
  }
  .mb_sm--115 {
    margin-bottom: 115px !important;
  }
  .ptb_sm--120 {
    padding: 120px 0 !important;
  }
  .plr_sm--120 {
    padding: 0 120px !important;
  }
  .pt_sm--120 {
    padding-top: 120px !important;
  }
  .pb_sm--120 {
    padding-bottom: 120px !important;
  }
  .pl_sm--120 {
    padding-left: 120px !important;
  }
  .pr_sm--120 {
    padding-right: 120px !important;
  }
  .mt_sm--120 {
    margin-top: 120px !important;
  }
  .ml_sm--120 {
    margin-left: 120px !important;
  }
  .mr_sm--120 {
    margin-right: 120px !important;
  }
  .mb_sm--120 {
    margin-bottom: 120px !important;
  }
  .ptb_sm--125 {
    padding: 125px 0 !important;
  }
  .plr_sm--125 {
    padding: 0 125px !important;
  }
  .pt_sm--125 {
    padding-top: 125px !important;
  }
  .pb_sm--125 {
    padding-bottom: 125px !important;
  }
  .pl_sm--125 {
    padding-left: 125px !important;
  }
  .pr_sm--125 {
    padding-right: 125px !important;
  }
  .mt_sm--125 {
    margin-top: 125px !important;
  }
  .ml_sm--125 {
    margin-left: 125px !important;
  }
  .mr_sm--125 {
    margin-right: 125px !important;
  }
  .mb_sm--125 {
    margin-bottom: 125px !important;
  }
  .ptb_sm--130 {
    padding: 130px 0 !important;
  }
  .plr_sm--130 {
    padding: 0 130px !important;
  }
  .pt_sm--130 {
    padding-top: 130px !important;
  }
  .pb_sm--130 {
    padding-bottom: 130px !important;
  }
  .pl_sm--130 {
    padding-left: 130px !important;
  }
  .pr_sm--130 {
    padding-right: 130px !important;
  }
  .mt_sm--130 {
    margin-top: 130px !important;
  }
  .ml_sm--130 {
    margin-left: 130px !important;
  }
  .mr_sm--130 {
    margin-right: 130px !important;
  }
  .mb_sm--130 {
    margin-bottom: 130px !important;
  }
  .ptb_sm--135 {
    padding: 135px 0 !important;
  }
  .plr_sm--135 {
    padding: 0 135px !important;
  }
  .pt_sm--135 {
    padding-top: 135px !important;
  }
  .pb_sm--135 {
    padding-bottom: 135px !important;
  }
  .pl_sm--135 {
    padding-left: 135px !important;
  }
  .pr_sm--135 {
    padding-right: 135px !important;
  }
  .mt_sm--135 {
    margin-top: 135px !important;
  }
  .ml_sm--135 {
    margin-left: 135px !important;
  }
  .mr_sm--135 {
    margin-right: 135px !important;
  }
  .mb_sm--135 {
    margin-bottom: 135px !important;
  }
  .ptb_sm--140 {
    padding: 140px 0 !important;
  }
  .plr_sm--140 {
    padding: 0 140px !important;
  }
  .pt_sm--140 {
    padding-top: 140px !important;
  }
  .pb_sm--140 {
    padding-bottom: 140px !important;
  }
  .pl_sm--140 {
    padding-left: 140px !important;
  }
  .pr_sm--140 {
    padding-right: 140px !important;
  }
  .mt_sm--140 {
    margin-top: 140px !important;
  }
  .ml_sm--140 {
    margin-left: 140px !important;
  }
  .mr_sm--140 {
    margin-right: 140px !important;
  }
  .mb_sm--140 {
    margin-bottom: 140px !important;
  }
  .ptb_sm--145 {
    padding: 145px 0 !important;
  }
  .plr_sm--145 {
    padding: 0 145px !important;
  }
  .pt_sm--145 {
    padding-top: 145px !important;
  }
  .pb_sm--145 {
    padding-bottom: 145px !important;
  }
  .pl_sm--145 {
    padding-left: 145px !important;
  }
  .pr_sm--145 {
    padding-right: 145px !important;
  }
  .mt_sm--145 {
    margin-top: 145px !important;
  }
  .ml_sm--145 {
    margin-left: 145px !important;
  }
  .mr_sm--145 {
    margin-right: 145px !important;
  }
  .mb_sm--145 {
    margin-bottom: 145px !important;
  }
  .ptb_sm--150 {
    padding: 150px 0 !important;
  }
  .plr_sm--150 {
    padding: 0 150px !important;
  }
  .pt_sm--150 {
    padding-top: 150px !important;
  }
  .pb_sm--150 {
    padding-bottom: 150px !important;
  }
  .pl_sm--150 {
    padding-left: 150px !important;
  }
  .pr_sm--150 {
    padding-right: 150px !important;
  }
  .mt_sm--150 {
    margin-top: 150px !important;
  }
  .ml_sm--150 {
    margin-left: 150px !important;
  }
  .mr_sm--150 {
    margin-right: 150px !important;
  }
  .mb_sm--150 {
    margin-bottom: 150px !important;
  }
  .ptb_sm--155 {
    padding: 155px 0 !important;
  }
  .plr_sm--155 {
    padding: 0 155px !important;
  }
  .pt_sm--155 {
    padding-top: 155px !important;
  }
  .pb_sm--155 {
    padding-bottom: 155px !important;
  }
  .pl_sm--155 {
    padding-left: 155px !important;
  }
  .pr_sm--155 {
    padding-right: 155px !important;
  }
  .mt_sm--155 {
    margin-top: 155px !important;
  }
  .ml_sm--155 {
    margin-left: 155px !important;
  }
  .mr_sm--155 {
    margin-right: 155px !important;
  }
  .mb_sm--155 {
    margin-bottom: 155px !important;
  }
  .ptb_sm--160 {
    padding: 160px 0 !important;
  }
  .plr_sm--160 {
    padding: 0 160px !important;
  }
  .pt_sm--160 {
    padding-top: 160px !important;
  }
  .pb_sm--160 {
    padding-bottom: 160px !important;
  }
  .pl_sm--160 {
    padding-left: 160px !important;
  }
  .pr_sm--160 {
    padding-right: 160px !important;
  }
  .mt_sm--160 {
    margin-top: 160px !important;
  }
  .ml_sm--160 {
    margin-left: 160px !important;
  }
  .mr_sm--160 {
    margin-right: 160px !important;
  }
  .mb_sm--160 {
    margin-bottom: 160px !important;
  }
  .ptb_sm--165 {
    padding: 165px 0 !important;
  }
  .plr_sm--165 {
    padding: 0 165px !important;
  }
  .pt_sm--165 {
    padding-top: 165px !important;
  }
  .pb_sm--165 {
    padding-bottom: 165px !important;
  }
  .pl_sm--165 {
    padding-left: 165px !important;
  }
  .pr_sm--165 {
    padding-right: 165px !important;
  }
  .mt_sm--165 {
    margin-top: 165px !important;
  }
  .ml_sm--165 {
    margin-left: 165px !important;
  }
  .mr_sm--165 {
    margin-right: 165px !important;
  }
  .mb_sm--165 {
    margin-bottom: 165px !important;
  }
  .ptb_sm--170 {
    padding: 170px 0 !important;
  }
  .plr_sm--170 {
    padding: 0 170px !important;
  }
  .pt_sm--170 {
    padding-top: 170px !important;
  }
  .pb_sm--170 {
    padding-bottom: 170px !important;
  }
  .pl_sm--170 {
    padding-left: 170px !important;
  }
  .pr_sm--170 {
    padding-right: 170px !important;
  }
  .mt_sm--170 {
    margin-top: 170px !important;
  }
  .ml_sm--170 {
    margin-left: 170px !important;
  }
  .mr_sm--170 {
    margin-right: 170px !important;
  }
  .mb_sm--170 {
    margin-bottom: 170px !important;
  }
  .ptb_sm--175 {
    padding: 175px 0 !important;
  }
  .plr_sm--175 {
    padding: 0 175px !important;
  }
  .pt_sm--175 {
    padding-top: 175px !important;
  }
  .pb_sm--175 {
    padding-bottom: 175px !important;
  }
  .pl_sm--175 {
    padding-left: 175px !important;
  }
  .pr_sm--175 {
    padding-right: 175px !important;
  }
  .mt_sm--175 {
    margin-top: 175px !important;
  }
  .ml_sm--175 {
    margin-left: 175px !important;
  }
  .mr_sm--175 {
    margin-right: 175px !important;
  }
  .mb_sm--175 {
    margin-bottom: 175px !important;
  }
  .ptb_sm--180 {
    padding: 180px 0 !important;
  }
  .plr_sm--180 {
    padding: 0 180px !important;
  }
  .pt_sm--180 {
    padding-top: 180px !important;
  }
  .pb_sm--180 {
    padding-bottom: 180px !important;
  }
  .pl_sm--180 {
    padding-left: 180px !important;
  }
  .pr_sm--180 {
    padding-right: 180px !important;
  }
  .mt_sm--180 {
    margin-top: 180px !important;
  }
  .ml_sm--180 {
    margin-left: 180px !important;
  }
  .mr_sm--180 {
    margin-right: 180px !important;
  }
  .mb_sm--180 {
    margin-bottom: 180px !important;
  }
  .ptb_sm--185 {
    padding: 185px 0 !important;
  }
  .plr_sm--185 {
    padding: 0 185px !important;
  }
  .pt_sm--185 {
    padding-top: 185px !important;
  }
  .pb_sm--185 {
    padding-bottom: 185px !important;
  }
  .pl_sm--185 {
    padding-left: 185px !important;
  }
  .pr_sm--185 {
    padding-right: 185px !important;
  }
  .mt_sm--185 {
    margin-top: 185px !important;
  }
  .ml_sm--185 {
    margin-left: 185px !important;
  }
  .mr_sm--185 {
    margin-right: 185px !important;
  }
  .mb_sm--185 {
    margin-bottom: 185px !important;
  }
  .ptb_sm--190 {
    padding: 190px 0 !important;
  }
  .plr_sm--190 {
    padding: 0 190px !important;
  }
  .pt_sm--190 {
    padding-top: 190px !important;
  }
  .pb_sm--190 {
    padding-bottom: 190px !important;
  }
  .pl_sm--190 {
    padding-left: 190px !important;
  }
  .pr_sm--190 {
    padding-right: 190px !important;
  }
  .mt_sm--190 {
    margin-top: 190px !important;
  }
  .ml_sm--190 {
    margin-left: 190px !important;
  }
  .mr_sm--190 {
    margin-right: 190px !important;
  }
  .mb_sm--190 {
    margin-bottom: 190px !important;
  }
  .ptb_sm--195 {
    padding: 195px 0 !important;
  }
  .plr_sm--195 {
    padding: 0 195px !important;
  }
  .pt_sm--195 {
    padding-top: 195px !important;
  }
  .pb_sm--195 {
    padding-bottom: 195px !important;
  }
  .pl_sm--195 {
    padding-left: 195px !important;
  }
  .pr_sm--195 {
    padding-right: 195px !important;
  }
  .mt_sm--195 {
    margin-top: 195px !important;
  }
  .ml_sm--195 {
    margin-left: 195px !important;
  }
  .mr_sm--195 {
    margin-right: 195px !important;
  }
  .mb_sm--195 {
    margin-bottom: 195px !important;
  }
  .ptb_sm--200 {
    padding: 200px 0 !important;
  }
  .plr_sm--200 {
    padding: 0 200px !important;
  }
  .pt_sm--200 {
    padding-top: 200px !important;
  }
  .pb_sm--200 {
    padding-bottom: 200px !important;
  }
  .pl_sm--200 {
    padding-left: 200px !important;
  }
  .pr_sm--200 {
    padding-right: 200px !important;
  }
  .mt_sm--200 {
    margin-top: 200px !important;
  }
  .ml_sm--200 {
    margin-left: 200px !important;
  }
  .mr_sm--200 {
    margin-right: 200px !important;
  }
  .mb_sm--200 {
    margin-bottom: 200px !important;
  }
  .pl_sm--0 {
    padding-left: 0;
  }
  .pr_sm--0 {
    padding-right: 0;
  }
  .pt_sm--0 {
    padding-top: 0;
  }
  .pb_sm--0 {
    padding-bottom: 0;
  }
  .mr_sm--0 {
    margin-right: 0;
  }
  .ml_sm--0 {
    margin-left: 0;
  }
  .mt_sm--0 {
    margin-top: 0;
  }
  .mb_sm--0 {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 575px) {
  .ptb_mobile--5 {
    padding: 5px 0 !important;
  }
  .plr_mobile--5 {
    padding: 0 5px !important;
  }
  .pt_mobile--5 {
    padding-top: 5px !important;
  }
  .pb_mobile--5 {
    padding-bottom: 5px !important;
  }
  .pl_mobile--5 {
    padding-left: 5px !important;
  }
  .pr_mobile--5 {
    padding-right: 5px !important;
  }
  .mt_mobile--5 {
    margin-top: 5px !important;
  }
  .mb_mobile--5 {
    margin-bottom: 5px !important;
  }
  .ptb_mobile--10 {
    padding: 10px 0 !important;
  }
  .plr_mobile--10 {
    padding: 0 10px !important;
  }
  .pt_mobile--10 {
    padding-top: 10px !important;
  }
  .pb_mobile--10 {
    padding-bottom: 10px !important;
  }
  .pl_mobile--10 {
    padding-left: 10px !important;
  }
  .pr_mobile--10 {
    padding-right: 10px !important;
  }
  .mt_mobile--10 {
    margin-top: 10px !important;
  }
  .mb_mobile--10 {
    margin-bottom: 10px !important;
  }
  .ptb_mobile--15 {
    padding: 15px 0 !important;
  }
  .plr_mobile--15 {
    padding: 0 15px !important;
  }
  .pt_mobile--15 {
    padding-top: 15px !important;
  }
  .pb_mobile--15 {
    padding-bottom: 15px !important;
  }
  .pl_mobile--15 {
    padding-left: 15px !important;
  }
  .pr_mobile--15 {
    padding-right: 15px !important;
  }
  .mt_mobile--15 {
    margin-top: 15px !important;
  }
  .mb_mobile--15 {
    margin-bottom: 15px !important;
  }
  .ptb_mobile--20 {
    padding: 20px 0 !important;
  }
  .plr_mobile--20 {
    padding: 0 20px !important;
  }
  .pt_mobile--20 {
    padding-top: 20px !important;
  }
  .pb_mobile--20 {
    padding-bottom: 20px !important;
  }
  .pl_mobile--20 {
    padding-left: 20px !important;
  }
  .pr_mobile--20 {
    padding-right: 20px !important;
  }
  .mt_mobile--20 {
    margin-top: 20px !important;
  }
  .mb_mobile--20 {
    margin-bottom: 20px !important;
  }
  .ptb_mobile--25 {
    padding: 25px 0 !important;
  }
  .plr_mobile--25 {
    padding: 0 25px !important;
  }
  .pt_mobile--25 {
    padding-top: 25px !important;
  }
  .pb_mobile--25 {
    padding-bottom: 25px !important;
  }
  .pl_mobile--25 {
    padding-left: 25px !important;
  }
  .pr_mobile--25 {
    padding-right: 25px !important;
  }
  .mt_mobile--25 {
    margin-top: 25px !important;
  }
  .mb_mobile--25 {
    margin-bottom: 25px !important;
  }
  .ptb_mobile--30 {
    padding: 30px 0 !important;
  }
  .plr_mobile--30 {
    padding: 0 30px !important;
  }
  .pt_mobile--30 {
    padding-top: 30px !important;
  }
  .pb_mobile--30 {
    padding-bottom: 30px !important;
  }
  .pl_mobile--30 {
    padding-left: 30px !important;
  }
  .pr_mobile--30 {
    padding-right: 30px !important;
  }
  .mt_mobile--30 {
    margin-top: 30px !important;
  }
  .mb_mobile--30 {
    margin-bottom: 30px !important;
  }
  .ptb_mobile--35 {
    padding: 35px 0 !important;
  }
  .plr_mobile--35 {
    padding: 0 35px !important;
  }
  .pt_mobile--35 {
    padding-top: 35px !important;
  }
  .pb_mobile--35 {
    padding-bottom: 35px !important;
  }
  .pl_mobile--35 {
    padding-left: 35px !important;
  }
  .pr_mobile--35 {
    padding-right: 35px !important;
  }
  .mt_mobile--35 {
    margin-top: 35px !important;
  }
  .mb_mobile--35 {
    margin-bottom: 35px !important;
  }
  .ptb_mobile--40 {
    padding: 40px 0 !important;
  }
  .plr_mobile--40 {
    padding: 0 40px !important;
  }
  .pt_mobile--40 {
    padding-top: 40px !important;
  }
  .pb_mobile--40 {
    padding-bottom: 40px !important;
  }
  .pl_mobile--40 {
    padding-left: 40px !important;
  }
  .pr_mobile--40 {
    padding-right: 40px !important;
  }
  .mt_mobile--40 {
    margin-top: 40px !important;
  }
  .mb_mobile--40 {
    margin-bottom: 40px !important;
  }
  .ptb_mobile--45 {
    padding: 45px 0 !important;
  }
  .plr_mobile--45 {
    padding: 0 45px !important;
  }
  .pt_mobile--45 {
    padding-top: 45px !important;
  }
  .pb_mobile--45 {
    padding-bottom: 45px !important;
  }
  .pl_mobile--45 {
    padding-left: 45px !important;
  }
  .pr_mobile--45 {
    padding-right: 45px !important;
  }
  .mt_mobile--45 {
    margin-top: 45px !important;
  }
  .mb_mobile--45 {
    margin-bottom: 45px !important;
  }
  .ptb_mobile--50 {
    padding: 50px 0 !important;
  }
  .plr_mobile--50 {
    padding: 0 50px !important;
  }
  .pt_mobile--50 {
    padding-top: 50px !important;
  }
  .pb_mobile--50 {
    padding-bottom: 50px !important;
  }
  .pl_mobile--50 {
    padding-left: 50px !important;
  }
  .pr_mobile--50 {
    padding-right: 50px !important;
  }
  .mt_mobile--50 {
    margin-top: 50px !important;
  }
  .mb_mobile--50 {
    margin-bottom: 50px !important;
  }
  .ptb_mobile--55 {
    padding: 55px 0 !important;
  }
  .plr_mobile--55 {
    padding: 0 55px !important;
  }
  .pt_mobile--55 {
    padding-top: 55px !important;
  }
  .pb_mobile--55 {
    padding-bottom: 55px !important;
  }
  .pl_mobile--55 {
    padding-left: 55px !important;
  }
  .pr_mobile--55 {
    padding-right: 55px !important;
  }
  .mt_mobile--55 {
    margin-top: 55px !important;
  }
  .mb_mobile--55 {
    margin-bottom: 55px !important;
  }
  .ptb_mobile--60 {
    padding: 60px 0 !important;
  }
  .plr_mobile--60 {
    padding: 0 60px !important;
  }
  .pt_mobile--60 {
    padding-top: 60px !important;
  }
  .pb_mobile--60 {
    padding-bottom: 60px !important;
  }
  .pl_mobile--60 {
    padding-left: 60px !important;
  }
  .pr_mobile--60 {
    padding-right: 60px !important;
  }
  .mt_mobile--60 {
    margin-top: 60px !important;
  }
  .mb_mobile--60 {
    margin-bottom: 60px !important;
  }
  .ptb_mobile--65 {
    padding: 65px 0 !important;
  }
  .plr_mobile--65 {
    padding: 0 65px !important;
  }
  .pt_mobile--65 {
    padding-top: 65px !important;
  }
  .pb_mobile--65 {
    padding-bottom: 65px !important;
  }
  .pl_mobile--65 {
    padding-left: 65px !important;
  }
  .pr_mobile--65 {
    padding-right: 65px !important;
  }
  .mt_mobile--65 {
    margin-top: 65px !important;
  }
  .mb_mobile--65 {
    margin-bottom: 65px !important;
  }
  .ptb_mobile--70 {
    padding: 70px 0 !important;
  }
  .plr_mobile--70 {
    padding: 0 70px !important;
  }
  .pt_mobile--70 {
    padding-top: 70px !important;
  }
  .pb_mobile--70 {
    padding-bottom: 70px !important;
  }
  .pl_mobile--70 {
    padding-left: 70px !important;
  }
  .pr_mobile--70 {
    padding-right: 70px !important;
  }
  .mt_mobile--70 {
    margin-top: 70px !important;
  }
  .mb_mobile--70 {
    margin-bottom: 70px !important;
  }
  .ptb_mobile--75 {
    padding: 75px 0 !important;
  }
  .plr_mobile--75 {
    padding: 0 75px !important;
  }
  .pt_mobile--75 {
    padding-top: 75px !important;
  }
  .pb_mobile--75 {
    padding-bottom: 75px !important;
  }
  .pl_mobile--75 {
    padding-left: 75px !important;
  }
  .pr_mobile--75 {
    padding-right: 75px !important;
  }
  .mt_mobile--75 {
    margin-top: 75px !important;
  }
  .mb_mobile--75 {
    margin-bottom: 75px !important;
  }
  .ptb_mobile--80 {
    padding: 80px 0 !important;
  }
  .plr_mobile--80 {
    padding: 0 80px !important;
  }
  .pt_mobile--80 {
    padding-top: 80px !important;
  }
  .pb_mobile--80 {
    padding-bottom: 80px !important;
  }
  .pl_mobile--80 {
    padding-left: 80px !important;
  }
  .pr_mobile--80 {
    padding-right: 80px !important;
  }
  .mt_mobile--80 {
    margin-top: 80px !important;
  }
  .mb_mobile--80 {
    margin-bottom: 80px !important;
  }
  .ptb_mobile--85 {
    padding: 85px 0 !important;
  }
  .plr_mobile--85 {
    padding: 0 85px !important;
  }
  .pt_mobile--85 {
    padding-top: 85px !important;
  }
  .pb_mobile--85 {
    padding-bottom: 85px !important;
  }
  .pl_mobile--85 {
    padding-left: 85px !important;
  }
  .pr_mobile--85 {
    padding-right: 85px !important;
  }
  .mt_mobile--85 {
    margin-top: 85px !important;
  }
  .mb_mobile--85 {
    margin-bottom: 85px !important;
  }
  .ptb_mobile--90 {
    padding: 90px 0 !important;
  }
  .plr_mobile--90 {
    padding: 0 90px !important;
  }
  .pt_mobile--90 {
    padding-top: 90px !important;
  }
  .pb_mobile--90 {
    padding-bottom: 90px !important;
  }
  .pl_mobile--90 {
    padding-left: 90px !important;
  }
  .pr_mobile--90 {
    padding-right: 90px !important;
  }
  .mt_mobile--90 {
    margin-top: 90px !important;
  }
  .mb_mobile--90 {
    margin-bottom: 90px !important;
  }
  .ptb_mobile--95 {
    padding: 95px 0 !important;
  }
  .plr_mobile--95 {
    padding: 0 95px !important;
  }
  .pt_mobile--95 {
    padding-top: 95px !important;
  }
  .pb_mobile--95 {
    padding-bottom: 95px !important;
  }
  .pl_mobile--95 {
    padding-left: 95px !important;
  }
  .pr_mobile--95 {
    padding-right: 95px !important;
  }
  .mt_mobile--95 {
    margin-top: 95px !important;
  }
  .mb_mobile--95 {
    margin-bottom: 95px !important;
  }
  .ptb_mobile--100 {
    padding: 100px 0 !important;
  }
  .plr_mobile--100 {
    padding: 0 100px !important;
  }
  .pt_mobile--100 {
    padding-top: 100px !important;
  }
  .pb_mobile--100 {
    padding-bottom: 100px !important;
  }
  .pl_mobile--100 {
    padding-left: 100px !important;
  }
  .pr_mobile--100 {
    padding-right: 100px !important;
  }
  .mt_mobile--100 {
    margin-top: 100px !important;
  }
  .mb_mobile--100 {
    margin-bottom: 100px !important;
  }
}
.mt-dec-30 {
  margin-top: -30px !important;
}

.mt_dec--30 {
  margin-top: -30px !important;
}

.mt-dec-100 {
  margin-top: -100px !important;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mb_dec--35 {
    margin-bottom: -50px;
  }
}

@media only screen and (max-width: 767px) {
  .mb_dec--35 {
    margin-bottom: -75px;
  }
}
@media only screen and (max-width: 575px) {
  .mb_dec--35 {
    margin-bottom: 0;
  }
}

.pb_xl--130 {
  padding-bottom: 100px;
}
@media only screen and (max-width: 1199px) {
  .pb_xl--130 {
    padding-bottom: 110px;
  }
}

.mt_dec--120 {
  margin-top: -130px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .plr_md--0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.g-24 {
  --bs-gutter-x: 24px;
  --bs-gutter-y: 24px;
}

.g-40 {
  --bs-gutter-x: 40px;
  --bs-gutter-y: 40px;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden] {
  display: none;
}

a {
  color: var(--color-heading);
  text-decoration: none;
  outline: none;
}

a:hover,
a:focus,
a:active {
  text-decoration: none;
  outline: none;
  color: var(--color-primary);
}

a:focus {
  outline: none;
}

address {
  margin: 0 0 24px;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

mark {
  background: var(--color-primary);
  color: #ffffff;
}

code,
kbd,
pre,
samp {
  font-size: var(--font-size-b3);
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  color: var(--color-primary);
}

kbd,
ins {
  color: #ffffff;
}

pre {
  font-family: "Raleway", sans-serif;
  font-size: var(--font-size-b3);
  margin: 10px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--color-body);
  background: var(--color-lighter);
}

small {
  font-size: smaller;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

dl {
  margin-top: 0;
  margin-bottom: 10px;
}

dd {
  margin: 0 15px 15px;
}

dt {
  font-weight: bold;
  color: var(--color-heading);
}

menu,
ol,
ul {
  margin: 16px 0;
  padding: 0 0 0 40px;
}

nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}

li > ul,
li > ol {
  margin: 0;
}

ol ul {
  margin-bottom: 0;
}

img {
  -ms-interpolation-mode: bicubic;
  border: 0;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 0;
}

form {
  margin: 0;
}

fieldset {
  border: 1px solid var(--color-border);
  margin: 0 2px;
  min-width: inherit;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
  white-space: normal;
}

button,
input,
select,
textarea {
  font-size: 100%;
  margin: 0;
  max-width: 100%;
  vertical-align: baseline;
}

button,
input {
  line-height: normal;
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
  -webkit-appearance: button;
  -moz-appearance: button;
  appearance: button;
  cursor: pointer;
}

button[disabled],
input[disabled] {
  cursor: default;
}

input[type=checkbox],
input[type=radio] {
  padding: 0;
}

input[type=search] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  appearance: textfield;
  padding-right: 2px;
  width: 270px;
}

input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
  appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
  vertical-align: top;
}

caption,
th,
td {
  font-weight: normal;
}

th {
  font-weight: 500;
  text-transform: uppercase;
}

td,
.wp-block-calendar tfoot td {
  border: 1px solid var(--color-border);
  padding: 7px 10px;
}

del {
  color: #333;
}

ins {
  background: rgba(255, 47, 47, 0.4);
  text-decoration: none;
}

hr {
  background-size: 4px 4px;
  border: 0;
  height: 1px;
  margin: 0 0 24px;
}

table a,
table a:link,
table a:visited {
  text-decoration: underline;
}

dt {
  font-weight: bold;
  margin-bottom: 10px;
}

dd {
  margin: 0 15px 15px;
}

caption {
  caption-side: top;
}

kbd {
  background: var(--heading-color);
}

dfn,
cite,
em {
  font-style: italic;
}

/* BlockQuote  */
blockquote,
q {
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

blockquote {
  font-size: var(--font-size-b1);
  font-style: italic;
  font-weight: var(--p-light);
  margin: 24px 40px;
}

blockquote blockquote {
  margin-right: 0;
}

blockquote cite,
blockquote small {
  font-size: var(--font-size-b3);
  font-weight: normal;
}

blockquote strong,
blockquote b {
  font-weight: 700;
}

/* ========= Forms Styles ========= */
input,
button,
select,
textarea {
  background: transparent;
  border: 1px solid var(--color-border);
  transition: all 0.4s ease-out 0s;
  color: var(--color-body);
  width: 100%;
}
input:focus, input:active,
button:focus,
button:active,
select:focus,
select:active,
textarea:focus,
textarea:active {
  outline: none;
  border-color: var(--color-primary);
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

input {
  height: 40px;
  padding: 0 15px;
}

input[type=text],
input[type=password],
input[type=email],
input[type=number],
input[type=tel],
textarea {
  font-size: var(--font-size-b2);
  font-weight: 400;
  height: auto;
  line-height: 28px;
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 0 15px;
  outline: none;
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--radius);
  /* -- Placeholder -- */
}
input[type=text]::placeholder,
input[type=password]::placeholder,
input[type=email]::placeholder,
input[type=number]::placeholder,
input[type=tel]::placeholder,
textarea::placeholder {
  color: var(--body-color);
  opacity: 1;
}
input[type=text]:-ms-input-placeholder,
input[type=password]:-ms-input-placeholder,
input[type=email]:-ms-input-placeholder,
input[type=number]:-ms-input-placeholder,
input[type=tel]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--body-color);
}
input[type=text]::-ms-input-placeholder,
input[type=password]::-ms-input-placeholder,
input[type=email]::-ms-input-placeholder,
input[type=number]::-ms-input-placeholder,
input[type=tel]::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--body-color);
}
input[type=text].p-holder__active, .input-active input[type=text], input[type=text].input-active,
input[type=password].p-holder__active,
.input-active input[type=password],
input[type=password].input-active,
input[type=email].p-holder__active,
.input-active input[type=email],
input[type=email].input-active,
input[type=number].p-holder__active,
.input-active input[type=number],
input[type=number].input-active,
input[type=tel].p-holder__active,
.input-active input[type=tel],
input[type=tel].input-active,
textarea.p-holder__active,
textarea.input-active {
  border-color: var(--color-primary);
  /* -- Placeholder -- */
}
input[type=text].p-holder__active::placeholder, .input-active input[type=text]::placeholder, input[type=text].input-active::placeholder,
input[type=password].p-holder__active::placeholder,
.input-active input[type=password]::placeholder,
input[type=password].input-active::placeholder,
input[type=email].p-holder__active::placeholder,
.input-active input[type=email]::placeholder,
input[type=email].input-active::placeholder,
input[type=number].p-holder__active::placeholder,
.input-active input[type=number]::placeholder,
input[type=number].input-active::placeholder,
input[type=tel].p-holder__active::placeholder,
.input-active input[type=tel]::placeholder,
input[type=tel].input-active::placeholder,
textarea.p-holder__active::placeholder,
textarea.input-active::placeholder {
  color: var(--color-primary);
  /* Firefox */
  opacity: 1;
}
input[type=text].p-holder__active:-ms-input-placeholder, .input-active input[type=text]:-ms-input-placeholder, input[type=text].input-active:-ms-input-placeholder,
input[type=password].p-holder__active:-ms-input-placeholder,
.input-active input[type=password]:-ms-input-placeholder,
input[type=password].input-active:-ms-input-placeholder,
input[type=email].p-holder__active:-ms-input-placeholder,
.input-active input[type=email]:-ms-input-placeholder,
input[type=email].input-active:-ms-input-placeholder,
input[type=number].p-holder__active:-ms-input-placeholder,
.input-active input[type=number]:-ms-input-placeholder,
input[type=number].input-active:-ms-input-placeholder,
input[type=tel].p-holder__active:-ms-input-placeholder,
.input-active input[type=tel]:-ms-input-placeholder,
input[type=tel].input-active:-ms-input-placeholder,
textarea.p-holder__active:-ms-input-placeholder,
textarea.input-active:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--color-primary);
}
input[type=text].p-holder__active::-ms-input-placeholder, .input-active input[type=text]::-ms-input-placeholder, input[type=text].input-active::-ms-input-placeholder,
input[type=password].p-holder__active::-ms-input-placeholder,
.input-active input[type=password]::-ms-input-placeholder,
input[type=password].input-active::-ms-input-placeholder,
input[type=email].p-holder__active::-ms-input-placeholder,
.input-active input[type=email]::-ms-input-placeholder,
input[type=email].input-active::-ms-input-placeholder,
input[type=number].p-holder__active::-ms-input-placeholder,
.input-active input[type=number]::-ms-input-placeholder,
input[type=number].input-active::-ms-input-placeholder,
input[type=tel].p-holder__active::-ms-input-placeholder,
.input-active input[type=tel]::-ms-input-placeholder,
input[type=tel].input-active::-ms-input-placeholder,
textarea.p-holder__active::-ms-input-placeholder,
textarea.input-active::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--color-primary);
}
input[type=text].p-holder__error, .input-error input[type=text], input[type=text].input-error,
input[type=password].p-holder__error,
.input-error input[type=password],
input[type=password].input-error,
input[type=email].p-holder__error,
.input-error input[type=email],
input[type=email].input-error,
input[type=number].p-holder__error,
.input-error input[type=number],
input[type=number].input-error,
input[type=tel].p-holder__error,
.input-error input[type=tel],
input[type=tel].input-error,
textarea.p-holder__error,
textarea.input-error {
  border-color: #f4282d;
  /* -- Placeholder -- */
}
input[type=text].p-holder__error::placeholder, .input-error input[type=text]::placeholder, input[type=text].input-error::placeholder,
input[type=password].p-holder__error::placeholder,
.input-error input[type=password]::placeholder,
input[type=password].input-error::placeholder,
input[type=email].p-holder__error::placeholder,
.input-error input[type=email]::placeholder,
input[type=email].input-error::placeholder,
input[type=number].p-holder__error::placeholder,
.input-error input[type=number]::placeholder,
input[type=number].input-error::placeholder,
input[type=tel].p-holder__error::placeholder,
.input-error input[type=tel]::placeholder,
input[type=tel].input-error::placeholder,
textarea.p-holder__error::placeholder,
textarea.input-error::placeholder {
  color: #f4282d;
  /* Firefox */
  opacity: 1;
}
input[type=text].p-holder__error:-ms-input-placeholder, .input-error input[type=text]:-ms-input-placeholder, input[type=text].input-error:-ms-input-placeholder,
input[type=password].p-holder__error:-ms-input-placeholder,
.input-error input[type=password]:-ms-input-placeholder,
input[type=password].input-error:-ms-input-placeholder,
input[type=email].p-holder__error:-ms-input-placeholder,
.input-error input[type=email]:-ms-input-placeholder,
input[type=email].input-error:-ms-input-placeholder,
input[type=number].p-holder__error:-ms-input-placeholder,
.input-error input[type=number]:-ms-input-placeholder,
input[type=number].input-error:-ms-input-placeholder,
input[type=tel].p-holder__error:-ms-input-placeholder,
.input-error input[type=tel]:-ms-input-placeholder,
input[type=tel].input-error:-ms-input-placeholder,
textarea.p-holder__error:-ms-input-placeholder,
textarea.input-error:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #f4282d;
}
input[type=text].p-holder__error::-ms-input-placeholder, .input-error input[type=text]::-ms-input-placeholder, input[type=text].input-error::-ms-input-placeholder,
input[type=password].p-holder__error::-ms-input-placeholder,
.input-error input[type=password]::-ms-input-placeholder,
input[type=password].input-error::-ms-input-placeholder,
input[type=email].p-holder__error::-ms-input-placeholder,
.input-error input[type=email]::-ms-input-placeholder,
input[type=email].input-error::-ms-input-placeholder,
input[type=number].p-holder__error::-ms-input-placeholder,
.input-error input[type=number]::-ms-input-placeholder,
input[type=number].input-error::-ms-input-placeholder,
input[type=tel].p-holder__error::-ms-input-placeholder,
.input-error input[type=tel]::-ms-input-placeholder,
input[type=tel].input-error::-ms-input-placeholder,
textarea.p-holder__error::-ms-input-placeholder,
textarea.input-error::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #f4282d;
}
input[type=text].p-holder__error:focus, .input-error input[type=text]:focus, input[type=text].input-error:focus,
input[type=password].p-holder__error:focus,
.input-error input[type=password]:focus,
input[type=password].input-error:focus,
input[type=email].p-holder__error:focus,
.input-error input[type=email]:focus,
input[type=email].input-error:focus,
input[type=number].p-holder__error:focus,
.input-error input[type=number]:focus,
input[type=number].input-error:focus,
input[type=tel].p-holder__error:focus,
.input-error input[type=tel]:focus,
input[type=tel].input-error:focus,
textarea.p-holder__error:focus,
textarea.input-error:focus {
  border-color: #f4282d;
}
input[type=text]:focus,
input[type=password]:focus,
input[type=email]:focus,
input[type=number]:focus,
input[type=tel]:focus,
textarea:focus {
  border-color: var(--color-primary);
}

input[type=checkbox],
input[type=radio] {
  opacity: 1;
  position: relative;
  height: auto !important;
  max-width: 18px;
  width: max-content;
}
input[type=checkbox] ~ label,
input[type=radio] ~ label {
  position: relative;
  font-size: 12px;
  line-height: 17px;
  color: var(--color-body);
  font-weight: 400;
  padding-left: 25px;
  cursor: pointer;
}
input[type=checkbox] ~ label::before,
input[type=radio] ~ label::before {
  content: " ";
  position: absolute;
  top: 1 px;
  left: 0;
  width: 15px;
  height: 15px;
  background-color: #5d5d7e;
  border-radius: 2px;
  transition: all 0.3s;
  border-radius: 2px;
}
input[type=checkbox] ~ label::after,
input[type=radio] ~ label::after {
  content: " ";
  position: absolute;
  top: 16%;
  left: 2px;
  width: 10px;
  height: 6px;
  background-color: transparent;
  border-bottom: 2px solid #ffffff;
  border-left: 2px solid #ffffff;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
input[type=checkbox]:checked ~ label::after,
input[type=radio]:checked ~ label::after {
  opacity: 1;
}

input:checked ~ .rn-check-box-label::before {
  background: var(--color-primary) !important;
}

input[type=radio] ~ label::before {
  border-radius: 50%;
}
input[type=radio] ~ label::after {
  width: 8px;
  height: 8px;
  left: 3px;
  background: #fff;
  border-radius: 50%;
}

.form-group {
  margin-bottom: 20px;
}
.form-group label {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
}
.form-group input {
  border: 0 none;
  border-radius: 4px;
  height: 50px;
  font-size: var(--font-size-b2);
  transition: var(--transition);
  padding: 0 20px;
  background-color: var(--color-lightest);
  border: 1px solid transparent;
  transition: var(--transition);
}
.form-group input:focus {
  border-color: var(--color-primary);
  box-shadow: none;
}
.form-group textarea {
  min-height: 160px;
  border: 0 none;
  border-radius: 4px;
  resize: none;
  padding: 15px;
  font-size: var(--font-size-b2);
  transition: var(--transition);
  background-color: var(--color-lightest);
  border: 1px solid transparent;
}
.form-group textarea:focus {
  border-color: var(--color-primary);
}

input[type=submit] {
  width: auto;
  padding: 0 30px;
  border-radius: 500px;
  display: inline-block;
  font-weight: 500;
  transition: 0.3s;
  height: 60px;
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: var(--p-medium);
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b3);
  height: 50px;
  border: 2px solid var(--color-primary);
  transition: var(--transition);
}
input[type=submit]:hover {
  background: transparent;
  color: var(--color-primary);
  transform: translateY(-5px);
}

/*==============================
 *  Utilities
=================================*/
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.fix {
  overflow: hidden;
}

.slick-initialized .slick-slide {
  margin-bottom: -10px;
}

.slick-gutter-15 {
  margin: -30px -15px;
}
.slick-gutter-15 .slick-slide {
  padding: 30px 15px;
}

iframe {
  width: 100%;
}

/*===============================
    Background Color
=================================*/
/*===========================
Background Image
=============================*/
.bg_image {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg_image--1 {
  background-image: url(../images/bg/bg-image-1.jpg);
}

.bg_image--2 {
  background-image: url(../images/bg/bg-image-2.jpg);
}

.bg_image--3 {
  background-image: url(../images/bg/bg-image-3.jpg);
}

.bg_image--4 {
  background-image: url(../images/bg/bg-image-4.jpg);
}

.bg_image--5 {
  background-image: url(../images/bg/bg-image-5.jpg);
}

.bg_image--6 {
  background-image: url(../images/bg/bg-image-6.jpg);
}

.bg_image--7 {
  background-image: url(../images/bg/bg-image-7.jpg);
}

.bg_image--8 {
  background-image: url(../images/bg/bg-image-8.jpg);
}

.bg_image--9 {
  background-image: url(../images/bg/bg-image-9.jpg);
}

.bg_image--10 {
  background-image: url(../images/bg/bg-image-10.jpg);
}

.bg_image--11 {
  background-image: url(../images/bg/bg-image-11.jpg);
}

.bg_image--12 {
  background-image: url(../images/bg/bg-image-12.jpg);
}

.bg_image--13 {
  background-image: url(../images/bg/bg-image-13.jpg);
}

.bg_image--14 {
  background-image: url(../images/bg/bg-image-14.jpg);
}

.bg_image--15 {
  background-image: url(../images/bg/bg-image-15.jpg);
}

.bg_image--16 {
  background-image: url(../images/bg/bg-image-16.jpg);
}

.bg_image--17 {
  background-image: url(../images/bg/bg-image-17.jpg);
}

.bg_image--18 {
  background-image: url(../images/bg/bg-image-18.jpg);
}

.bg_image--19 {
  background-image: url(../images/bg/bg-image-19.jpg);
}

.bg_image--20 {
  background-image: url(../images/bg/bg-image-20.jpg);
}

.bg_image--21 {
  background-image: url(../images/bg/bg-image-21.jpg);
}

.bg_image--22 {
  background-image: url(../images/bg/bg-image-22.jpg);
}

.bg_image--23 {
  background-image: url(../images/bg/bg-image-23.jpg);
}

.bg_image--24 {
  background-image: url(../images/bg/bg-image-24.jpg);
}

.bg_image--25 {
  background-image: url(../images/bg/bg-image-25.jpg);
}

.bg_image--26 {
  background-image: url(../images/bg/bg-image-26.jpg);
}

.bg_image--27 {
  background-image: url(../images/bg/bg-image-27.jpg);
}

.bg_image--28 {
  background-image: url(../images/bg/bg-image-28.jpg);
}

.bg_image--29 {
  background-image: url(../images/bg/bg-image-29.jpg);
}

.bg_image--30 {
  background-image: url(../images/bg/bg-image-30.jpg);
}

.bg_image--31 {
  background-image: url(../images/bg/bg-image-31.jpg);
}

.bg_image--32 {
  background-image: url(../images/bg/bg-image-32.jpg);
}

.bg_image--33 {
  background-image: url(../images/bg/bg-image-33.jpg);
}

.bg_image--34 {
  background-image: url(../images/bg/bg-image-34.jpg);
}

.bg_image--35 {
  background-image: url(../images/bg/bg-image-35.jpg);
}

.bg_image--36 {
  background-image: url(../images/bg/bg-image-36.jpg);
}

.bg_image--37 {
  background-image: url(../images/bg/bg-image-37.jpg);
}

.bg_image--38 {
  background-image: url(../images/bg/bg-image-38.jpg);
}

.bg_image--39 {
  background-image: url(../images/bg/bg-image-39.jpg);
}

.bg_image--40 {
  background-image: url(../images/bg/bg-image-40.jpg);
}

/*===================
Custom Row
======================*/
.row--0 {
  margin-left: 0px;
  margin-right: 0px;
}
.row--0 > [class*=col] {
  padding-left: 0px;
  padding-right: 0px;
}

.row--5 {
  margin-left: -5px;
  margin-right: -5px;
}
.row--5 > [class*=col] {
  padding-left: 5px;
  padding-right: 5px;
}

.row--10 {
  margin-left: -10px;
  margin-right: -10px;
}
.row--10 > [class*=col] {
  padding-left: 10px;
  padding-right: 10px;
}

.row--20 {
  margin-left: -20px;
  margin-right: -20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--20 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--20 > [class*=col], .row--20 > [class*=col-] {
  padding-left: 20px;
  padding-right: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--25 {
  margin-left: -25px;
  margin-right: -25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--25 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--25 > [class*=col], .row--25 > [class*=col-] {
  padding-left: 25px;
  padding-right: 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--30 {
  margin-left: -30px;
  margin-right: -30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--30 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--30 > [class*=col], .row--30 > [class*=col-] {
  padding-left: 30px;
  padding-right: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--45 {
  margin-left: -45px;
  margin-right: -45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--45 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--45 > [class*=col], .row--45 > [class*=col-] {
  padding-left: 45px;
  padding-right: 45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--40 {
  margin-left: -40px;
  margin-right: -40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--40 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--40 > [class*=col], .row--40 > [class*=col-] {
  padding-left: 40px;
  padding-right: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--60 {
  margin-left: -60px;
  margin-right: -60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--60 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--60 > [class*=col], .row--60 > [class*=col-] {
  padding-left: 60px;
  padding-right: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

/*===========================
    Input Placeholder
=============================*/
input:-moz-placeholder,
textarea:-moz-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

/*=============================
	Overlay styles
==============================*/
[data-overlay],
[data-black-overlay],
[data-white-overlay] {
  position: relative;
  z-index: 2;
}

[data-overlay] > div,
[data-overlay] > *,
[data-black-overlay] > div,
[data-black-overlay] > *,
[data-white-overlay] > div,
[data-white-overlay] > * {
  position: relative;
  z-index: 2;
}

[data-overlay]:before,
[data-black-overlay]:before,
[data-white-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
}

[data-overlay]:before {
  background: var(--color-primary);
}

[data-black-overlay]:before {
  background-color: #000000;
}

[data-white-overlay]:before {
  background-color: #ffffff;
}

[data-overlay="1"]:before,
[data-black-overlay="1"]:before,
[data-white-overlay="1"]:before {
  opacity: 0.1;
}

[data-overlay="2"]:before,
[data-black-overlay="2"]:before,
[data-white-overlay="2"]:before {
  opacity: 0.2;
}

[data-overlay="3"]:before,
[data-black-overlay="3"]:before,
[data-white-overlay="3"]:before {
  opacity: 0.3;
}

[data-overlay="4"]:before,
[data-black-overlay="4"]:before,
[data-white-overlay="4"]:before {
  opacity: 0.4;
}

[data-overlay="5"]:before,
[data-black-overlay="5"]:before,
[data-white-overlay="5"]:before {
  opacity: 0.5;
}

[data-overlay="6"]:before,
[data-black-overlay="6"]:before,
[data-white-overlay="6"]:before {
  opacity: 0.6;
}

[data-overlay="7"]:before,
[data-black-overlay="7"]:before,
[data-white-overlay="7"]:before {
  opacity: 0.7;
}

[data-overlay="8"]:before,
[data-black-overlay="8"]:before,
[data-white-overlay="8"]:before {
  opacity: 0.8;
}

[data-overlay="9"]:before,
[data-black-overlay="9"]:before,
[data-white-overlay="9"]:before {
  opacity: 0.9;
}

[data-overlay="10"]:before,
[data-black-overlay="10"]:before,
[data-white-overlay="10"]:before {
  opacity: 1;
}

/*!
Animate.css - http://daneden.me/animate
Version - 3.4.0
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animated.bounceIn,
.animated.bounceOut {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
}

.animated.flipOutX,
.animated.flipOutY {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
}

@-webkit-keyframes bounce {
  from, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
@keyframes bounce {
  from, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}

/*jump animation */
@keyframes jump-1 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-2 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  50% {
    -webkit-transform: translate3d(0, 30px, 0);
    transform: translate3d(0, 30px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-3 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 50px, 0) scale(0.7);
    transform: translate3d(0, 50px, 0) scale(0.7);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-4 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0) scale(0.8);
    transform: translate3d(0, 20px, 0) scale(0.8);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-5 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 10px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
@keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

@-webkit-keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse-2 {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.4, 1.4, 1.4);
    transform: scale3d(1.4, 1.4, 1.4);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
}

@-webkit-keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
}

@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
.swing {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-animation-name: swing;
  animation-name: swing;
}

@-webkit-keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.tada {
  -webkit-animation-name: tada;
  animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes wobble {
  from {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes wobble {
  from {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble;
}

@-webkit-keyframes jello {
  from, 11.1%, to {
    -webkit-transform: none;
    transform: none;
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
@keyframes jello {
  from, 11.1%, to {
    -webkit-transform: none;
    transform: none;
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
.jello {
  -webkit-animation-name: jello;
  animation-name: jello;
  -webkit-transform-origin: center;
  transform-origin: center;
}

@-webkit-keyframes bounceIn {
  from, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes bounceIn {
  from, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.bounceIn {
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}

@-webkit-keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight;
}

@-webkit-keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp;
}

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%, 55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%, 55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
.bounceOut {
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut;
}

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown;
}

@-webkit-keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft;
}

@-webkit-keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight;
}

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig;
}

@keyframes fadeInUp2 {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 40%, 0);
    transform: translate3d(0, 40%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
  transition: 0.2s;
}

@-webkit-keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig;
}

@-webkit-keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig;
}

@-webkit-keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight;
}

@-webkit-keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig;
}

@-webkit-keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig;
}

@-webkit-keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
@keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  -webkit-animation-name: flip;
  animation-name: flip;
}

@-webkit-keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
.flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
}

@-webkit-keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
.flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
}

@-webkit-keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
.flipOutX {
  -webkit-animation-name: flipOutX;
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
.flipOutY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipOutY;
  animation-name: flipOutY;
}

@-webkit-keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
}

@-webkit-keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
@keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-timing-function: ease-in;
}

@-webkit-keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn;
}

@-webkit-keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight;
}

@-webkit-keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
@keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut;
}

@-webkit-keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft;
}

@-webkit-keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight;
}

@-webkit-keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft;
}

@-webkit-keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight;
}

@-webkit-keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%, 80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
@keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%, 80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
.hinge {
  -webkit-animation-name: hinge;
  animation-name: hinge;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
@keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut;
}

@-webkit-keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown;
}

@-webkit-keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft;
}

@-webkit-keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight;
}

@-webkit-keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp;
}

@-webkit-keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

@-webkit-keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown;
}

@-webkit-keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft;
}

@-webkit-keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
@keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight;
}

@-webkit-keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp;
}

@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown;
}

@-webkit-keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInLeft2 {
  from {
    -webkit-transform: translate3d(-10%, 0, 0);
    transform: translate3d(-10%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft;
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

@-webkit-keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInUp2 {
  from {
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
    visibility: hidden;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    visibility: visible;
  }
}
@keyframes slideInUp3 {
  from {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
    visibility: hidden;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    visibility: visible;
  }
}
[data-aos=slideInUp2] {
  opacity: 0;
  transition-property: transform, opacity;
}
[data-aos=slideInUp2].aos-animate {
  opacity: 1;
}
@media screen and (min-width: 768px) {
  [data-aos=slideInUp2] {
    transform: translateY(30px);
  }
  [data-aos=slideInUp2].aos-animate {
    transform: translateY(0);
  }
}

.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp;
}

@-webkit-keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown;
}

@-webkit-keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft;
}

@-webkit-keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

@-webkit-keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp;
}

@keyframes jump-1 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-2 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  50% {
    -webkit-transform: translate3d(0, 30px, 0);
    transform: translate3d(0, 30px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes rotateIt {
  to {
    transform: rotate(-360deg);
  }
}
@keyframes rotateIt2 {
  to {
    transform: rotate(360deg);
  }
}
@keyframes shape-service-1 {
  0% {
    right: -40%;
    top: 30%;
  }
  100% {
    right: -23%;
    top: 0;
  }
}
@keyframes animate-floting {
  0% {
    transform: translateX(50%);
  }
  50% {
    transform: translateX(-40%);
  }
  100% {
    transform: translateX(40%);
  }
}
@keyframes animate-floting-2 {
  0% {
    transform: translateX(-50%);
  }
  50% {
    transform: translateX(40%);
  }
  100% {
    transform: translateX(-40%);
  }
}
@keyframes animate-floting-3 {
  0% {
    transform: translateX(-20%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-20%);
  }
}
.floting-line {
  animation: animate-floting 15s linear infinite;
}
.floting-line:hover {
  animation-play-state: paused;
}

.floting-line-2 {
  animation: animate-floting-2 15s linear infinite;
}
.floting-line-2:hover {
  animation-play-state: paused;
}

@keyframes waves {
  0% {
    -webkit-transform: scale(0.2, 0.2);
    transform: scale(0.2, 0.2);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
  50% {
    opacity: 0.9;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
  }
  100% {
    -webkit-transform: scale(0.9, 0.9);
    transform: scale(0.9, 0.9);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
}
@keyframes vsmorph {
  0% {
    border-radius: var(--morp-value);
  }
  50% {
    border-radius: var(--morp-md-value);
  }
  100% {
    border-radius: 40% 60%;
  }
}
@keyframes morpspin {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
.reveal-item {
  position: relative;
  display: block;
  overflow: hidden;
}

.reveal-item .reveal-animation {
  position: absolute;
  top: 0;
  width: 100%;
  height: 101%;
  background: var(--color-primary);
}

.reveal-item .reveal-animation::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bs-gray-400);
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-duration: 1s;
  transition-duration: 1s;
}

.reveal-animation.reveal-primary::before {
  background: #0c0c0a;
}

.reveal-animation.reveal-dark::before {
  background: #000;
}

.reveal-animation.reveal-white::before {
  background: #000;
}

.reveal-animation.reveal-top.aos-animate::before,
.reveal-animation.reveal-bottom.aos-animate::before {
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}

.reveal-animation.reveal-start.aos-animate::before,
.reveal-animation.reveal-end.aos-animate::before {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

.reveal-animation.reveal-top::before {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  -webkit-transform-origin: 0% 100%;
  transform-origin: 0% 100%;
}

.reveal-animation.reveal-start::before {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 100% 0%;
  transform-origin: 100% 0%;
}

.reveal-animation.reveal-end::before {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 0% 100%;
  transform-origin: 0% 100%;
}

.reveal-animation.reveal-bottom::before {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  -webkit-transform-origin: 100% 0%;
  transform-origin: 100% 0%;
}

[data-aos=reveal-top],
[data-aos=reveal-start],
[data-aos=reveal-end],
[data-aos=reveal-bottom] {
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-delay: 1s;
  transition-delay: 1s;
}

[data-aos=reveal-top] {
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}

[data-aos=reveal-top].aos-animate {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  -webkit-transform-origin: 100% 0%;
  transform-origin: 100% 0%;
}

[data-aos=reveal-start] {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

[data-aos=reveal-start].aos-animate {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 0% 100%;
  transform-origin: 0% 100%;
}

[data-aos=reveal-end] {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
}

[data-aos=reveal-end].aos-animate {
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: 100% 0%;
  transform-origin: 100% 0%;
}

[data-aos=reveal-bottom] {
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}

[data-aos=reveal-bottom].aos-animate {
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  -webkit-transform-origin: 0% 100%;
  transform-origin: 0% 100%;
}

[data-aos=reveal-item] {
  visibility: hidden;
  -webkit-transition-property: visibility;
  transition-property: visibility;
  -webkit-transition-duration: 0s;
  transition-duration: 0s;
}

[data-aos=reveal-item].aos-animate {
  visibility: visible;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(1.5, 1.5, 1.5);
    transform: scale3d(1.5, 1.5, 1.5);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.scaleIn {
  -webkit-animation-name: scaleIn;
  animation-name: scaleIn;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 30%, 0);
    transform: translate3d(0, 30%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, 10%, 0);
    transform: translate3d(0, 10%, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(30%, 0, 0);
    transform: translate3d(30%, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-30%, 0, 0);
    transform: translate3d(-30%, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.6, 0.6, 0.6);
    transform: scale3d(0.6, 0.6, 0.6);
  }
  50% {
    opacity: 1;
  }
}
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@keyframes moveright {
  0% {
    -webkit-clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
    clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
  }
  to {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
@keyframes moveLeft {
  0% {
    -webkit-clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
    clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
  }
  to {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
@keyframes moveOut {
  0% {
    -webkit-clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%);
    clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%);
  }
  100% {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
@keyframes toBottomLeft {
  0% {
    -webkit-clip-path: polygon(100% 0, 100% 0, 100% 0, 100% 0);
    clip-path: polygon(100% 0, 100% 0, 100% 0, 100% 0);
  }
  100% {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
@keyframes toTopRight {
  0% {
    -webkit-clip-path: polygon(0 100%, 0 100%, 0 100%, 0 100%);
    clip-path: polygon(0 100%, 0 100%, 0 100%, 0 100%);
  }
  100% {
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}
.move-right {
  animation: moveright 1s linear;
}

.move-left {
  animation: moveLeft 1s linear;
}

.move-out {
  animation: moveOut 1.5s linear;
}

.toBottomLeft {
  animation: toBottomLeft 1.5s linear;
}

.toTopRight {
  animation: toTopRight 1.5s linear;
}

/* Elements  */
.container {
  max-width: 1290px;
  margin: auto;
}
@media only screen and (max-width: 1199px) {
  .container {
    padding: 0 15px;
  }
}
@media only screen and (max-width: 991px) {
  .container {
    padding: 0 15px;
  }
}

.bg_nutral {
  background: #F6F6F6;
}

.float-right-style {
  width: calc(100% + 320px);
}

.bg-banner-three {
  background: #262626;
}

.title-main-wrapper-center-three {
  text-align: center;
  max-width: 785px;
  margin: auto;
}
.title-main-wrapper-center-three .title {
  font-size: 64px;
}
@media only screen and (max-width: 1199px) {
  .title-main-wrapper-center-three .title {
    font-size: 46px;
  }
}
@media only screen and (max-width: 991px) {
  .title-main-wrapper-center-three .title {
    font-size: 42px;
  }
}
@media only screen and (max-width: 767px) {
  .title-main-wrapper-center-three .title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 575px) {
  .title-main-wrapper-center-three .title {
    font-size: 28px;
  }
}
.title-main-wrapper-center-three p {
  font-size: 20px;
}

.title-area-center-inner-with-sub {
  text-align: center;
  max-width: 768px;
  margin: auto;
}
.title-area-center-inner-with-sub span {
  display: block;
  max-width: max-content;
  margin: auto;
  border: 1px solid #4948FF;
  padding: 4px 14px;
  border-radius: 33px;
  position: relative;
  z-index: 1;
}
.title-area-center-inner-with-sub span::after {
  position: absolute;
  content: "";
  left: 0;
  top: -8.5%;
  bottom: 0;
  right: 0;
  width: 104%;
  height: 130%;
  z-index: -1;
  background: linear-gradient(91deg, rgba(255, 255, 255, 0) 0%, #f8f9fa 100%);
}
.title-area-center-inner-with-sub .title {
  font-size: 64px;
  margin-bottom: 18px;
  margin-top: 13px;
}
@media only screen and (max-width: 767px) {
  .title-area-center-inner-with-sub .title {
    font-size: 44px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  .title-area-center-inner-with-sub .title {
    font-size: 36px;
    line-height: 1.3;
  }
}
.title-area-center-inner-with-sub p.disc {
  font-size: 20px;
  line-height: 1.5;
}

.header-style-one.border-bottm {
  border-bottom: 1px solid #D1D1D1;
}

.loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}
.loader-wrapper .loader-section {
  position: fixed;
  top: 0;
  background: var(--color-white);
  width: 50%;
  height: 100%;
  z-index: 1000;
}

.loader-wrapper .loader-section.section-left {
  left: 0;
}

.loader-wrapper .loader-section.section-right {
  right: 0;
}

/* Loaded Styles */
.loaded .loader-wrapper .loader-section.section-left {
  transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

.loaded .loader-wrapper .loader-section.section-right {
  transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

.loaded .loader {
  opacity: 0;
  transition: all 0.3s ease-out;
}

.loaded .loader-wrapper {
  visibility: hidden;
  transform: translateY(-100%);
  transition: all 0.3s 1s ease-out;
}

.loader:after {
  content: "";
  position: absolute;
  top: 14px;
  left: 14px;
  right: 14px;
  bottom: 14px;
  border: 4px solid transparent;
  border-top-color: var(--color-primary);
  border-right-color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  border-radius: 100%;
  -webkit-animation: spin 1.5s linear infinite;
  animation: spin 1.5s linear infinite;
}

.loader {
  display: block;
  position: relative;
  top: 50%;
  left: 50%;
  width: 70px;
  height: 70px;
  z-index: 1001;
  transform: translate(-50%, -50%);
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.rtl-ltr-switcher-btn {
  position: fixed;
  right: 0;
  top: 55%;
  left: auto;
  transform: translateY(-50%);
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  padding: 25px 6px;
  background: linear-gradient(-45deg, #7274ff, #3c9ae7, var(--color-primary), #001aff);
  background-size: 400% 400%;
  animation: gradient 5s ease infinite;
  z-index: 1000;
  color: #fff;
  cursor: pointer;
}
.rtl-ltr-switcher-btn .ltr,
.rtl-ltr-switcher-btn .rtl {
  display: none;
}
.rtl-ltr-switcher-btn .ltr.show,
.rtl-ltr-switcher-btn .rtl.show {
  display: block;
}
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.header-wrapper-1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 991px) {
  .header-wrapper-1 {
    padding: 20px 0;
    margin-bottom: 25px;
  }
}
@media only screen and (max-width: 575px) {
  .header-wrapper-1 {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 991px) {
  .header-wrapper-1 .nav-area {
    display: none;
  }
}

@media only screen and (max-width: 575px) {
  header .rts-btn {
    display: none;
  }
}

.header-style-one.style-two {
  border-bottom: 1px solid #D1D1D1;
}

@-webkit-keyframes stickyanimations {
  0% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
  }
  100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
@keyframes stickyanimations {
  0% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
  }
  100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
}
.header--sticky {
  z-index: 999;
  position: relative;
  width: 100%;
  background: transparent;
}

.header--sticky.sticky {
  position: fixed !important;
  width: 100%;
  -webkit-animation: stickyanimations 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: stickyanimations 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0px 7px 18px rgba(24, 16, 16, 0.0509803922);
  background: #fff;
}

.header-style-one.header--sticky {
  display: none;
}

.header-style-one.header--sticky.sticky {
  display: block;
}

.header-style-one.style-two.header--sticky {
  display: none;
}

.header-style-one.style-two.header--sticky.sticky {
  display: block;
}

.header-style-one.style-two {
  border-bottom: 1px solid #D1D1D1;
  position: relative;
  z-index: 99;
}

.header-area-four {
  position: absolute;
  width: 100%;
  z-index: 99;
}
.header-area-four .header-four-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 991px) {
  .header-area-four .header-four-wrapper {
    padding: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .header-area-four .header-four-wrapper {
    padding: 15px 10px;
  }
}
.header-area-four .header-four-wrapper .nav-area ul li > a.nav-link {
  color: #fff;
}
.header-area-four.sticky {
  background: #0B0A33;
}

.header-area-four.style-five .header-four-wrapper .nav-area ul li > a.nav-link {
  color: #0B0A33;
}

.header-area-four.style-five.sticky {
  background: #ffffff;
}

.rts-banner-area-five {
  position: relative;
  z-index: 1;
}
.rts-banner-area-five .round-shape-one img {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  top: 0;
  opacity: 1;
  width: 100%;
}
@-moz-document url-prefix() {
  .rts-banner-area-five .round-shape-one img {
    opacity: 0.15;
  }
}
.pagination-navigation-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pagination-navigation-wrapper .navigation .swiper-button-next,
.pagination-navigation-wrapper .navigation .swiper-button-prev {
  height: 48px;
  width: 48px;
  background: #EBEBFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pagination-navigation-wrapper .navigation .swiper-button-next i,
.pagination-navigation-wrapper .navigation .swiper-button-prev i {
  color: var(--color-primary);
  transition: 0.3s;
}
.pagination-navigation-wrapper .navigation .swiper-button-next::after,
.pagination-navigation-wrapper .navigation .swiper-button-prev::after {
  display: none;
}
.pagination-navigation-wrapper .navigation .swiper-button-next:hover,
.pagination-navigation-wrapper .navigation .swiper-button-prev:hover {
  background: var(--color-primary);
  transition: 0.3s;
}
.pagination-navigation-wrapper .navigation .swiper-button-next:hover i,
.pagination-navigation-wrapper .navigation .swiper-button-prev:hover i {
  color: #fff;
}
.pagination-navigation-wrapper .navigation .swiper-button-prev {
  left: 0;
}
.pagination-navigation-wrapper .navigation .swiper-button-next {
  right: auto;
  left: 58px;
}
.pagination-navigation-wrapper .pagination-2 {
  position: absolute;
  left: auto !important;
  right: 0 !important;
}
.pagination-navigation-wrapper .pagination-2 .swiper-pagination {
  position: relative;
}
@media only screen and (max-width: 575px) {
  .pagination-navigation-wrapper .pagination-2 .swiper-pagination {
    display: none;
  }
}
.pagination-navigation-wrapper .pagination-2 .swiper-pagination .swiper-pagination-bullet {
  display: inline-block;
  width: 87px;
  height: 4px;
  border-radius: 1px;
  margin-left: 16px;
}

.button-area-right-header {
  display: flex;
  align-items: center;
  gap: 20px;
}
.button-area-right-header .menu-btn-toggle {
  display: none;
}
@media only screen and (max-width: 991px) {
  .button-area-right-header .menu-btn-toggle {
    display: block;
  }
}

.header-area-four .button-area-right-header {
  display: none;
}
@media only screen and (max-width: 991px) {
  .header-area-four .button-area-right-header {
    display: block;
  }
  .header-area-four .button-area-right-header svg rect {
    fill: #fff;
  }
}
@media only screen and (max-width: 991px) {
  .header-area-four .nav-area {
    display: none;
  }
}

.header-area-four.style-five .menu-btn-toggle svg rect {
  fill: #0B0A33;
}

.menu-item-open > a {
  color: #5956FF !important;
}
.menu-item-open > a i::before {
  content: "\f077";
}
.menu-item-open li a.active {
  color: var(--color-primary);
}

.with-megamenu .submenu .single-menu li .industries.active {
  color: var(--color-primary);
}

.nav-area ul {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 40px;
}
.nav-area ul li {
  margin: 0;
  padding: 0;
}
.nav-area ul li a {
  padding: 38px 0;
  display: block;
  color: #000000;
  font-size: 16px;
  font-family: var(--font-medium);
}

.header--sticky.sticky .nav-area ul li > a.nav-link {
  padding: 20px 0;
}

.has-dropdown a {
  display: flex;
  align-items: center;
}
.has-dropdown a i::after {
  display: none;
}
.has-dropdown a i::before {
  top: -11px;
  font-size: 11px;
  right: -17px;
}
.has-dropdown:hover > a i::before {
  content: "\f077";
}

li.has-dropdown {
  position: relative;
}
li.has-dropdown .submenu {
  min-width: 230px;
  height: auto;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  text-align: left;
  transition: 0.3s;
  border-radius: 0 0 6px 6px;
  background-color: #fff;
  border-left: 0;
  border-bottom: 0;
  border-right: 0;
  display: inline-block;
  box-shadow: 0 36px 35px rgba(0, 0, 0, 0.08);
  padding: 0 0;
  transform-origin: 0 0;
  transform: scaleY(0);
}
li.has-dropdown .submenu li {
  margin-right: 0;
  padding: 0;
}
li.has-dropdown .submenu li a {
  padding: 9px 16px !important;
  font-weight: 400;
  font-size: 16px;
  transition: all 0.3s;
  border-radius: 0;
  display: block;
  display: flex;
  align-items: center;
  gap: 10px;
}
li.has-dropdown .submenu li a i {
  font-size: 11px;
  transition: 0.3s;
}
li.has-dropdown .submenu li a:hover {
  background: transparent;
  color: var(--color-primary);
  gap: 15px;
}
li.has-dropdown:hover .submenu {
  opacity: 1;
  visibility: visible;
  top: 100%;
  transform: translateY(0);
  transform: scaleY(1);
}

.rts-btn.btn-primary.border.bg-transparent {
  color: var(--color-primary);
  border: 1px solid var(--color-primary) !important;
}
.rts-btn.btn-primary.border.bg-transparent img {
  filter: brightness(0) saturate(100%) invert(46%) sepia(55%) saturate(5974%) hue-rotate(228deg) brightness(98%) contrast(103%);
}
.rts-btn.btn-primary.border.bg-transparent:hover {
  background: var(--color-primary) !important;
  color: #fff;
}
.rts-btn.btn-primary.border.bg-transparent:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(10%) saturate(7500%) hue-rotate(241deg) brightness(114%) contrast(108%);
}

.rts-mega-menu {
  position: absolute;
  width: 100%;
  height: auto;
  top: 100%;
  transform: scaleY(0);
  left: 0;
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  text-align: left;
  transition: all 0.3s;
  border-radius: 0 0 5px 5px !important;
  background-color: #ffffff;
  display: inline-block;
  box-shadow: 0 36px 35px rgba(61, 60, 60, 0.08);
  transform-origin: 0 0 0;
  padding: 30px 30px;
}
.rts-mega-menu.with-add {
  padding: 0;
  overflow: hidden;
  border-radius: 0 0 10px 10px;
}
.rts-mega-menu.with-add .menu-add-top-area {
  padding: 30px 0 25px 0;
  border-bottom: 1px solid #E1E1FF;
  margin-left: 50px;
}
.rts-mega-menu.with-add .menu-add-top-area .title {
  margin-bottom: 0;
  font-size: 24px;
  color: var(--color-primary);
}
.rts-mega-menu.with-add .menu-right-add {
  display: flex;
  justify-content: flex-end;
  align-items: end;
  max-width: max-content;
  margin-left: auto;
  position: relative;
  border-radius: 0 0 7px 0;
  overflow: hidden;
}
.rts-mega-menu.with-add .menu-right-add .absolute-image img {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
}
.rts-mega-menu.with-add .menu-right-add .absolute-image .inner-content {
  position: absolute;
  left: 23px;
  bottom: 23px;
}
.rts-mega-menu.with-add .menu-right-add .absolute-image .inner-content .title {
  color: #fff;
  font-size: 24px;
}
.rts-mega-menu.with-add .menu-right-add .absolute-image .inner-content .rts-btn {
  background: #fff;
  color: var(--color-primary);
}
.rts-mega-menu.with-add .mega-menu-item li a {
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%;
}
.rts-mega-menu.with-add .mega-menu-item li a img {
  max-width: 30px;
  height: auto;
  padding: 0;
  background: transparent;
  transition: 0.3s;
  margin-right: 5px;
}
.rts-mega-menu.with-add .mega-menu-item li a:hover img {
  filter: brightness(0) saturate(100%) invert(97%) sepia(3%) saturate(0%) hue-rotate(288deg) brightness(103%) contrast(100%);
}
.rts-mega-menu.with-add .mega-menu-item li a.active img {
  filter: brightness(0) saturate(100%) invert(97%) sepia(3%) saturate(0%) hue-rotate(288deg) brightness(103%) contrast(100%);
}

.mega-menu-item {
  padding: 0;
  margin: 0;
  flex-direction: column;
  display: flex;
  align-items: flex-start !important;
}
.mega-menu-item li {
  margin-bottom: 19px;
  margin-top: 0;
  margin-right: 0;
  margin-left: 0;
  width: 100%;
}
.mega-menu-item li:hover a {
  border: 1px solid #E6E5FF;
  border-radius: 4px;
}
.mega-menu-item li a {
  display: flex !important;
  align-items: center;
  padding: 6px 12px !important;
  border: 1px solid transparent;
  width: 90%;
}
.mega-menu-item li a img {
  margin-right: 16px;
  padding: 10px;
  max-width: max-content;
  background: #F0F0FF;
  border-radius: 4px;
}
.mega-menu-item li a .info p {
  margin-bottom: 2px;
  font-weight: 500;
  font-size: 16px;
  line-height: 26px;
  color: #083A5E;
}
.mega-menu-item li a .info span {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #497696;
}

.has-dropdown.mega-menu {
  position: static !important;
}

.has-dropdown.mega-menu:hover .rts-mega-menu {
  opacity: 1;
  visibility: visible;
  top: 100%;
  transform: scaleY(1);
}

.container-full-header .rts-mega-menu {
  transform: translateX(-50%) scaleY(0);
  left: 50%;
  max-width: 80%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .container-full-header .rts-mega-menu {
    max-width: 90%;
  }
}
.container-full-header .has-dropdown.mega-menu:hover .rts-mega-menu {
  transform: translateX(-50%) scaleY(1);
}

.has-dropdown.mega-menu:hover > a::after {
  content: "\f077";
  color: var(--color-primary);
}

.appoinment-area-main.contact-page {
  border-radius: 10px;
  background: #F9F8FF;
  border: 1px solid #DDD8F9;
  background-image: none;
}
.appoinment-area-main.contact-page input,
.appoinment-area-main.contact-page textarea,
.appoinment-area-main.contact-page .custom-select {
  color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}
.appoinment-area-main.contact-page input span,
.appoinment-area-main.contact-page textarea span,
.appoinment-area-main.contact-page .custom-select span {
  color: var(--color-primary) !important;
}
.appoinment-area-main.contact-page .rts-btn {
  background: var(--color-primary);
  color: #fff;
}

.rts-btn {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  max-width: max-content;
  transition: 0.3s;
  z-index: 1;
  border-radius: 10px !important;
}

.rts-btn:focus {
  box-shadow: none;
}
.rts-btn img {
  max-width: 24px;
  height: auto;
}
.rts-btn.btn-bold {
  height: 48px;
}
.rts-btn.btn-border {
  border: 1px solid #D1D1D1;
  gap: 44px;
  color: #262626;
  font-weight: 400;
  position: relative;
  overflow: hidden;
}
.rts-btn.btn-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s, border 0.5s;
}
.rts-btn.btn-border::after {
  content: "";
  position: absolute;
  top: 1%;
  left: 0%;
  width: 200px;
  height: 200px;
  background-color: #0077AD;
  border-color: transparent;
  border-radius: 50%;
  transform: translate(-10px, -70px) scale(0.1);
  opacity: 0;
  z-index: -1;
  transition: transform 0.5s, opacity 0.5s, background-color 0.5s;
}
.rts-btn.btn-border:hover {
  color: #fff;
  border: 1px solid transparent;
}
.rts-btn.btn-border:hover ::before {
  opacity: 0;
}
.rts-btn.btn-border:hover::after {
  opacity: 1;
  transform: scaleX(1.5) scaleY(1.5);
}
.rts-btn.btn-border:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(136deg) brightness(120%) contrast(107%);
}
.rts-btn.btn-primary {
  padding: 17px 15px;
  background: var(--color-primary);
  gap: 31px;
  height: 48px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-primary);
}
.rts-btn.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s, border 0.5s;
}
.rts-btn.btn-primary::after {
  content: "";
  position: absolute;
  top: 1%;
  left: 0%;
  width: 200px;
  height: 200px;
  background-color: #fff;
  border-color: transparent;
  border-radius: 50%;
  transform: translate(-10px, -70px) scale(0.1);
  opacity: 0;
  z-index: -1;
  transition: transform 0.5s, opacity 0.5s, background-color 0.5s;
}
.rts-btn.btn-primary:hover {
  color: var(--color-primary);
}
.rts-btn.btn-primary:hover ::before {
  opacity: 0;
}
.rts-btn.btn-primary:hover::after {
  opacity: 1;
  transform: scaleX(1.5) scaleY(1.5);
}
.rts-btn.btn-primary:hover svg path {
  fill: var(--color-primary) !important;
  stroke: var(--color-primary);
}

.arrow-btn img {
  max-width: 22px;
  height: auto;
}

.btn-border svg {
  max-width: 24px;
}
.btn-border svg path {
  stroke: #262626;
}
.btn-border:hover svg path {
  stroke: #fff;
}

.banner-wrapper-one .title {
  font-weight: 400;
  font-size: 80px;
  line-height: 90%;
  margin-bottom: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .banner-wrapper-one .title {
    line-height: 1.2;
  }
}
@media only screen and (max-width: 1199px) {
  .banner-wrapper-one .title {
    line-height: 1.2;
    font-size: 60px;
  }
}
@media only screen and (max-width: 575px) {
  .banner-wrapper-one .title {
    font-size: 48px;
  }
}
.banner-wrapper-one .title span {
  color: var(--color-primary);
}
.banner-wrapper-one span.pre-title {
  padding: 5px 20px;
  border: 1px solid #D1D1D1;
  display: block;
  max-width: max-content;
  border-radius: 33px;
  font-size: 14px;
  color: #000;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}
.banner-wrapper-one span.pre-title::after {
  position: absolute;
  content: "";
  left: 0;
  top: -8.5%;
  bottom: 0;
  right: 0;
  width: 104%;
  height: 130%;
  z-index: -1;
  background: linear-gradient(91deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
}
.banner-wrapper-one p {
  max-width: 90%;
  color: #262626;
  line-height: 1.5;
  font-size: 20px;
  margin-bottom: 45px;
}
@media only screen and (max-width: 1199px) {
  .banner-wrapper-one p {
    max-width: 100%;
  }
}

.right-clippath-wrapper {
  margin-right: -330px;
  margin-left: 130px;
  gap: 12px;
}
@media only screen and (min-width: 1600px) and (max-width: 1919px) {
  .right-clippath-wrapper {
    margin-left: 50px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .right-clippath-wrapper {
    margin-right: -137px;
    margin-left: 0;
  }
}
@media only screen and (max-width: 1199px) {
  .right-clippath-wrapper {
    margin-right: -60px;
    margin-left: 0;
  }
}
@media only screen and (max-width: 991px) {
  .right-clippath-wrapper {
    margin-right: 0;
  }
}
.right-clippath-wrapper .left-image {
  clip-path: polygon(0% 3.69%, 100% 0%, 100% 96.31%, 0% 100%, 0% 3.69%);
}
.right-clippath-wrapper .jarallax {
  height: 450px;
}
.right-clippath-wrapper .jara-mask-1 {
  height: 642px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .right-clippath-wrapper .jara-mask-1 {
    height: 500px;
  }
}
@media only screen and (max-width: 1199px) {
  .right-clippath-wrapper .jara-mask-1 {
    height: 409px;
  }
}
@media only screen and (max-width: 1199px) {
  .right-clippath-wrapper .jara-mask-1 {
    height: 329px;
  }
}

.jarallax {
  position: relative;
  z-index: 0;
}

.jara-mask-1 {
  width: 100%;
}

.right-clippath-wrapper {
  position: relative;
}
@media only screen and (max-width: 767px) {
  .right-clippath-wrapper {
    flex-direction: column;
    align-items: center;
  }
}
.right-clippath-wrapper .shape-image .one {
  position: absolute;
  left: -190px;
  bottom: 0;
  z-index: -1;
}
@media only screen and (max-width: 1199px) {
  .right-clippath-wrapper .shape-image .one {
    display: none;
  }
}
.right-clippath-wrapper .shape-image .two {
  position: absolute;
  right: 0;
  bottom: -100px;
  z-index: -1;
}

.button-wrapper {
  display: flex;
  align-items: center;
  gap: 25px;
  justify-content: center;
}
@media only screen and (max-width: 1199px) {
  .button-wrapper {
    flex-wrap: wrap;
  }
}
@media only screen and (max-width: 575px) {
  .button-wrapper {
    flex-wrap: wrap;
  }
}

.rts-banner-area-two {
  text-align: center;
  max-width: 770px;
  margin: auto;
}
.rts-banner-area-two span {
  border: 1px solid #E2E2E2;
  display: block;
  max-width: max-content;
  padding: 6px 19px;
  margin: auto;
  border-radius: 32px;
  position: relative;
  z-index: 1;
}
.rts-banner-area-two span::after {
  position: absolute;
  content: "";
  left: 0;
  top: -8.5%;
  bottom: 0;
  right: 0;
  width: 104%;
  height: 130%;
  z-index: -1;
  background: linear-gradient(91deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
}
.rts-banner-area-two .title {
  margin-bottom: 45px;
  font-weight: 400;
  font-size: 80px;
  line-height: 1;
  margin-top: 17px;
}
@media only screen and (max-width: 1199px) {
  .rts-banner-area-two .title {
    font-size: 60px;
  }
}
@media only screen and (max-width: 991px) {
  .rts-banner-area-two .title {
    font-size: 56px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  .rts-banner-area-two .title {
    font-size: 30px;
  }
}
@media only screen and (max-width: 479px) {
  .rts-banner-area-two .title {
    font-size: 30px;
  }
}
.rts-banner-area-two .rts-btn {
  height: 48px;
}

.rts-banner-area-style-narrow .shape-top-right {
  position: absolute;
  right: 0;
  top: 0;
}
@media only screen and (max-width: 991px) {
  .rts-banner-area-style-narrow .shape-top-right {
    display: none;
  }
}
.rts-banner-area-style-narrow .shape-bottom-left {
  position: absolute;
  left: 0;
  top: 326px;
}
@media only screen and (max-width: 991px) {
  .rts-banner-area-style-narrow .shape-bottom-left {
    display: none;
  }
}

.image-banner-cottom-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}
@media only screen and (max-width: 767px) {
  .image-banner-cottom-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
}
.image-banner-cottom-wrapper .single-leftt-large-iamge {
  clip-path: polygon(0% 6.536%, 100% 0%, 100% 100%, 0% 100%, 0% 6.536%);
  height: 752px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .image-banner-cottom-wrapper .single-leftt-large-iamge {
    height: 457px;
  }
}
@media only screen and (max-width: 1199px) {
  .image-banner-cottom-wrapper .single-leftt-large-iamge {
    height: 514px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .image-banner-cottom-wrapper .single-leftt-large-iamge {
    height: auto;
  }
}
@media only screen and (max-width: 767px) {
  .image-banner-cottom-wrapper .single-leftt-large-iamge {
    height: 385px;
  }
}
@media only screen and (max-width: 575px) {
  .image-banner-cottom-wrapper .single-leftt-large-iamge {
    height: 230px;
  }
}
.image-banner-cottom-wrapper .single-right-large-iamge {
  clip-path: polygon(0% 4.902%, 100% 0%, 100% 100%, 0% 100%, 0% 4.902%);
  height: 752px;
  max-width: 604px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .image-banner-cottom-wrapper .single-right-large-iamge {
    height: 507px;
    max-width: 521px;
  }
}
@media only screen and (max-width: 1199px) {
  .image-banner-cottom-wrapper .single-right-large-iamge {
    height: 507px;
    max-width: 316px;
  }
}
@media only screen and (max-width: 767px) {
  .image-banner-cottom-wrapper .single-right-large-iamge {
    max-width: 100%;
  }
}

.banner-three-wrapper * {
  color: #fff;
}
.banner-three-wrapper .pre-title {
  padding: 3px 15px;
  display: block;
  max-width: max-content;
  border-radius: 32px;
  border: 1px solid rgba(133, 133, 253, 0.1215686275);
}
.banner-three-wrapper .title {
  font-size: 80px;
  line-height: 90%;
  margin-top: 25px;
}
@media only screen and (max-width: 1199px) {
  .banner-three-wrapper .title {
    font-size: 50px;
    margin-top: 25px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 767px) {
  .banner-three-wrapper .title {
    font-size: 38px;
  }
}
@media only screen and (max-width: 767px) {
  .banner-three-wrapper .title {
    font-size: 32px;
  }
}

.mt-dec-banner-3 {
  margin-top: -350px;
}
@media only screen and (max-width: 991px) {
  .mt-dec-banner-3 {
    margin-top: -280px;
  }
}

.rts-banner-three-area {
  position: relative;
  z-index: 1;
}
.rts-banner-three-area .round-shape {
  position: absolute;
  top: -120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  opacity: 0.2;
}
.rts-banner-three-area .right-shape {
  position: absolute;
  right: 0;
  top: 38%;
}
@media only screen and (max-width: 1199px) {
  .rts-banner-three-area .right-shape {
    display: none;
  }
}
.rts-banner-three-area .top-shape {
  position: absolute;
  top: 0;
  left: 41%;
}

.banner-four-bg {
  background-image: url(../images/banner/04.webp);
  height: 853px !important;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
@media only screen and (max-width: 767px) {
  .banner-four-bg {
    height: 853px !important;
  }
}

.banner-four-area {
  display: flex;
  align-items: center;
}
.banner-four-area .banner-four-wrapper .inner-left {
  max-width: 992px;
}
.banner-four-area .banner-four-wrapper .title {
  font-size: 80px;
  color: #fff;
  line-height: 1;
}
@media only screen and (max-width: 991px) {
  .banner-four-area .banner-four-wrapper .title {
    font-size: 52px;
  }
}
@media only screen and (max-width: 767px) {
  .banner-four-area .banner-four-wrapper .title {
    font-size: 44px;
    line-height: 1.4;
  }
}
.banner-four-area .banner-four-wrapper p.disc {
  color: #fff;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.5;
}
@media only screen and (max-width: 767px) {
  .banner-four-area .banner-four-wrapper p.disc {
    font-size: 18px;
  }
}

.solution-exparts-area-service-page .title-area-center-inner-with-sub span::after {
  background: linear-gradient(91deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
}

.banner-four-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .banner-four-wrapper {
    flex-direction: column;
    gap: 25px;
    align-items: flex-start;
  }
}
.banner-four-wrapper .right-inner-button a {
  overflow: hidden;
  height: 100px;
  width: 100px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.banner-four-wrapper .right-inner-button a img {
  transition: 0.3s;
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
}
.banner-four-wrapper .right-inner-button a:hover {
  background: #fff;
}
.banner-four-wrapper .right-inner-button a:hover img {
  transform: scale(1.2);
  filter: brightness(0) saturate(100%) invert(0%) sepia(6%) saturate(8%) hue-rotate(345deg) brightness(102%) contrast(100%);
}

.banner-four-area {
  position: relative;
}
.banner-four-area .shape {
  position: absolute;
  right: 270px;
  bottom: 53px;
}

.banner-inner-wrapper-five {
  text-align: center;
  max-width: 780px;
  margin: auto;
  padding-top: 50px;
}
.banner-inner-wrapper-five .title {
  font-size: 96px;
  font-weight: 400;
  color: #262626;
  line-height: 1;
}
@media only screen and (max-width: 991px) {
  .banner-inner-wrapper-five .title {
    font-size: 70px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 767px) {
  .banner-inner-wrapper-five .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 575px) {
  .banner-inner-wrapper-five .title {
    font-size: 40px;
  }
}
.banner-inner-wrapper-five .title span {
  color: var(--color-primary);
}
.banner-inner-wrapper-five p.disc {
  max-width: 85%;
  margin: auto;
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 45px;
}
@media only screen and (max-width: 575px) {
  .banner-inner-wrapper-five p.disc {
    max-width: 100%;
    font-size: 16px;
  }
}
.banner-inner-wrapper-five .rts-btn {
  margin: auto;
}

.bg-gradient-6 {
  background: linear-gradient(180deg, #F5F5FF 0%, rgba(255, 255, 255, 0) 100%);
}

.active-pricing-5 {
  background: var(--color-primary);
  position: relative;
}
.active-pricing-5 .tag {
  position: absolute;
  right: 16px;
  top: 16px;
}
.active-pricing-5 .single-check img {
  filter: brightness(0) saturate(100%) invert(88%) sepia(73%) saturate(0%) hue-rotate(212deg) brightness(106%) contrast(102%);
}
.active-pricing-5 * {
  color: #fff;
}
.active-pricing-5 .rts-btn {
  background: #fff;
}

.rts-service-banner-area .shape-area-start img {
  position: absolute;
  pointer-events: none;
}
@media only screen and (max-width: 767px) {
  .rts-service-banner-area .shape-area-start img {
    display: none;
  }
}
.rts-service-banner-area .shape-area-start img.one {
  left: 30%;
  top: 0;
}
.rts-service-banner-area .shape-area-start img.two {
  right: 44%;
  top: 69%;
}

.service-single-area-banner {
  background-image: url(../images/service/06.webp);
  height: 300px !important;
}
@media only screen and (max-width: 575px) {
  .service-single-area-banner {
    height: 400px !important;
  }
}
.service-single-area-banner.it-strategies {
  background-image: url(../images/service/11.webp);
}
.service-single-area-banner.cyber-security {
  background-image: url(../images/service/12.webp);
}
.service-single-area-banner.technology-service {
  background-image: url(../images/service/13.webp);
}
.service-single-area-banner.it-service {
  background-image: url(../images/service/14.webp);
}
.service-single-area-banner.development-service {
  background-image: url(../images/service/15.webp);
}
.service-single-area-banner.ai-learning-service {
  background-image: url(../images/service/02.webp);
}
.service-single-area-banner.management-service {
  background-image: url(../images/service/17.webp);
}
.service-single-area-banner.it-innovations {
  background-image: url(../images/service/18.webp);
}

.case-studies-banner-top {
  padding: 80px 0;
  text-align: center;
}
.case-studies-banner-top .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .case-studies-banner-top .title {
    font-size: 46px;
  }
}
.case-studies-banner-top p {
  margin: 0;
  font-size: 20px;
}

.rts-case-studies-banner-area {
  position: relative;
}
.rts-case-studies-banner-area .shape-left-top {
  position: absolute;
  top: 0;
  left: 0;
}
@media only screen and (max-width: 575px) {
  .rts-case-studies-banner-area .shape-left-top {
    display: none;
  }
}

.banner-inner-why-choose-us {
  max-width: 60%;
  margin: auto;
  padding: 100px 0 60px 0;
  text-align: center;
}
@media only screen and (max-width: 575px) {
  .banner-inner-why-choose-us {
    padding: 30px 0 25px 0;
    max-width: 100%;
  }
}

.thumbnail-banner-choose-us {
  height: 500px !important;
}
@media only screen and (max-width: 575px) {
  .thumbnail-banner-choose-us {
    height: 300px !important;
  }
}
.thumbnail-banner-choose-us img {
  height: 100%;
}
@media only screen and (max-width: 575px) {
  .thumbnail-banner-choose-us img {
    width: 100%;
  }
}

.career-page-single-banner.blog-page {
  max-width: 65%;
  margin: auto;
}
.career-page-single-banner.blog-page .title {
  line-height: 1.4;
}

.why-choose-intro-disc p {
  font-size: 20px;
  line-height: 1.5;
}

.career-single-banner-area.blog-page {
  background: #fafafa;
  text-align: center;
}

.career-page-single-banner span.pre {
  color: var(--color-primary);
  font-size: 20px;
}
.career-page-single-banner h1.title {
  font-size: 56px;
  margin-bottom: 15px;
}
@media only screen and (max-width: 767px) {
  .career-page-single-banner h1.title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 575px) {
  .career-page-single-banner h1.title {
    font-size: 36px;
  }
}
.career-page-single-banner .title {
  font-size: 32px;
}
.career-page-single-banner .title span {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
}
.career-page-single-banner .single-career-wrapper {
  display: flex;
  align-items: center;
  gap: 35px;
  margin-top: 30px;
}
@media only screen and (max-width: 575px) {
  .career-page-single-banner .single-career-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}
.career-page-single-banner .single-career-wrapper .single .title {
  margin-bottom: 5px;
  font-size: 22px;
}
.career-page-single-banner .single-career-wrapper .single span {
  color: rgba(0, 0, 0, 0.4);
}

.left-career-single h6.title {
  font-size: 22px;
  color: #141414;
}

.apply-now-card {
  padding: 45px;
  margin-top: -200px;
  background: #fff;
  border: 1px solid #EAF0FF;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  text-align: center;
}
@media only screen and (max-width: 991px) {
  .apply-now-card {
    margin-top: 0;
  }
}
@media only screen and (max-width: 575px) {
  .apply-now-card {
    padding: 45px 25px;
  }
}
.apply-now-card .pre {
  color: #717383;
}
.apply-now-card a.rts-btn {
  max-width: 100%;
  justify-content: center;
  margin-top: 22px;
  margin-bottom: 22px;
}
.apply-now-card a.rts-btn:hover::after {
  transform: scaleX(5) scaleY(5);
}
.apply-now-card p {
  margin-bottom: 18px;
  color: #717383;
}
.apply-now-card p a {
  font-weight: 600;
  color: #141414;
}

.apply-right-area-sticky {
  height: 100%;
}
.apply-right-area-sticky .sticky-inner {
  position: sticky;
  position: -webkit-sticky;
  top: 50px;
  /* required */
}

.career-single-body {
  overflow-x: visible;
}

.career-page-single-banner.center-aligh {
  text-align: center;
}
.career-page-single-banner.center-aligh .single-career-wrapper {
  justify-content: center;
}
@media only screen and (max-width: 575px) {
  .career-page-single-banner.center-aligh .single-career-wrapper {
    align-items: center;
  }
}

@media only screen and (max-width: 1199px) {
  .container-contact {
    padding: 0 15px;
  }
}

.apply-job-form input[type=file] {
  height: 45px;
  margin-bottom: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 9px;
  margin-top: 10px;
  cursor: pointer;
}
.apply-job-form .filelabel {
  width: 100%;
  cursor: pointer;
}

.gmnoprint.gm-bundled-control.gm-bundled-control-on-bottom {
  margin: 10px;
  user-select: none;
  position: absolute;
  top: 0 !important;
  right: 44px !important;
}

.about-banner-area-bg {
  background-image: url(../images/about/01.webp);
  height: 750px !important;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  clip-path: polygon(0% 0%, 100% 0%, 100% 90.323%, 0% 100%, 0% 0%);
}
@media only screen and (max-width: 767px) {
  .about-banner-area-bg {
    height: 450px !important;
  }
}

.mt-dec-80 {
  margin-top: -80px;
  position: relative;
  z-index: 10;
}
.mt-dec-80 .single-counter-up-one {
  background: #fff !important;
}

.what-we-do-wrapper-about p.disc {
  font-size: 36px;
  line-height: 1.5;
}
@media only screen and (max-width: 767px) {
  .what-we-do-wrapper-about p.disc {
    font-size: 22px;
  }
}

.what-we-do-main-wrapper .title {
  font-size: 28px;
  margin-bottom: 18px;
}
.what-we-do-main-wrapper p.disc {
  font-size: 20px;
  line-height: 1.5;
}
.what-we-do-main-wrapper .rts-btn {
  margin-top: 64px;
  display: flex;
  justify-content: space-between;
  max-width: 192px;
}

.thumbnail-about-mid {
  clip-path: polygon(0% 5%, 100% 0%, 100% 95%, 0% 100%, 0% 5%);
  height: 750px !important;
}
@media only screen and (max-width: 1199px) {
  .thumbnail-about-mid {
    height: auto !important;
  }
}
@media only screen and (max-width: 991px) {
  .thumbnail-about-mid {
    height: 378px !important;
  }
}

.brand-area-main-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.brand-area-main-wrapper .single-brand {
  max-width: 180px;
  height: auto;
}

.image--large-video {
  clip-path: polygon(0% 9.494%, 100% 0%, 100% 88.924%, 0% 100%, 0% 9.494%);
}
.image--large-video img {
  clip-path: polygon(0% 9.494%, 100% 0%, 100% 88.924%, 0% 100%, 0% 9.494%);
}

.container-large {
  max-width: 1780px;
  margin: auto;
}

.alrge-video-area .jarallax {
  height: 832px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .alrge-video-area .jarallax {
    height: 547px;
  }
}
@media only screen and (max-width: 1199px) {
  .alrge-video-area .jarallax {
    height: 547px;
  }
}
@media only screen and (max-width: 991px) {
  .alrge-video-area .jarallax {
    height: 389px;
  }
}
@media only screen and (max-width: 575px) {
  .alrge-video-area .jarallax {
    height: 163px;
  }
}
.alrge-video-area .jarallax.v3 {
  height: 950px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .alrge-video-area .jarallax.v3 {
    height: 676px;
  }
}
@media only screen and (max-width: 1199px) {
  .alrge-video-area .jarallax.v3 {
    height: 476px;
  }
}
@media only screen and (max-width: 991px) {
  .alrge-video-area .jarallax.v3 {
    height: 360px;
  }
}
@media only screen and (max-width: 991px) {
  .alrge-video-area .jarallax.v3 {
    height: 280px;
  }
}
@media only screen and (max-width: 575px) {
  .alrge-video-area .jarallax.v3 {
    display: none;
  }
}

.jarallax {
  height: 100%;
}

.title-video-top {
  max-width: 768px;
  text-align: center;
  margin: auto;
}
.title-video-top p.large {
  font-size: 40px;
  margin: auto;
  line-height: 1.3;
  color: #262626;
  margin-bottom: 30px;
}
@media only screen and (max-width: 991px) {
  .title-video-top p.large {
    font-size: 32px;
  }
}
@media only screen and (max-width: 1199px) {
  .title-video-top p.large {
    font-size: 26px;
  }
}
@media only screen and (max-width: 575px) {
  .title-video-top p.large {
    font-size: 22px;
  }
}
.title-video-top p.large span {
  color: var(--color-primary);
}
.title-video-top p.disc {
  display: block;
  font-size: 24px;
  line-height: 1.5;
  color: #262626;
}
@media only screen and (max-width: 991px) {
  .title-video-top p.disc {
    font-size: 18px;
  }
}

.large-video-bottom {
  max-width: 768px;
  margin: auto;
  text-align: center;
  margin-top: 70px;
}
.large-video-bottom p.disc {
  font-size: 20px;
  line-height: 1.5;
}
.large-video-bottom .title {
  font-size: 36px;
}
.large-video-bottom a.rts-btn {
  margin: auto;
}
.large-video-bottom a.rts-btn img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(1%) saturate(2900%) hue-rotate(63deg) brightness(115%) contrast(100%);
}
.large-video-bottom a.rts-btn:focus {
  box-shadow: none;
}

@media only screen and (max-width: 575px) {
  .alrge-video-area.rts-section-gapBottom.mt-dec-banner-3 {
    display: none;
  }
}

.alrge-video-area {
  position: relative;
}
@media only screen and (max-width: 767px) {
  .alrge-video-area {
    display: none;
  }
}
.alrge-video-area .shape-top {
  position: absolute;
  left: 0;
  top: 17%;
}
@media only screen and (max-width: 1199px) {
  .alrge-video-area .shape-top {
    display: none;
  }
}
.alrge-video-area .shape-bottom {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}
@media only screen and (max-width: 575px) {
  .alrge-video-area .shape-bottom {
    display: none;
  }
}

.inner-content-wrapper-three * {
  color: #fff;
}
.inner-content-wrapper-three p {
  font-size: 20px;
  line-height: 1.5;
  color: #D1D1D1;
}
@media only screen and (max-width: 575px) {
  .inner-content-wrapper-three p {
    font-size: 18px;
  }
}
.inner-content-wrapper-three .rts-btn img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(50deg) brightness(101%) contrast(102%);
}

.bg-solution {
  background: #0B0A33;
}

.single-solution-style-one {
  padding: 48px;
  border-right: 1px solid #454545;
  border-top: 1px solid #454545;
  position: relative;
  transition: 0.3s;
}
@media only screen and (max-width: 1199px) {
  .single-solution-style-one {
    border: none !important;
    padding: 15px !important;
  }
}
@media only screen and (max-width: 575px) {
  .single-solution-style-one {
    padding: 15px !important;
  }
}
.single-solution-style-one .right-draw {
  position: absolute;
  right: 0;
  top: 0;
  opacity: 0;
}
.single-solution-style-one .right-draw img {
  transition: 0.3s;
}
.single-solution-style-one:hover {
  background: #fff;
}
.single-solution-style-one:hover .icon img {
  filter: brightness(0) saturate(100%) invert(20%) sepia(100%) saturate(5128%) hue-rotate(243deg) brightness(98%) contrast(106%);
}
.single-solution-style-one:hover .title {
  color: #000000;
}
.single-solution-style-one:hover p.disc {
  color: #000000;
}
.single-solution-style-one:hover .right-draw {
  opacity: 1;
}
.single-solution-style-one:hover .right-draw img {
  animation: moveLeft 0.5s linear;
}
.single-solution-style-one:hover .btn-arrow {
  color: #6D6D6D;
}
.single-solution-style-one.border-left {
  border-left: 1px solid #454545;
}
.single-solution-style-one.border-bottom-1 {
  border-bottom: 1px solid #454545;
}
.single-solution-style-one .title {
  color: #fff;
  margin-top: 40px;
  transition: 0.3s;
}
.single-solution-style-one p.disc {
  color: #FFFFFF;
  margin-bottom: 25px;
  transition: 0.3s;
}
.single-solution-style-one .btn-arrow {
  color: #B0B0B0;
  transition: 0.3s;
  font-weight: 500;
  font-size: 16px;
}
.single-solution-style-one .btn-arrow img {
  transition: 0.3s;
}
.single-solution-style-one .btn-arrow svg {
  transition: 0.3s;
}
.single-solution-style-one .btn-arrow:hover {
  color: var(--color-primary);
}
.single-solution-style-one .btn-arrow:hover img {
  margin-left: 5px;
  filter: brightness(0) saturate(100%) invert(20%) sepia(100%) saturate(5128%) hue-rotate(243deg) brightness(98%) contrast(106%);
}
.single-solution-style-one .btn-arrow:hover svg {
  margin-left: 5px;
}
.single-solution-style-one .btn-arrow:hover svg path {
  stroke: var(--color-primary);
}

.title-center-2 {
  text-align: center;
  max-width: 952px;
  margin: auto;
}
.title-center-2 .title {
  font-size: 64px;
  color: #fff;
  font-weight: 400;
}
@media only screen and (max-width: 991px) {
  .title-center-2 .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 575px) {
  .title-center-2 .title {
    font-size: 36px;
  }
}
.title-center-2 p {
  max-width: 80%;
  margin: auto;
  color: #fff;
  font-size: 20px;
  line-height: 1.5;
}
@media only screen and (max-width: 575px) {
  .title-center-2 p {
    max-width: 100%;
  }
}

.solution-expertice-area .rts-btn {
  background: #fff;
  color: #000;
  margin: auto;
  margin-top: 80px;
  border: 1px solid transparent;
}
.solution-expertice-area .rts-btn img {
  filter: brightness(0) saturate(100%) invert(0%) sepia(12%) saturate(202%) hue-rotate(343deg) brightness(84%) contrast(100%);
}
.solution-expertice-area .rts-btn svg {
  max-width: 24px;
}
.solution-expertice-area .rts-btn svg path {
  stroke: #2A2ACC !important;
}
.solution-expertice-area .rts-btn:hover svg path {
  stroke: #2A2ACC !important;
}

.text-center-title-bg-white {
  text-align: center;
}
.text-center-title-bg-white .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .text-center-title-bg-white .title {
    font-size: 34px !important;
  }
}
.text-center-title-bg-white p {
  max-width: 60%;
  margin: auto;
  font-size: 20px;
}
@media only screen and (max-width: 767px) {
  .text-center-title-bg-white p {
    max-width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .text-center-title-bg-white p {
    max-width: 100%;
    font-size: 16px;
  }
}

.solution-expertice-area {
  position: relative;
}
.solution-expertice-area .top-left {
  position: absolute;
  left: 120px;
  top: -24px;
}

.container-full {
  max-width: 1920px;
  margin: auto;
}

.case-studies-area .shape-left-top {
  position: absolute;
  left: 0;
  top: 0;
}
@media only screen and (max-width: 575px) {
  .case-studies-area .shape-left-top {
    display: none;
  }
}
.case-studies-area .shape-bottom {
  position: absolute;
  right: 0;
  bottom: 0;
}
@media only screen and (max-width: 575px) {
  .case-studies-area .shape-bottom {
    display: none;
  }
}

.solution-exparts-area-service-page .exparts-area-main-mt-dec {
  position: relative;
}
.solution-exparts-area-service-page .exparts-area-main-mt-dec .top-right-shape {
  position: absolute;
  right: 0;
  top: 0;
}
@media only screen and (max-width: 991px) {
  .solution-exparts-area-service-page .exparts-area-main-mt-dec .top-right-shape {
    display: none;
  }
}

.single-case-studies.style-swiper {
  padding: 8px;
  background: #fff;
}
.single-case-studies .thumbnail {
  overflow: hidden;
  display: block;
  border-radius:50px !important;
}
.single-case-studies .thumbnail img {
  transition: 0.4s;
  transform: scale(1.05);
  border-radius:50px !important;
}
.single-case-studies .inner-content {
  padding: 30px;
}
@media only screen and (max-width: 767px) {
  .single-case-studies .inner-content {
    padding: 25px 0;
  }
}
@media only screen and (max-width: 575px) {
  .single-case-studies .inner-content {
    padding: 30px 10px;
  }
}
.single-case-studies .inner-content span {
  color: #262626;
  font-size: 16px;
}
.single-case-studies .inner-content .title {
  margin-top: 12px;
  transition: 0.3s;
  font-size: 36px;
  margin-bottom: 26px;
}
@media only screen and (max-width: 575px) {
  .single-case-studies .inner-content .title {
    font-size: 28px;
  }
}
.single-case-studies .inner-content .title:hover {
  color: var(--color-primary);
}
.single-case-studies:hover .thumbnail img {
  transform: scale(1);
}

.g-80 {
  --bs-gutter-x: 80px;
  --bs-gutter-y: 80px;
}
@media only screen and (max-width: 991px) {
  .g-80 {
    --bs-gutter-x: 40px;
    --bs-gutter-y: 40px;
  }
}

.more-project-btn {
  margin: auto;
  height: 48px;
  background: #262626;
  color: #fff !important;
  border: none !important;
}
.more-project-btn img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(136deg) brightness(120%) contrast(107%);
}

.single-case-studies-three a.thumbnail {
  display: block;
  overflow: hidden;
  margin-bottom: 26px;
}
.single-case-studies-three a.thumbnail img {
  transition: 0.3s;
  transform: scale(1.03);
}
.single-case-studies-three .inner-content .title {
  font-size: 28px;
  transition: 0.3s;
}
.single-case-studies-three .inner-content .title:hover {
  color: var(--color-primary);
}
.single-case-studies-three .inner-content p {
  color: #262626;
  font-size: 20px;
  line-height: 1.5;
}
.single-case-studies-three:hover .thumbnail img {
  transform: scale(1);
}

.project-style-5-title-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .project-style-5-title-between {
    flex-direction: column;
    align-items: flex-start;
  }
}
.project-style-5-title-between .title {
  font-size: 64px;
}
@media only screen and (max-width: 575px) {
  .project-style-5-title-between .title {
    font-size: 44px;
  }
}
.project-style-5-title-between p.disc {
  max-width: 50%;
  margin-left: auto;
  font-size: 20px;
  line-height: 1.5;
}
@media only screen and (max-width: 767px) {
  .project-style-5-title-between p.disc {
    margin-left: 0;
    max-width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .project-style-5-title-between p.disc {
    font-size: 18px;
  }
}

.over_link {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.single-project-area-5 {
  position: relative;
  overflow: hidden;
  display: block;
}
.single-project-area-5 .thumbnail {
  overflow: hidden;
  display: block;
}
.single-project-area-5 .inner-content {
  background: rgba(255, 255, 255, 0.5803921569);
  backdrop-filter: blur(10px);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32px;
  transition: 0.3s;
  height: 132px;
  display: flex;
  align-items: center;
}
.single-project-area-5 .inner-content .inner {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  transition: 0.3s;
  width: 100%;
}
.single-project-area-5 .inner-content .title {
  margin-bottom: 0;
  font-size: 36px;
}
.single-project-area-5 .inner-content .icon-area a {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary);
  height: 40px;
  width: 40px;
}
.single-project-area-5 .inner-content .icon-area a img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(286deg) brightness(103%) contrast(103%);
}
.single-project-area-5:hover .inner-content {
  height: 100%;
}

.single-large-case-studies-area-details .single-case-studies .thumbnail img {
  transform: scale(1.005);
}
.single-large-case-studies-area-details .single-case-studies .inner-content {
  display: flex;
  align-items: flex-start;
  padding: 150px 0;
  justify-content: space-between;
  border-bottom: 1px solid #D1D1D1;
}
@media only screen and (max-width: 991px) {
  .single-large-case-studies-area-details .single-case-studies .inner-content {
    flex-direction: column;
    align-items: flex-start;
    padding: 60px 0;
  }
}
.single-large-case-studies-area-details .single-case-studies .inner-content .right-area {
  max-width: 640px;
  margin-left: auto;
}
@media only screen and (max-width: 991px) {
  .single-large-case-studies-area-details .single-case-studies .inner-content .right-area {
    margin-left: 0;
  }
}
.single-large-case-studies-area-details .single-case-studies .inner-content .right-area .top {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 30px;
}
@media only screen and (max-width: 575px) {
  .single-large-case-studies-area-details .single-case-studies .inner-content .right-area .top {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
.single-large-case-studies-area-details .single-case-studies .inner-content .right-area .top span {
  color: #6D6D6D;
}
.single-large-case-studies-area-details .single-case-studies .inner-content .right-area p {
  font-size: 20px;
}

.about-case-details-inne .p1 {
  font-size: 36px;
  line-height: 1.5;
}
@media only screen and (max-width: 991px) {
  .about-case-details-inne .p1 {
    font-size: 32px;
  }
}
@media only screen and (max-width: 767px) {
  .about-case-details-inne .p1 {
    font-size: 22px;
  }
}
@media only screen and (max-width: 575px) {
  .about-case-details-inne .p1 {
    font-size: 18px;
  }
}
.about-case-details-inne .between-area-main-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 48px;
  margin-top: 80px;
}
@media only screen and (max-width: 767px) {
  .about-case-details-inne .between-area-main-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
  }
}
@media only screen and (max-width: 575px) {
  .about-case-details-inne .between-area-main-wrapper {
    margin-top: 20px;
  }
}

.inner-content-wrapper-paragraph-case-para p {
  font-size: 24px;
  line-height: 1.5;
  margin-bottom: 30px;
}
@media only screen and (max-width: 767px) {
  .inner-content-wrapper-paragraph-case-para p {
    font-size: 18px;
  }
}

.title-more-case {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .title-more-case {
    font-size: 36px;
  }
}

.bg-gradient-pricing {
  background: linear-gradient(180deg, #F6F6F6 0%, rgba(255, 255, 255, 0) 100%);
  position: relative;
  z-index: 1;
}
.bg-gradient-pricing .bg-shape-area {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  z-index: -1;
  min-width: 70%;
}

.m-auto {
  margin: auto;
  text-align: center;
  font-size: 14px;
}

.container-pricing {
  max-width: 768px;
  margin: auto;
}

.single-pricing-area {
  padding: 64px;
  border-right: 1px solid #D1D1D1;
  border-top: 1px solid #D1D1D1;
  border-bottom: 1px solid #D1D1D1;
  background: #FFFFFF;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .single-pricing-area {
    padding: 30px;
  }
}
.single-pricing-area .head {
  margin-bottom: 30px;
}
.single-pricing-area .head span {
  font-size: 28px;
  color: var(--color-primary);
  font-weight: 600;
  margin-bottom: 10px;
  display: block;
}
@media only screen and (max-width: 575px) {
  .single-pricing-area .head span {
    font-family: var(--font-medium) !important;
    font-size: 22px;
  }
}
.single-pricing-area .head .title {
  font-size: 64px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 767px) {
  .single-pricing-area .head .title {
    font-size: 42px;
  }
}
@media only screen and (max-width: 575px) {
  .single-pricing-area .head .title {
    font-size: 36px;
    margin-top: 10px;
  }
}
.single-pricing-area .body .rts-btn {
  width: 100%;
  z-index: 1;
  height: 48px;
  max-width: 100%;
  margin-bottom: 15px;
}
.single-pricing-area .body .rts-btn::after {
  height: 300px;
  width: 300px;
}
.single-pricing-area.border-left {
  border-left: 1px solid #D1D1D1;
}

.gradient-bg-pricing {
  background: linear-gradient(180deg, #E9F0FF 0%, rgba(255, 255, 255, 0) 100%);
}

.single-pricing-area-start-2 {
  border-top: 1px solid #D1D1D1;
  border-right: 1px solid #D1D1D1;
  border-bottom: 1px solid #D1D1D1;
  padding: 32px;
}
@media only screen and (max-width: 479px) {
  .single-pricing-area-start-2 {
    padding: 25px 10px;
  }
}
.single-pricing-area-start-2.border-left {
  border-left: 1px solid #D1D1D1;
}
.single-pricing-area-start-2 .head-area {
  text-align: center;
}
.single-pricing-area-start-2 .head-area .title {
  font-size: 64px;
}
@media only screen and (max-width: 1199px) {
  .single-pricing-area-start-2 .head-area .title {
    font-size: 46px;
  }
}
@media only screen and (max-width: 991px) {
  .single-pricing-area-start-2 .head-area .title {
    font-size: 44px !important;
  }
}
@media only screen and (max-width: 575px) {
  .single-pricing-area-start-2 .head-area .title {
    font-size: 40px !important;
  }
}
.single-pricing-area-start-2 .head-area span {
  font-weight: 500;
}
.single-pricing-area-start-2 .head-area .icon {
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: auto;
  background: #FFFFFF;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}
.single-pricing-area-start-2 .head-area p {
  margin-bottom: 17px;
  font-size: 20px;
  font-weight: 500;
  margin-top: 20px;
}
.single-pricing-area-start-2 .body-areas {
  margin-top: 30px;
  padding-bottom: 35px;
}
.single-pricing-area-start-2 .body-areas .single-check {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 12px 0;
}
.single-pricing-area-start-2 .body-areas .single-check p {
  margin-bottom: 0;
}
.single-pricing-area-start-2 .footer-pricing {
  padding-top: 32px;
  border-top: 1px solid #D1D1D1;
}
.single-pricing-area-start-2 .footer-pricing .rts-btn {
  max-width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px 16px;
}
.single-pricing-area-start-2 .footer-pricing .rts-btn::after {
  width: 400px;
  height: 400px;
}
.single-pricing-area-start-2.active {
  position: relative;
}
.single-pricing-area-start-2.active .tag {
  position: absolute;
  right: 20px;
  top: 20px;
  color: var(--color-primary);
  font-weight: 500;
}
.single-pricing-area-start-2.active .head-area p {
  color: var(--color-primary);
}

.faq-bottom-section-text p a {
  color: var(--color-primary) !important;
}

.service-banner-content-wrapper .bread-plug {
  display: flex;
  align-items: center;
  gap: 7px;
}
.service-banner-content-wrapper .bread-plug a {
  font-size: 20px;
}
.service-banner-content-wrapper .bread-plug i {
  color: #999999;
}
.service-banner-content-wrapper .bread-plug a.current {
  color: #999999;
}
.service-banner-content-wrapper .title {
  font-size: 64px;
  line-height: 1;
  margin-top: 30px;
  margin-bottom: 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .service-banner-content-wrapper .title {
    font-size: 54px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 1199px) {
  .service-banner-content-wrapper .title {
    font-size: 54px;
  }
}
@media only screen and (max-width: 767px) {
  .service-banner-content-wrapper .title {
    font-size: 44px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  .service-banner-content-wrapper .title {
    line-height: 1.3;
    font-size: 44px;
  }
}
@media only screen and (max-width: 479px) {
  .service-banner-content-wrapper .title {
    font-size: 36px;
  }
}
.service-banner-content-wrapper p.disc {
  max-width: 640px;
}

.accordion-container-one {
  max-width: 768px;
  margin: auto;
}
.accordion-container-one .accordion-item {
  border-color: #D1D1D1;
}
.accordion-container-one .accordion-body {
  padding: 20px 35px;
  padding-top: 10px;
}
.accordion-container-one .accordion-button {
  box-shadow: none;
  padding: 26px 32px;
  font-size: 20px;
  color: #000;
}
@media only screen and (max-width: 575px) {
  .accordion-container-one .accordion-button {
    font-size: 16px;
    padding: 20px;
  }
}
.accordion-container-one .accordion-button::after {
  transform: rotate(92deg);
}
.accordion-container-one .accordion-button[aria-expanded=true] {
  background: transparent;
}
.accordion-container-one .accordion-button[aria-expanded=true]::after {
  transform: rotate(0);
}

.bg-primary {
  background: var(--color-primary);
}

.title-between-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .title-between-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 25px;
  }
}
@media only screen and (max-width: 575px) {
  .title-between-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
}
.title-between-wrapper p.disc {
  max-width: 35%;
}
@media only screen and (max-width: 767px) {
  .title-between-wrapper p.disc {
    max-width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .title-between-wrapper p.disc {
    max-width: 100%;
  }
}

.bg-primary .title-between-wrapper * {
  color: #fff;
}

.professional-faq-area {
  overflow: hidden;
}
.professional-faq-area .shape-top {
  position: absolute;
  left: 0;
  bottom: -3px;
}
@media only screen and (max-width: 767px) {
  .professional-faq-area .shape-top {
    display: none;
  }
}

.accordion-container-one.style-two .accordion-item {
  background: #F6F6F6;
  margin-bottom: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.125) !important;
}
.accordion-container-one.style-two .accordion-button {
  background-color: #F6F6F6;
}

.title-between-wrapper .title {
  font-weight: 400;
  font-size: 64px;
}
@media only screen and (max-width: 767px) {
  .title-between-wrapper .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 575px) {
  .title-between-wrapper .title {
    font-size: 32px;
  }
}

.single-testimonials-area-one {
  background: #fff;
  padding: 48px;
  border-right: 1px solid #D1D1D1;
  border-bottom: 1px solid #D1D1D1;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-area-one {
    padding: 22px;
  }
}
.single-testimonials-area-one p {
  font-size: 20px;
  line-height: 1.5;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-area-one p {
    font-size: 16px;
  }
}
.single-testimonials-area-one .author-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 60px;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-area-one .author-wrapper {
    margin-top: 30px;
  }
}
.single-testimonials-area-one .author-wrapper .avatar img {
  max-width: 60px;
}
.single-testimonials-area-one .author-wrapper .information .title {
  margin-bottom: 5px;
  font-size: 16px;
}
.single-testimonials-area-one .author-wrapper .information span {
  font-size: 14px;
  color: #636363;
}

.testimonials-area-start .shape-top-right {
  position: absolute;
  right: 120px;
  top: -24px;
}

.rts-blog-area .shape-bottom {
  position: absolute;
  top: 0;
  right: 0;
}
@media only screen and (max-width: 575px) {
  .rts-blog-area .shape-bottom {
    display: none;
  }
}

.cta-one-wrapper {
  position: relative;
}
.cta-one-wrapper .shape-area {
  position: absolute;
  left: 80px;
  top: -24px;
}

.testimonials-title-two-center {
  text-align: center;
}
.testimonials-title-two-center .title {
  color: #fff;
  font-weight: 400;
  font-weight: 64px;
}

.swiper-slide-active .single-testimonials-two {
  border-left: 1px solid #4A4965;
}

.single-testimonials-two {
  padding: 48px;
  border-right: 1px solid #4A4965;
  border-top: 1px solid #4A4965;
  border-bottom: 1px solid #4A4965;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-two {
    padding: 20px;
  }
}
.single-testimonials-two * {
  color: #fff;
}
.single-testimonials-two p {
  font-size: 20px;
  line-height: 1.5;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-two p {
    font-size: 18px;
  }
}
.single-testimonials-two .inner-author {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 120px;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-two .inner-author {
    margin-top: 50px;
  }
}
.single-testimonials-two .inner-author .info .title {
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 16px;
}
.single-testimonials-two .inner-author .info span {
  font-size: 14px;
}

.rts-testimonials-area-two {
  position: relative;
}
.rts-testimonials-area-two .shape-area-one {
  position: absolute;
  left: 120px;
  top: -24px;
}
.rts-testimonials-area-two .shape-area-two {
  position: absolute;
  right: 0px;
  top: 0px;
}

.position-relative-testimonials-two {
  position: relative;
}
.position-relative-testimonials-two .swiper-button-prev,
.position-relative-testimonials-two .swiper-button-next {
  height: 48px;
  width: 48px;
  border-radius: 50%;
  background: #323153;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 0;
  top: auto;
  transition: 0.3s;
}
.position-relative-testimonials-two .swiper-button-prev:hover,
.position-relative-testimonials-two .swiper-button-next:hover {
  background: var(--color-primary);
}
.position-relative-testimonials-two .swiper-button-prev::after,
.position-relative-testimonials-two .swiper-button-next::after {
  display: none;
}
.position-relative-testimonials-two .swiper-button-prev i,
.position-relative-testimonials-two .swiper-button-next i {
  color: #fff;
}
.position-relative-testimonials-two .swiper-button-prev {
  left: 45.8%;
  right: auto;
}
@media only screen and (max-width: 575px) {
  .position-relative-testimonials-two .swiper-button-prev {
    left: 36.5%;
  }
}
@media only screen and (max-width: 479px) {
  .position-relative-testimonials-two .swiper-button-prev {
    display: none;
  }
}
.position-relative-testimonials-two .swiper-button-next {
  right: 45.8%;
  left: auto;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .position-relative-testimonials-two .swiper-button-next {
    right: 42.8%;
  }
}
@media only screen and (max-width: 575px) {
  .position-relative-testimonials-two .swiper-button-next {
    right: 36.5%;
  }
}
@media only screen and (max-width: 479px) {
  .position-relative-testimonials-two .swiper-button-next {
    display: none;
  }
}

.mySwiper-testimonials {
  padding-bottom: 130px;
}
@media only screen and (max-width: 479px) {
  .mySwiper-testimonials {
    padding-bottom: 0;
  }
}

.single-testimonials-4 {
  padding: 48px;
  border: 1px solid #4A4965;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-4 {
    padding: 8px;
  }
}
.single-testimonials-4 * {
  color: #fff;
}
.single-testimonials-4 .user-area {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 65px;
}
@media only screen and (max-width: 575px) {
  .single-testimonials-4 .user-area {
    margin-top: 30px;
  }
}
.single-testimonials-4 .user-area .title {
  margin-bottom: 3px;
}

.mySwiper-testimonials-4 {
  padding: 60px 0;
  padding-bottom: 150px;
  position: relative;
}
@media only screen and (max-width: 991px) {
  .mySwiper-testimonials-4 {
    padding-bottom: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .mySwiper-testimonials-4 {
    padding-bottom: 50px;
  }
}
.mySwiper-testimonials-4 .swiper-wrapper {
  display: flex;
  align-items: center;
}
.mySwiper-testimonials-4 .swiper-slide {
  transition: 0.3s ease-out;
  transition-delay: 0.3s;
}
.mySwiper-testimonials-4 .swiper-slide-active {
  transform: scale(1.3);
}
@media only screen and (max-width: 991px) {
  .mySwiper-testimonials-4 .swiper-slide-active {
    transform: scale(1) !important;
  }
}
.mySwiper-testimonials-4 .swiper-slide-active .single-testimonials-4 {
  background: #201F99;
  border: 1px solid #201F99;
}
.mySwiper-testimonials-4 .swiper-slide-active .single-testimonials-4 .user-area {
  margin-top: 120px;
}
@media only screen and (max-width: 575px) {
  .mySwiper-testimonials-4 .swiper-slide-active .single-testimonials-4 .user-area {
    margin-top: 40px;
  }
}
.mySwiper-testimonials-4 .swiper-button-next,
.mySwiper-testimonials-4 .swiper-button-prev {
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #323153;
  border-radius: 50%;
  position: absolute;
  top: auto;
  bottom: 0;
  transition: 0.3s;
}
@media only screen and (max-width: 991px) {
  .mySwiper-testimonials-4 .swiper-button-next,
  .mySwiper-testimonials-4 .swiper-button-prev {
    display: none;
  }
}
.mySwiper-testimonials-4 .swiper-button-next::after,
.mySwiper-testimonials-4 .swiper-button-prev::after {
  font-size: 16px;
  color: #FFFFFF;
}
.mySwiper-testimonials-4 .swiper-button-next:hover,
.mySwiper-testimonials-4 .swiper-button-prev:hover {
  background: var(--color-primary);
}
.mySwiper-testimonials-4 .swiper-button-next {
  left: auto;
  right: 47%;
  bottom: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .mySwiper-testimonials-4 .swiper-button-next {
    right: 45%;
  }
}
@media only screen and (max-width: 1199px) {
  .mySwiper-testimonials-4 .swiper-button-next {
    right: 45%;
  }
}
.mySwiper-testimonials-4 .swiper-button-prev {
  right: auto;
  left: 47%;
  bottom: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .mySwiper-testimonials-4 .swiper-button-prev {
    left: 45%;
  }
}
@media only screen and (max-width: 1199px) {
  .mySwiper-testimonials-4 .swiper-button-prev {
    left: 45%;
  }
}

.testimonails-title-wrapper-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.testimonails-title-wrapper-between .swiper-button-next,
.testimonails-title-wrapper-between .swiper-button-prev {
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #454545;
  transition: 0.3s;
}
@media only screen and (max-width: 575px) {
  .testimonails-title-wrapper-between .swiper-button-next,
  .testimonails-title-wrapper-between .swiper-button-prev {
    display: none;
  }
}
.testimonails-title-wrapper-between .swiper-button-next::after,
.testimonails-title-wrapper-between .swiper-button-prev::after {
  display: none;
}
.testimonails-title-wrapper-between .swiper-button-next:hover,
.testimonails-title-wrapper-between .swiper-button-prev:hover {
  background: var(--color-primary);
}
.testimonails-title-wrapper-between .swiper-button-next {
  right: 0;
}
.testimonails-title-wrapper-between .swiper-button-prev {
  right: 48px;
  left: auto;
}

.mySwiper-testimonials-5 {
  padding-bottom: 100px;
}
@media only screen and (max-width: 575px) {
  .mySwiper-testimonials-5 {
    padding-bottom: 60px;
  }
}
.mySwiper-testimonials-5 .swiper-pagination-bullet {
  width: 40px;
  border-radius: 0;
  height: 4px;
  background: #454545;
  opacity: 1;
}
.mySwiper-testimonials-5 .swiper-pagination-bullet-active {
  width: 40px;
  border-radius: 0;
  height: 4px;
  background: var(--color-primary);
  opacity: 1;
}

.rts-testimonials-area-about.bg-dark-1 * {
  color: #fff;
}

.single-testimonials-about {
  padding: 48px;
  background: #3D3D3D;
}
@media only screen and (max-width: 767px) {
  .single-testimonials-about {
    padding: 25px;
  }
}
.single-testimonials-about p.disc {
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 45px;
}
.single-testimonials-about .author-area {
  display: flex;
  align-items: center;
  gap: 14px;
}
.single-testimonials-about .author-area .information .title {
  margin-bottom: 7px;
  font-size: 16px;
}
.single-testimonials-about .author-area .information p {
  font-size: 14px;
  color: #999999;
}

.testimonials-border .single-testimonials-area-one {
  border: 1px solid #d1d1d1;
}

.testimonials-area-start.in-service-single .title-between-wrapper .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .testimonials-area-start.in-service-single .title-between-wrapper .title {
    font-size: 32px;
  }
}

.single-blog-area-start {
  border-right: 1px solid #D1D1D1;
  border-bottom: 1px solid #D1D1D1;
  border-top: 1px solid #D1D1D1;
}
.single-blog-area-start.border-none {
  border: none;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}
.single-blog-area-start.border-left {
  border-left: 1px solid #D1D1D1;
}
.single-blog-area-start a.thumbnail {
  overflow: hidden;
  display: block;
}
.single-blog-area-start a.thumbnail img {
  transition: 0.3s;
  transform: scale(1.1);
  width: 100%;
}
.single-blog-area-start .inner-content-area {
  padding: 24px;
}
.single-blog-area-start .inner-content-area .top-area span {
  color: var(--color-primary);
  font-family: var(--font-medium);
  display: block;
  margin-bottom: 10px;
}
.single-blog-area-start .inner-content-area .top-area .title {
  transition: 0.3s;
  font-size: 28px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 575px) {
  .single-blog-area-start .inner-content-area .top-area .title {
    font-size: 22px;
  }
}
.single-blog-area-start .inner-content-area .top-area .title:hover {
  color: var(--color-primary);
}
.single-blog-area-start .inner-content-area p.disc {
  color: #6D6D6D;
  margin-bottom: 20px;
}
.single-blog-area-start .inner-content-area .bottom-author-area {
  display: flex;
  align-items: center;
  gap: 15px;
}
.single-blog-area-start .inner-content-area .bottom-author-area .author-area-info .title {
  margin-bottom: 2px;
  font-size: 14px;
  transition: 0.3s;
  font-weight: 600;
}
.single-blog-area-start .inner-content-area .bottom-author-area .author-area-info .title:hover {
  color: var(--color-primary);
}
.single-blog-area-start .inner-content-area .bottom-author-area .author-area-info span {
  color: #6D6D6D;
  font-weight: 500;
  margin-bottom: 0;
}
.single-blog-area-start:hover .thumbnail img {
  transform: scale(1);
}

.blog-list-body .single-blog-area-start.border-none {
  border: 1px solid #E9E9E9;
  box-shadow: none;
}

.rts-blog-area .rts-btn {
  height: 48px;
  margin: auto;
  margin-top: 80px;
}

.title-blog-center {
  text-align: center;
}
.title-blog-center .title {
  font-size: 64px;
}
@media only screen and (max-width: 991px) {
  .title-blog-center .title {
    font-size: 42px;
  }
}
@media only screen and (max-width: 767px) {
  .title-blog-center .title {
    font-size: 32px;
  }
}

.single-blog-list-area-lef-timage .thumbnail {
  display: block;
  overflow: hidden;
}
.single-blog-list-area-lef-timage .thumbnail img {
  transition: 0.3s;
}
.single-blog-list-area-lef-timage:hover .thumbnail img {
  transform: scale(1.1);
}

.single-blog-list-area-right-content {
  padding: 24px;
  background: #F6F6F6;
  height: 100%;
}
.single-blog-list-area-right-content .tag {
  color: #6D6D6D;
  font-family: var(--font-medium);
  display: block;
  margin-bottom: 7px;
}
.single-blog-list-area-right-content a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25px;
}
.single-blog-list-area-right-content a .title {
  font-size: 28px;
  margin: 0;
  transition: 0.3s;
}
.single-blog-list-area-right-content a .title:hover {
  color: var(--color-primary);
}
.single-blog-list-area-right-content p.disc {
  color: #6D6D6D !important;
}
.single-blog-list-area-right-content .bottom-author-area {
  display: flex;
  align-items: center;
  gap: 15px;
}
.single-blog-list-area-right-content .bottom-author-area .author-area-info .title {
  margin-bottom: 3px;
  font-family: var(--font-medium);
  transition: 0.3s;
}
.single-blog-list-area-right-content .bottom-author-area .author-area-info .title:hover {
  color: var(--color-primary);
}
.single-blog-list-area-right-content .bottom-author-area .author-area-info span {
  color: #6D6D6D;
}

.single-blog-style-three .thumbnail {
  display: block;
  overflow: hidden;
}
.single-blog-style-three .thumbnail img {
  transition: 0.3s;
}
.single-blog-style-three .inner {
  padding: 24px;
  background: #F6F6F6;
}
.single-blog-style-three .inner .tag {
  font-family: var(--font-medium);
  color: #6D6D6D;
  font-size: 14px;
}
.single-blog-style-three .inner .title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-blog-style-three .inner .title-wrapper .title {
  font-size: 28px;
  margin-top: 10px;
  margin-bottom: 0;
  transition: 0.3s;
}
.single-blog-style-three .inner .title-wrapper .title:hover {
  color: var(--color-primary);
}
.single-blog-style-three:hover .thumbnail img {
  transform: scale(1.05);
}

.rts-blog-area-5 {
  background: #F6F6F6;
}

.single-blog-area-five {
  position: relative;
}
.single-blog-area-five .badge-1 {
  position: absolute;
  display: block;
  padding: 6px 10px;
  background: var(--color-primary);
  color: #fff;
  left: 24px;
  top: 24px;
  z-index: 10;
}
.single-blog-area-five .inner-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1564) 0%, rgba(0, 0, 0, 0.68) 100%);
}
@media only screen and (max-width: 479px) {
  .single-blog-area-five .inner-content {
    padding: 10px;
  }
}
.single-blog-area-five .inner-content * {
  color: #fff;
}
.single-blog-area-five .inner-content .title-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.single-blog-area-five .inner-content .title-area .title {
  margin: 0;
}
.single-blog-area-five .inner-content .title-area img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}
.single-blog-area-five .thumbnail {
  overflow: hidden;
  display: block;
}
.single-blog-area-five .thumbnail img {
  transition: 0.3s;
}
.single-blog-area-five:hover .thumbnail img {
  transform: scale(1.05);
}

.rts-single-wized {
  background: transparent;
  border-radius: 0;
  padding: 40px;
  margin-bottom: 40px;
  border: 1px solid #E9E9E9;
}
.rts-single-wized:last-child {
  margin-bottom: 0;
}
@media only screen and (max-width: 479px) {
  .rts-single-wized {
    padding: 20px;
  }
}
.rts-single-wized.service {
  border-radius: 0;
}
.rts-single-wized.service .single-categories li a {
  border-radius: 0;
}
.rts-single-wized.download {
  background: #171717;
}
.rts-single-wized.download .title {
  color: #fff;
}
.rts-single-wized.download .single-download-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid #2D2D2D;
}
.rts-single-wized.download .single-download-area:first-child {
  padding-top: 0;
}
.rts-single-wized.download .single-download-area:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.rts-single-wized.download .single-download-area .mid {
  margin-right: auto;
  margin-left: 15px;
}
.rts-single-wized.download .single-download-area .mid .title {
  margin-bottom: 0;
  font-size: 18px;
  font-family: var(--font-primary);
}
.rts-single-wized.download .single-download-area .mid span {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #FFFFFF;
}
.rts-single-wized.download .single-download-area a.rts-btn {
  padding: 11px 15px;
  border-radius: 0;
}
.rts-single-wized.contact {
  background: #1C2539;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px;
}
@media only screen and (max-width: 479px) {
  .rts-single-wized.contact {
    padding: 25px 20px;
  }
}
@media only screen and (max-width: 479px) {
  .rts-single-wized.contact:last-child {
    margin-bottom: 0;
  }
}
.rts-single-wized.contact .wized-body {
  text-align: center;
}
.rts-single-wized.contact .wized-body .title {
  color: #fff;
  margin-bottom: 30px;
  font-size: 22px;
  line-height: 32px;
}
.rts-single-wized.contact .wized-body a.rts-btn {
  display: block;
  max-width: max-content;
  margin: auto;
}
.rts-single-wized .wized-header .title {
  margin-bottom: 10px;
}
.rts-single-wized .wized-body {
  margin-top: 30px;
}
.rts-single-wized .wized-body .rts-search-wrapper {
  position: relative;
}
.rts-single-wized .wized-body .rts-search-wrapper input {
  background: transparent;
  height: 55px;
  border-radius: 0;
  padding-right: 70px;
  padding-left: 25px;
  border: 1px solid #E9E9E9;
}
.rts-single-wized .wized-body .rts-search-wrapper input:focus {
  border: 1px solid var(--color-primary);
}
.rts-single-wized .wized-body .rts-search-wrapper button {
  position: absolute;
  max-width: max-content;
  height: 55px;
  width: 55px;
  border-radius: 0;
  background: var(--color-primary);
  display: inline-block;
  padding: 0 19px;
  right: 0;
}
.rts-single-wized .wized-body .rts-search-wrapper button i {
  color: #fff;
  font-size: 16px;
  line-height: 16px;
}
.rts-single-wized .single-categories {
  margin-bottom: 15px;
  padding: 0;
}
.rts-single-wized .single-categories:last-child {
  margin-bottom: 0;
}
.rts-single-wized .single-categories li {
  list-style: none;
}
.rts-single-wized .single-categories li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 13px 25px;
  background: transparent;
  color: #5D666F;
  font-weight: 500;
  transition: 0.3s;
  border-radius: 0;
  border: 1px solid #E9E9E9;
}
.rts-single-wized .single-categories li a i {
  color: var(--color-primary);
  transition: 0.3s;
}
.rts-single-wized .single-categories li a:hover {
  background: var(--color-primary);
  transform: translateY(-5px) scale(1.03);
  color: var(--color-white);
}
.rts-single-wized .single-categories li a:hover i {
  color: #fff;
}
.rts-single-wized .recent-post-single {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.rts-single-wized .recent-post-single:last-child {
  margin-bottom: 0;
}
.rts-single-wized .recent-post-single .thumbnail {
  margin-right: 20px;
  overflow: hidden;
  max-width: max-content;
  width: 100%;
  border-radius: 0;
}
.rts-single-wized .recent-post-single .thumbnail img {
  min-width: 85px;
  height: auto;
  transition: 0.3s;
}
.rts-single-wized .recent-post-single .thumbnail:hover img {
  transform: scale(1.2);
}
.rts-single-wized .recent-post-single .user {
  display: flex;
  align-items: center;
}
.rts-single-wized .recent-post-single .user span {
  margin-left: 9px;
}
.rts-single-wized .recent-post-single .post-title .title {
  margin-bottom: 0;
  font-size: 16px;
  color: #1C2539;
  line-height: 26px;
  margin-top: 5px;
  transition: 0.3s;
  font-family: var(--font-medium);
}
@media only screen and (max-width: 479px) {
  .rts-single-wized .recent-post-single .post-title .title {
    font-size: 14px;
    line-height: 26px;
    margin-top: 0;
  }
}
.rts-single-wized .recent-post-single .post-title:hover .title {
  color: var(--color-primary);
}
.rts-single-wized .gallery-inner {
  display: flex;
  flex-direction: column;
}
.rts-single-wized .gallery-inner .single-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rts-single-wized .gallery-inner .single-row a {
  overflow: hidden;
  border-radius: 0;
}
@media only screen and (max-width: 479px) {
  .rts-single-wized .gallery-inner .single-row a {
    display: block;
    width: 100%;
  }
}
.rts-single-wized .gallery-inner .single-row a img {
  max-width: 97px;
  height: auto;
  transition: 0.6s;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .rts-single-wized .gallery-inner .single-row a img {
    max-width: 80px;
  }
}
@media only screen and (max-width: 1199px) {
  .rts-single-wized .gallery-inner .single-row a img {
    min-width: 269px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-single-wized .gallery-inner .single-row a img {
    min-width: 193px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-single-wized .gallery-inner .single-row a img {
    min-width: 135px;
  }
}
@media only screen and (max-width: 575px) {
  .rts-single-wized .gallery-inner .single-row a img {
    min-width: 140px;
  }
}
@media only screen and (max-width: 479px) {
  .rts-single-wized .gallery-inner .single-row a img {
    min-width: 80px;
  }
}
.rts-single-wized .gallery-inner .single-row a:hover img {
  transform: scale(1.2);
}
.rts-single-wized .gallery-inner .single-row.row-1 {
  margin-bottom: 20px;
}
.rts-single-wized .tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -10px;
}
.rts-single-wized .tags-wrapper a {
  padding: 5px 16px;
  background: transparent;
  border-radius: 0;
  margin-right: 10px;
  margin-bottom: 10px;
  color: #1C2539;
  font-size: 14px;
  font-weight: 500;
  transition: 0.5s;
  border: 1px solid #E9E9E9;
}
.rts-single-wized .tags-wrapper a:hover {
  background: var(--color-primary);
  color: #fff;
  transform: translateY(-3px) scale(1.09);
  border: 1px solid transparent;
}

.pagination-one ul {
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  list-style: none;
}
.pagination-one ul li {
  margin: 0;
  margin-right: 10px;
}
.pagination-one ul li button {
  width: 50px;
  height: 50px;
  background: transparent;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #000;
  border: 1px solid #E9E9E9;
  transition: 0.3s;
}
.pagination-one ul li button.active {
  background: var(--color-primary);
  color: #fff;
}
.pagination-one ul li button:hover {
  background: var(--color-primary);
  color: #fff;
}

.pagination-breadcrumb {
  display: flex;
  align-items: center;
  gap: 7px;
}
.pagination-breadcrumb a {
  font-size: 18px;
}
.pagination-breadcrumb a.current {
  color: var(--color-primary);
}

.blog-single-post-listing {
  border: 1px solid #E6E9F0;
  margin-bottom: 50px;
}
@media only screen and (max-width: 1199px) {
  .blog-single-post-listing {
    margin-right: 0;
  }
}
.blog-single-post-listing.details {
  border-radius: 0;
}
.blog-single-post-listing.details .thumbnail {
  border-radius: 0;
}
.blog-single-post-listing.details .thumbnail.details {
  border-radius: 0;
  width: 100%;
  max-width: max-content;
}
@media only screen and (max-width: 1199px) {
  .blog-single-post-listing.details .thumbnail.details {
    max-width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single-post-listing.details .thumbnail.details {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .thumbnail.details {
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing.details .thumbnail.details {
    margin-bottom: 0;
  }
}
.blog-single-post-listing.details .rts-quote-area {
  padding: 50px;
  background: #F6F6F6;
  border-radius: 0;
  margin-bottom: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single-post-listing.details .rts-quote-area {
    padding: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .rts-quote-area {
    padding: 10px;
    margin-bottom: 25px;
  }
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing.details .rts-quote-area {
    margin-top: 15px;
  }
}
.blog-single-post-listing.details .rts-quote-area .title {
  margin-bottom: 25px;
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing.details .rts-quote-area .title {
    font-size: 16px;
    margin-bottom: 15px;
  }
}
.blog-single-post-listing.details .rts-quote-area .name {
  font-size: 18px;
  color: var(--color-primary);
  font-weight: 700;
}
.blog-single-post-listing.details .rts-quote-area span {
  display: block;
  font-weight: 400;
  font-size: 14px;
  color: #5D666F;
}
.blog-single-post-listing.details .check-area-details .single-check {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}
.blog-single-post-listing.details .check-area-details .single-check i {
  margin-right: 15px;
  color: var(--color-primary);
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing.details .check-area-details .single-check i {
    margin-top: -26px;
  }
}
.blog-single-post-listing.details .check-area-details .single-check span {
  color: #5D666F;
}
.blog-single-post-listing.details .details-tag {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing.details .details-tag {
    justify-content: flex-start;
  }
}
.blog-single-post-listing.details .details-tag h6 {
  margin-bottom: 0;
  font-size: 18px;
  margin-right: 15px;
}
.blog-single-post-listing.details .details-tag button {
  padding: 8px 12px;
  background: #F6F6F6;
  max-width: max-content;
  margin-left: 10px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 0;
  color: #1C2539;
  transition: 0.3s;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .blog-single-post-listing.details .details-tag button:last-child {
    margin-top: 10px;
    margin-left: -2px;
  }
}
.blog-single-post-listing.details .details-tag button:hover {
  background: var(--color-primary);
  color: #fff;
  transform: translateY(-2px) scale(1.02);
}
.blog-single-post-listing.details .details-share {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-single-post-listing.details .details-share {
    justify-content: flex-start;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .details-share {
    justify-content: flex-start;
    margin-top: 30px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing.details .details-share {
    justify-content: flex-start;
    margin-top: 20px;
  }
}
.blog-single-post-listing.details .details-share button {
  max-width: max-content;
  position: relative;
  z-index: 1;
  margin-left: 23px;
  color: #1C2539;
  transition: 0.3s;
  font-size: 14px;
}
.blog-single-post-listing.details .details-share button::after {
  position: absolute;
  content: "";
  background: #F6F6F6;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  transition: 0.3s;
}
.blog-single-post-listing.details .details-share button:hover {
  color: #fff;
  transform: scale(1.2);
}
.blog-single-post-listing.details .details-share button:hover::after {
  background: var(--color-primary);
}
.blog-single-post-listing.details .details-share h6 {
  font-size: 18px;
  margin-bottom: 0;
  margin-right: 15px;
}
.blog-single-post-listing.details .author-area {
  margin-top: 44px;
  display: flex;
  align-items: center;
  padding: 40px 0;
  border-top: 1px solid #E6E9F0;
  border-bottom: 1px solid #E6E9F0;
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .author-area {
    align-items: flex-start;
  }
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing.details .author-area {
    flex-wrap: wrap;
  }
}
.blog-single-post-listing.details .author-area .thumbnail {
  margin-right: 30px;
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .author-area .thumbnail {
    margin-right: 0;
  }
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing.details .author-area .author-details {
    margin-left: 15px;
  }
}
.blog-single-post-listing.details .author-area .author-details h5 {
  margin-bottom: 10px;
}
.blog-single-post-listing.details .author-area .author-details p {
  line-height: 26px;
}
.blog-single-post-listing .replay-area-details {
  margin-top: 40px;
}
.blog-single-post-listing .replay-area-details form input {
  height: 55px;
  border-radius: 0;
  background: transparent;
  border: 1px solid #E9E9E9;
}
.blog-single-post-listing .replay-area-details form input:focus {
  border: 1px solid var(--color-primary);
}
.blog-single-post-listing .replay-area-details form textarea {
  border-radius: 0;
  background: transparent;
  height: 140px;
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #E9E9E9;
}
.blog-single-post-listing .replay-area-details form textarea:focus {
  border: 1px solid var(--color-primary);
}
.blog-single-post-listing .thumbnail {
  overflow: hidden;
}
.blog-single-post-listing .thumbnail img {
  transition: 0.8s;
  width: 100%;
}
.blog-single-post-listing .thumbnail:hover img {
  transform: scale(1.2);
}
.blog-single-post-listing .blog-listing-content {
  padding: 50px;
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing .blog-listing-content {
    padding: 25px 10px;
  }
}
.blog-single-post-listing .blog-listing-content .user-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}
.blog-single-post-listing .blog-listing-content .user-info .single {
  margin-right: 30px;
  min-width: max-content;
}
@media only screen and (max-width: 767px) {
  .blog-single-post-listing .blog-listing-content .user-info .single {
    margin-right: 5px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing .blog-listing-content .user-info .single {
    margin-right: 5px;
  }
}
.blog-single-post-listing .blog-listing-content .user-info .single i {
  margin-right: 10px;
  color: var(--color-primary);
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing .blog-listing-content .user-info .single i {
    margin-right: 2px;
    font-size: 14px;
  }
}
@media only screen and (max-width: 575px) {
  .blog-single-post-listing .blog-listing-content .user-info .single span {
    font-size: 13px;
  }
}
.blog-single-post-listing .blog-listing-content .blog-title {
  transition: 0.3s;
}
.blog-single-post-listing .blog-listing-content .blog-title .title {
  transition: 0.3s;
  margin-bottom: 16px;
}
.blog-single-post-listing .blog-listing-content .blog-title:hover .title {
  color: var(--color-primary);
}
.blog-single-post-listing .blog-listing-content p.disc {
  font-size: 16px;
  line-height: 26px;
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing .blog-listing-content p.disc {
    margin-bottom: 15px;
  }
}
.blog-single-post-listing .blog-listing-content a.rts-btn {
  margin-top: 35px;
  display: block;
  max-width: max-content;
}
@media only screen and (max-width: 479px) {
  .blog-single-post-listing .blog-listing-content a.rts-btn {
    margin-top: 20px;
  }
}

.cta-one-wrapper {
  padding: 80px 97px;
  background: #F6F6F6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 575px) {
  .cta-one-wrapper {
    padding: 35px 40px;
  }
}
@media only screen and (max-width: 479px) {
  .cta-one-wrapper {
    padding: 30px 15px;
  }
}
@media only screen and (max-width: 767px) {
  .cta-one-wrapper .right {
    display: none;
  }
}
.cta-one-wrapper .left-area {
  max-width: 600px;
}
.cta-one-wrapper .left-area .title {
  font-size: 48px;
  font-weight: 400;
  line-height: 1.4;
}
@media only screen and (max-width: 575px) {
  .cta-one-wrapper .left-area .title {
    font-size: 30px;
  }
}
.cta-one-wrapper .left-area p {
  font-size: 20px;
}
@media only screen and (max-width: 575px) {
  .cta-one-wrapper .left-area p {
    font-size: 16px;
  }
}
.cta-one-wrapper .left-area .rts-btn img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7495%) hue-rotate(205deg) brightness(112%) contrast(101%);
}
@media only screen and (max-width: 575px) {
  .cta-one-wrapper .right {
    display: none;
  }
}

.bg_cta-one {
  background-image: url(../images/cta/01.webp);
  background-repeat: no-repeat;
  background-position: center center;
  /* position: relative; */
  background-size: cover;
}

.main-area-wrapper-cta {
  max-width: 768px;
  margin: auto;
  text-align: center;
}
.main-area-wrapper-cta .pre-title {
  padding: 4px 16px;
  display: block;
  max-width: max-content;
  margin: auto;
  color: #fff;
  border-radius: 32px;
  border: 1px solid #C2C2FF;
}
.main-area-wrapper-cta .title {
  text-align: center;
  font-size: 64px;
  color: #fff;
  margin-top: 25px;
}
@media only screen and (max-width: 767px) {
  .main-area-wrapper-cta .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 479px) {
  .main-area-wrapper-cta .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 575px) {
  .main-area-wrapper-cta .title {
    font-size: 28px;
  }
}
.main-area-wrapper-cta p {
  color: #FFFFFF;
  font-size: 20px;
}
@media only screen and (max-width: 575px) {
  .main-area-wrapper-cta p {
    font-size: 18px;
  }
}
.main-area-wrapper-cta .rts-btn {
  margin: auto;
  background: #fff;
}
.main-area-wrapper-cta .rts-btn svg {
  max-width: 24px;
}
.main-area-wrapper-cta .rts-btn svg path {
  stroke: #262626;
  transition: 0.3s;
}
.main-area-wrapper-cta .rts-btn:hover svg path {
  stroke: #fff;
}

.rts-cta-area-two {
  position: relative;
}
@media only screen and (max-width: 767px) {
  .rts-cta-area-two .shape-iamge {
    display: none;
  }
}
.rts-cta-area-two .shape-iamge .one {
  position: absolute;
  right: 0;
  top: 0;
}
.rts-cta-area-two .shape-iamge .two {
  position: absolute;
  left: 0;
  bottom: 0;
}

.section-seperator-top {
  position: relative;
}
.section-seperator-top::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  background: #D1D1D1;
}

.cta-two-wrapper {
  background: #262626;
  clip-path: polygon(0% 6.881%, 100% 0%, 100% 93.119%, 0% 100%, 0% 6.881%);
  height: 430px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
}
.cta-two-wrapper .shape-area .one {
  position: absolute;
  right: 0;
  top: 0;
}
.cta-two-wrapper .shape-area .two {
  position: absolute;
  left: 0;
  bottom: 0;
}
.cta-two-wrapper * {
  color: white;
}
.cta-two-wrapper .rts-btn {
  margin: auto;
}
.cta-two-wrapper .rts-btn img {
  filter: brightness(0) saturate(100%) invert(81%) sepia(95%) saturate(0%) hue-rotate(86deg) brightness(105%) contrast(105%);
}

.image--large-video {
  position: relative;
}

.box-shadow-none {
  box-shadow: none !important;
  border: 1px solid #D1D1D1 !important;
}

.cta-main-wrapper-area-four {
  background: #F5F5FF;
  text-align: center;
}
.cta-main-wrapper-area-four .inner {
  max-width: 768px;
  margin: auto;
}
.cta-main-wrapper-area-four .inner .title {
  font-size: 40px;
}
@media only screen and (max-width: 479px) {
  .cta-main-wrapper-area-four .inner .title {
    font-size: 26px;
  }
}
.cta-main-wrapper-area-four .inner p.disc {
  font-size: 20px;
  line-height: 1.5;
  color: #262626;
}
@media only screen and (max-width: 479px) {
  .cta-main-wrapper-area-four .inner p.disc {
    font-size: 16px;
  }
}
.cta-main-wrapper-area-four .inner .rts-btn {
  margin: auto;
}

.rts-call-to-action-area-about {
  text-align: center;
}
.rts-call-to-action-area-about .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .rts-call-to-action-area-about .title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 479px) {
  .rts-call-to-action-area-about .title {
    font-size: 26px;
  }
}
.rts-call-to-action-area-about p.disc {
  max-width: 50%;
  margin: auto;
  margin-bottom: 25px;
  display: block;
}
@media only screen and (max-width: 767px) {
  .rts-call-to-action-area-about p.disc {
    max-width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .rts-call-to-action-area-about p.disc {
    max-width: 100%;
  }
}
.rts-call-to-action-area-about .rts-btn {
  margin: auto;
}
.rts-call-to-action-area-about .rts-btn img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(332deg) brightness(102%) contrast(101%);
}

.header-style-one.header--sticky.sticky .header-wrapper-1 {
  margin-bottom: 0;
}

.call-to-action-bg-dark-area {
  background: #0B0A33;
  padding: 128px 50px;
  text-align: center;
}
@media only screen and (max-width: 575px) {
  .call-to-action-bg-dark-area {
    padding: 50px 15px;
  }
}
.call-to-action-bg-dark-area * {
  color: #fff;
}
.call-to-action-bg-dark-area .title {
  font-size: 48px;
  margin-bottom: 10px;
}
@media only screen and (max-width: 575px) {
  .call-to-action-bg-dark-area .title {
    line-height: 1.4;
    font-size: 36px;
  }
}
.call-to-action-bg-dark-area .rts-btn {
  margin: auto;
}

.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area {
  position: relative;
}
.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img {
  position: absolute;
}
@media only screen and (max-width: 767px) {
  .rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img {
    display: none;
  }
}
.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img.one {
  right: 0;
  top: 20%;
}
.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img.two {
  left: 20%;
  top: 0%;
}
.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img.three {
  left: 10%;
  bottom: 0%;
}
.rts-call-to-action-area-bg-dark .call-to-action-bg-dark-area .bg-shape img.four {
  right: 20%;
  bottom: 0%;
}

.get-quote-area-service-wrapper {
  text-align: center;
  padding-bottom: 80px;
}
.get-quote-area-service-wrapper .title {
  font-size: 64px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 767px) {
  .get-quote-area-service-wrapper .title {
    font-size: 34px;
  }
}
.get-quote-area-service-wrapper p {
  font-size: 20px;
}
.get-quote-area-service-wrapper .rts-btn {
  margin: auto;
}

.have-you-any-questions-area-service .shape-left-right img {
  position: absolute;
}
@media only screen and (max-width: 1199px) {
  .have-you-any-questions-area-service .shape-left-right img {
    display: none;
  }
}
.have-you-any-questions-area-service .shape-left-right img.left {
  left: 220px;
  top: 63%;
  transform: translateY(-50%);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .have-you-any-questions-area-service .shape-left-right img.left {
    left: 50px;
  }
}
@media only screen and (max-width: 1199px) {
  .have-you-any-questions-area-service .shape-left-right img.left {
    left: 50px;
  }
}
.have-you-any-questions-area-service .shape-left-right img.right {
  right: 220px;
  top: 63%;
  transform: translateY(-50%);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .have-you-any-questions-area-service .shape-left-right img.right {
    right: 50px;
  }
}

.large-video-area .vedio-icone .video-play-button::after {
  width: 120px;
  height: 120px;
}
@media only screen and (max-width: 575px) {
  .large-video-area .vedio-icone .video-play-button::after {
    width: 80px;
    height: 80px;
  }
}
.large-video-area .vedio-icone .video-play-button span {
  border-left: 20px solid var(--color-primary);
  border-top: 14px solid transparent;
  border-bottom: 14px solid transparent;
}

.single-nav-area-footer p.parent {
  color: #262626;
  font-size: 14px;
  opacity: 0.7;
  margin-bottom: 15px;
  font-family: var(--font-medium);
}
.single-nav-area-footer ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.single-nav-area-footer ul li {
  margin: 17px 0;
}
.single-nav-area-footer ul li a {
  color: #262626;
  font-weight: 500;
  max-width: max-content;
  position: relative;
  font-family: var(--font-medium);
}
.single-nav-area-footer ul li a::after {
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 100%;
  background: #D1D1D1;
  height: 1px;
  content: "";
  transition: 0.3s;
}
.single-nav-area-footer ul li a::before {
  position: absolute;
  right: 0;
  bottom: -5px;
  width: 0%;
  background: var(--color-primary);
  height: 1px;
  content: "";
  transition: 0.3s;
  z-index: 1;
}
.single-nav-area-footer ul li a:hover {
  color: var(--color-primary);
}
.single-nav-area-footer ul li a:hover::before {
  width: 100%;
  left: 0;
  right: 0;
}

.rts-footer-area .logo {
  display: block;
  margin-bottom: 20px;
}
.rts-footer-area .logo-area {
  max-width: 322px;
}
@media only screen and (max-width: 991px) {
  .rts-footer-area .logo-area {
    max-width: 100%;
    margin-bottom: 30px;
  }
}
.rts-footer-area .logo-area p.disc {
  font-size: 16px;
}
@media only screen and (max-width: 479px) {
  .rts-footer-area .logo-area p.disc {
    font-size: 14px;
  }
}

.rts-copyright-area-one .copyright-wrapper {
  border-top: 1px solid #D1D1D1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 27px 0;
  padding-bottom: 70px;
}
@media only screen and (max-width: 575px) {
  .rts-copyright-area-one .copyright-wrapper {
    flex-direction: column;
    gap: 20px;
  }
}
.rts-copyright-area-one .copyright-wrapper p {
  margin: 0;
}
.rts-copyright-area-one ul {
  display: flex;
  align-items: center;
  gap: 24px;
  list-style: none;
  padding: 0;
  margin: 0;
}
.rts-copyright-area-one ul li {
  margin: 0;
  padding: 0;
}
.rts-copyright-area-one ul li a {
  transition: 0.3s;
}
.rts-copyright-area-one ul li a i {
  transition: 0.3s;
  font-size: 24px;
}
.rts-copyright-area-one ul li a:hover i {
  transform: scale(1.2);
}

.gradient-footer-wrapper {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F5FF 100%);
}

.bg_color-dark-5 {
  background: #0B0A33;
}

.title-center-footer-5 {
  text-align: center;
}
.title-center-footer-5 .title {
  font-size: 64px;
  color: #fff;
}
@media only screen and (max-width: 575px) {
  .title-center-footer-5 .title {
    font-size: 44px;
  }
}

.map-location-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  margin-top: 100px;
}
@media only screen and (max-width: 1199px) {
  .map-location-area {
    flex-wrap: wrap;
  }
}
@media only screen and (max-width: 575px) {
  .map-location-area {
    justify-content: center;
  }
}
.map-location-area * {
  color: #fff;
}
.map-location-area .location-single {
  text-align: center;
  position: relative;
}
.map-location-area .location-single:last-child::after {
  display: none;
}
.map-location-area .location-single::after {
  top: 0;
  content: "";
  position: absolute;
  right: -80px;
  height: 100%;
  width: 1px;
  background: #FFFFFF;
  opacity: 0.25;
}
@media only screen and (max-width: 1199px) {
  .map-location-area .location-single::after {
    right: -40px;
  }
}
@media only screen and (max-width: 991px) {
  .map-location-area .location-single::after {
    display: none;
  }
}
.map-location-area .location-single .title {
  font-size: 40px;
}
@media only screen and (max-width: 575px) {
  .map-location-area .location-single .title {
    font-size: 28px;
  }
}
.map-location-area .location-single a {
  display: block;
  transition: 0.3s;
  font-size: 20px;
  color: #D1D1D1;
  margin: 2px 0;
}
.map-location-area .location-single a:hover {
  color: var(--color-primary);
}

.footer-copyright-area-small {
  padding: 40px 0;
  background: var(--color-primary);
}
.footer-copyright-area-small * {
  color: #fff;
}
.footer-copyright-area-small .small-footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 575px) {
  .footer-copyright-area-small .small-footer-wrapper {
    flex-direction: column;
    align-items: center;
  }
}
.footer-copyright-area-small .small-footer-wrapper p {
  margin: 0;
  font-size: 16px;
}
.footer-copyright-area-small .small-footer-wrapper .nav-social-area ul {
  gap: 24px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}

.title-center-style-two {
  text-align: center;
}

.title-center-style-two .title {
  font-weight: 400;
}
@media only screen and (max-width: 575px) {
  .title-center-style-two .title {
    font-size: 24px;
  }
}

.single-service-style-two {
  background: #F6F6F6;
  padding: 48px 32px;
  transition: 0.3s;
  border-top: 3px solid #E7E7E7;
}
.single-service-style-two .inner {
  overflow: hidden;
  height: 184px;
  display: flex;
  flex-direction: column;
}
.single-service-style-two .inner .icon {
  margin-bottom: 114px;
  transition: 0.3s;
}
.single-service-style-two .inner .icon img {
  transition: 0.3s;
}
.single-service-style-two .inner .bottom {
  transition: 0.3s;
}
.single-service-style-two .inner .bottom .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28px;
  font-weight: 400;
  transition: 0.3s;
}
@media only screen and (max-width: 575px) {
  .single-service-style-two .inner .bottom .title {
    font-size: 18px;
  }
}
.single-service-style-two .inner .bottom .title svg path {
  stroke: #262626;
}
.single-service-style-two .inner .bottom .title img {
  transition: 0.3s;
}
.single-service-style-two:hover {
  background: #0077AD;
  border-color: #2A2ACC;
}
.single-service-style-two:hover * {
  color: #fff;
}
.single-service-style-two:hover svg path {
  stroke: #fff !important;
}
.single-service-style-two:hover .inner .icon {
  margin-bottom: 45px;
}
.single-service-style-two:hover .inner .icon img {
  filter: brightness(0) saturate(100%) invert(99%) sepia(5%) saturate(2%) hue-rotate(32deg) brightness(104%) contrast(100%);
}
.single-service-style-two:hover .inner .title img {
  filter: brightness(0) saturate(100%) invert(99%) sepia(5%) saturate(2%) hue-rotate(32deg) brightness(104%) contrast(100%);
}

.bg-dark-1 {
  background: #262626;
}
.bg-dark-1 .title-main-wrapper-center-three .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .bg-dark-1 .title-main-wrapper-center-three .title {
    font-size: 34px;
  }
}
.bg-dark-1 .title-main-wrapper-center-three * {
  color: #fff;
}

.single-service-three {
  padding: 48px;
}
.single-service-three.bg-light-3 {
  background: #3D3D3D;
}
.single-service-three .icon {
  margin-bottom: 60px;
  height: 48px;
  width: 48px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.single-service-three .title {
  font-size: 24px;
}
.single-service-three * {
  color: #fff;
}

.mt-dec-service-3 {
  margin-top: -134px;
  position: relative;
  z-index: 10;
}

.single-service-area-4 {
  border: 1px solid #D1D1D1;
  padding: 48px 32px;
  background: #FFFFFF;
  transition: 0.3s;
  height: 100%;
}
.single-service-area-4.in-about-page {
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  border: none;
  border-top: 3px solid var(--color-primary);
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .single-service-area-4.in-about-page {
    height: 100%;
  }
}
.single-service-area-4.in-about-page .icon {
  margin-bottom: 80px;
}
.single-service-area-4.in-about-page .title-area .title {
  font-size: 28px;
  margin: 0;
}
.single-service-area-4.in-about-page .title-area img {
  max-width: 28px;
  max-height: 28px;
}
.single-service-area-4 .icon {
  margin-bottom: 148px;
}
.single-service-area-4 .icon img {
  transition: 0.3s;
}
.single-service-area-4 .title-area a .title {
  transition: 0.3s;
  margin-bottom: 0;
}
.single-service-area-4 .title-area svg path {
  stroke: #262626 !important;
}
.single-service-area-4 .title-area a {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-service-area-4 .title-area a a:hover .title {
  color: var(--color-primary);
}
.single-service-area-4:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
}
.single-service-area-4:hover .icon img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(6deg) brightness(103%) contrast(102%);
}
.single-service-area-4:hover * {
  color: #fff;
}
.single-service-area-4:hover svg path {
  stroke: #fff !important;
}

.bg-4 {
  background: #F5F5FF;
}

.title-style-4-center {
  text-align: center;
}
.title-style-4-center .title {
  font-size: 40px;
  font-weight: var(--font-medium);
}
@media only screen and (max-width: 575px) {
  .title-style-4-center .title {
    font-size: 30px;
  }
}

.single-service-area-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 32px 24px;
  background: #FFFFFF;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  transition: 0.3s;
  height: 100%;
}
.single-service-area-wrapper img {
  transition: 0.3s;
}
.single-service-area-wrapper .info {
  flex-basis: 80%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-service-area-wrapper .info .title {
  margin: 0;
  transition: 0.3s;
}
.single-service-area-wrapper svg path {
  transition: 0.3s;
}
.single-service-area-wrapper:hover {
  background: var(--color-primary);
  transform: translateY(-5px);
}
.single-service-area-wrapper:hover * {
  color: #fff;
}
.single-service-area-wrapper:hover svg path {
  stroke: #fff;
}
.single-service-area-wrapper:hover img {
  filter: brightness(0) saturate(100%) invert(98%) sepia(15%) saturate(0%) hue-rotate(259deg) brightness(118%) contrast(100%);
}

.service-list-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .service-list-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 30px;
  }
}
.service-list-between .left {
  max-width: 480px;
}
.service-list-between .left span {
  color: #908FFF;
  font-weight: 600;
  display: block;
  margin-bottom: 25px;
  font-size: 28px;
}
.service-list-between .left .title {
  font-size: 64px;
}
@media only screen and (max-width: 575px) {
  .service-list-between .left .title {
    font-size: 44px;
  }
}
.service-list-between .right-area-thumbnail {
  max-width: 500px;
  margin-left: auto;
  height: 500px;
}
@media only screen and (max-width: 767px) {
  .service-list-between .right-area-thumbnail {
    margin-left: 0;
    width: 100%;
    max-width: 100%;
    height: auto;
  }
}
@media only screen and (max-width: 767px) {
  .service-list-between .right-area-thumbnail img {
    width: 100%;
  }
}

.single-service-border-top {
  padding-top: 45px;
  border-top: 2px solid rgba(0, 0, 0, 0.1);
}
.single-service-border-top .icon {
  margin-bottom: 30px;
}

.bg-gradient-5 {
  background: #F5F5FF;
}

.bg-gradient-5-bold {
  background: #EBEBFF;
}

.has-dropdown:hover .nav-link {
  color: var(--color-primary);
}

.rts-service-provide-area {
  background: #F6F6F6;
}

.container-s-float {
  max-width: 1484px;
  margin: auto;
}

.single-service-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 45px 110px;
  transition: 0.3s;
}
@media only screen and (max-width: 1199px) {
  .single-service-list {
    padding: 45px 50px;
  }
}
@media only screen and (max-width: 991px) {
  .single-service-list {
    flex-direction: column;
    align-items: flex-start;
    gap: 25px;
  }
}
@media only screen and (max-width: 575px) {
  .single-service-list {
    padding: 35px 25px;
  }
}
.single-service-list.active {
  background: #FFFFFF;
}
.single-service-list:hover {
  background: #FFFFFF;
}
.single-service-list .title {
  font-size: 40px;
}
@media only screen and (max-width: 767px) {
  .single-service-list .title {
    line-height: 1.4;
    font-size: 32px;
  }
}
.single-service-list p.disc {
  max-width: 640px;
  font-size: 20px;
  line-height: 1.5;
}
.single-service-list .icon {
  height: 96px;
  width: 96px;
  background: #0077AD;
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-service-list .arrow-btn {
  border: 1px solid #D1D1D1;
  height: 48px;
  width: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}
.single-service-list .arrow-btn img {
  transition: 0.3s;
  filter: brightness(0) saturate(100%) invert(19%) sepia(71%) saturate(7461%) hue-rotate(244deg) brightness(100%) contrast(102%);
}
.single-service-list .arrow-btn:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
}
.single-service-list .arrow-btn:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(79deg) brightness(105%) contrast(102%);
}

.thumbnail-bannr-service-right {
  height: 500px;
}
@media only screen and (max-width: 991px) {
  .thumbnail-bannr-service-right {
    height: auto;
  }
}

.large-image-area-bg-service-page {
  background-image: url(../images/service/05.webp);
  height: 750px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
@media only screen and (max-width: 575px) {
  .large-image-area-bg-service-page {
    height: 550px;
  }
}

.exparts-area-main-mt-dec {
  background: #fff;
  padding: 80px;
  margin-top: -80px;
  position: relative;
  z-index: 10;
}
@media only screen and (max-width: 575px) {
  .exparts-area-main-mt-dec {
    padding: 80px 20px;
  }
}
.exparts-area-main-mt-dec .title {
  font-size: 48px;
}
@media only screen and (max-width: 991px) {
  .exparts-area-main-mt-dec .title {
    font-size: 36px;
  }
}
@media only screen and (max-width: 575px) {
  .exparts-area-main-mt-dec .title {
    font-size: 32px;
  }
}
.exparts-area-main-mt-dec .title-area-center-inner-with-sub {
  max-width: 75%;
}
@media only screen and (max-width: 991px) {
  .exparts-area-main-mt-dec .title-area-center-inner-with-sub {
    max-width: 100%;
  }
}
.exparts-area-main-mt-dec .title-area-center-inner-with-sub .rts-btn {
  margin: auto;
  margin-top: 40px;
}
.exparts-area-main-mt-dec .title-area-center-inner-with-sub .rts-btn svg path {
  stroke: #fff;
}

.rts-btn.btn-primary svg {
  max-width: 24px;
}
.rts-btn.btn-primary svg path {
  stroke: #fff;
}
.rts-btn.btn-primary:hover svg path {
  stroke: #2A2ACC !important;
}

.service-area-details-wrapper .inner-content {
  position: relative;
  z-index: 5;
  background: #fff;
}
.service-area-details-wrapper .top {
  text-align: center;
  margin: auto;
  margin-top: -220px;
  padding-top: 80px;
  max-width: 60%;
}
@media only screen and (max-width: 1199px) {
  .service-area-details-wrapper .top {
    max-width: 100%;
  }
}
.service-area-details-wrapper .top p.disc {
  max-width: 60%;
  margin: auto;
}
@media only screen and (max-width: 575px) {
  .service-area-details-wrapper .top p.disc {
    max-width: 90%;
  }
}
.service-area-details-wrapper .mid-content {
  padding: 130px 100px;
}
@media only screen and (max-width: 767px) {
  .service-area-details-wrapper .mid-content {
    padding: 120px 50px;
  }
}
@media only screen and (max-width: 575px) {
  .service-area-details-wrapper .mid-content {
    padding: 80px 20px;
  }
}
@media only screen and (max-width: 479px) {
  .service-area-details-wrapper .mid-content {
    padding: 70px 15px;
  }
}
.service-area-details-wrapper .mid-content p.disc {
  font-size: 24px;
  line-height: 1.5;
}
@media only screen and (max-width: 1199px) {
  .service-area-details-wrapper .mid-content p.disc {
    font-size: 18px;
  }
}

.thumbnail-area-large-service-detaile-mid {
  height: 700px;
  clip-path: polygon(0% 5%, 100% 0%, 100% 95%, 0% 100%, 0% 5%);
}
@media only screen and (max-width: 1199px) {
  .thumbnail-area-large-service-detaile-mid {
    height: auto;
  }
}

.service-area-details-wrapper.border-bottom {
  border: none !important;
}
.service-area-details-wrapper.border-bottom .inner-content {
  border-bottom: 1px solid #D1D1D1;
}

.float-right-style {
  width: 124%;
  max-width: 124%;
}
@media only screen and (max-width: 575px) {
  .float-right-style {
    width: 100%;
  }
}

.innovative-solution {
  position: relative;
}
.innovative-solution .shape-top-right {
  position: absolute;
  top: -24px;
  left: 242px;
}

.luminos-main-solutioin-key .title {
  font-size: 36px;
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .luminos-main-solutioin-key .title {
    font-size: 28px;
  }
}
.luminos-main-solutioin-key .check-wrapper {
  margin-top: 30px;
}
.luminos-main-solutioin-key .single-check {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 18px;
}
.luminos-main-solutioin-key .single-check p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.luminos-main-solutioin-key .tag-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 70px;
}
@media only screen and (max-width: 767px) {
  .luminos-main-solutioin-key .tag-wrapper {
    margin-top: 30px;
    flex-wrap: wrap;
  }
}
.luminos-main-solutioin-key .tag-wrapper .single-tag {
  cursor: pointer;
}
.luminos-main-solutioin-key .tag-wrapper .single-tag span {
  display: block;
  padding: 5px 10px;
  border-radius: 22px;
  border: 1px solid #0F62FE;
  color: var(--color-primary);
  transition: 0.3s;
}
.luminos-main-solutioin-key .tag-wrapper .single-tag:hover span {
  color: #fff;
  background: var(--color-primary);
}

.single-working-process-choose-us {
  padding: 40px;
  border: 1px solid #D1D1D1;
  margin-bottom: 22px;
  border-radius: 10px;
  transition: 0.3s;
}
@media only screen and (max-width: 767px) {
  .single-working-process-choose-us {
    padding: 15px;
  }
}
.single-working-process-choose-us:hover {
  border-color: var(--color-primary);
}
.single-working-process-choose-us .title {
  font-size: 24px;
  font-weight: 500;
}
.single-working-process-choose-us p {
  margin-bottom: 31px;
}
.single-working-process-choose-us .tag-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}
@media only screen and (max-width: 767px) {
  .single-working-process-choose-us .tag-wrapper {
    flex-wrap: wrap;
  }
}
.single-working-process-choose-us .tag-wrapper .single {
  display: block;
  padding: 5px 15px;
  border: 1px solid #D1D1D1;
  border-radius: 33px;
  transition: 0.3s;
  cursor: pointer;
}
.single-working-process-choose-us .tag-wrapper .single:hover {
  background: var(--color-primary);
  color: #fff;
  border: 1px solid var(--color-primary);
}

.index-five {
  overflow-x: visible;
}

.service-sticky-wrapper-main {
  height: auto;
}

.service-section-area {
  position: sticky;
  top: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .service-section-area {
    top: 0;
  }
}

.vedio-icone .video-play-button {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  box-sizing: content-box;
  display: block;
  width: 32px;
  height: 44px;
  border-radius: 50%;
  padding: 18px 20px 18px 28px;
  display: flex;
}
.vedio-icone .video-play-button::before {
  content: "";
  position: absolute;
  z-index: 0;
  left: -32%;
  top: -31%;
  display: block;
  width: 130px;
  height: 130px;
  background: transparent;
  border-radius: 50%;
  border: 1px solid var(--color-primary);
  animation: waves 3s ease-in-out infinite;
}
.vedio-icone .video-play-button::after {
  content: "";
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 60px;
  height: 60px;
  background: #fff;
  transition: all 200ms;
  border-radius: 50%;
}
.vedio-icone .video-play-button span {
  display: block;
  position: relative;
  z-index: 3;
  width: 0;
  height: 0;
  border-left: 15px solid var(--color-primary);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  top: 50%;
  transform: translate(-50%, -50%);
  left: 47%;
}
.vedio-icone .video-play-button span.outer-text {
  border: none;
  min-width: max-content;
  margin-left: 75px;
  position: relative;
  margin-top: -12px;
  color: var(--color-primary);
  font-weight: 500;
}
.vedio-icone .video-overlay {
  position: fixed;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  opacity: 0;
  transition: all ease 500ms;
  display: none;
}
.vedio-icone .video-overlay iframe {
  width: 70%;
  height: 70%;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  top: 50%;
  position: relative;
  transform: translateY(-50%);
}
.vedio-icone .video-overlay.open {
  position: fixed;
  z-index: 1000;
  opacity: 1;
  display: block;
}
.vedio-icone .video-overlay .video-overlay-close {
  position: absolute;
  z-index: 1000;
  top: 15px;
  right: 20px;
  font-size: 36px;
  line-height: 1;
  font-weight: 400;
  color: #fff;
  text-decoration: none;
  cursor: pointer;
  transition: all 200ms;
}

.rts-bg-video-area-large {
  height: 640px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(../images/banner/05.webp);
}
@media only screen and (max-width: 575px) {
  .rts-bg-video-area-large {
    height: 400px;
  }
}

.map-tooltip {
  position: relative;
}
.map-tooltip::after, .map-tooltip::before {
  --scale: 0;
  --arrow-size: 8px;
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%) translateY(var(--translate-y, 0)) scale(var(--scale));
  transition: 150ms transform;
  transform-origin: bottom center;
}
.map-tooltip::before {
  --translate-y: calc(-100% - var(--arrow-size));
  content: attr(data-tooltip);
  color: var(--color-white);
  padding: 5px 8px;
  background: var(--color-primary);
  width: max-content;
  border-radius: 5px;
  text-align: center;
}
.map-tooltip::after {
  --translate-y: calc(-1 * var(--arrow-size));
  content: "";
  border: var(--arrow-size) solid transparent;
  border-top-color: var(--color-primary);
}
.map-tooltip:hover::before, .map-tooltip:hover::after {
  --scale: 1;
}

.thumbnail-map {
  position: relative;
}

.map-tool-tip-single {
  position: absolute;
  top: 50%;
  left: 50%;
}
.map-tool-tip-single.two {
  right: 150px;
  left: auto;
  top: 120px;
}
.map-tool-tip-single.three {
  left: 150px;
  right: auto;
  top: 150px;
}
.map-tool-tip-single.four {
  left: 500px;
  right: auto;
  top: 60px;
}
.map-tool-tip-single.five {
  right: 100px;
  bottom: 120px;
  top: auto;
  left: auto;
}
.map-tool-tip-single.six {
  left: 350px;
  bottom: 120px;
  top: auto;
  right: auto;
}
.map-tool-tip-single .map-tooltip span {
  display: block;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: var(--color-primary);
  animation: pulse-2 1s linear infinite;
}

.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 10000;
  opacity: 1;
  visibility: hidden;
  transform: translateY(15px);
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.progress-wrap::after {
  position: absolute;
  font-family: var(--font-3);
  content: "\f077";
  text-align: center;
  line-height: 46px;
  font-size: 24px;
  color: var(--color-primary);
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  border: 1px solid var(--color-primary);
  border: none !important;
  box-shadow: none;
  border-radius: 50% !important;
  border-radius: 5px;
}

.progress-wrap:hover::after {
  opacity: 1;
  content: "\f077";
}

.progress-wrap::before {
  position: absolute;
  font-family: var(--font-3);
  content: "\f077";
  text-align: center;
  line-height: 46px;
  font-size: 24px;
  opacity: 0;
  background: var(--color-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 2;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap:hover::before {
  opacity: 0;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg {
  color: var(--color-primary);
  border-radius: 50%;
  background: transparent;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--color-primary);
  stroke-width: 4px;
  box-sizing: border-box;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.home-blue .progress-wrap svg.progress-circle path {
  stroke: var(--color-primary);
}
.home-blue .progress-wrap::after {
  border-color: var(--color-primary);
  box-shadow: 0px 3px 20px 6px rgba(7, 66, 233, 0.3215686275);
  color: var(--color-primary);
}

.benefits-area-title-wrapper {
  text-align: center;
}
.benefits-area-title-wrapper .title {
  font-size: 28px;
}

.single-benefits-area-wrapper {
  padding: 30px;
}
.single-benefits-area-wrapper.bg-light {
  background: #F6F6F6;
}
.single-benefits-area-wrapper .icon {
  margin-bottom: 30px;
}

.contact-page-banner {
  height: 450px !important;
  background-image: url(../images/contact/02.webp);
}
@media only screen and (max-width: 575px) {
  .contact-page-banner {
    height: 250px !important;
  }
}

.container-contact {
  max-width: 768px;
  margin: auto;
}

.bg_iamge {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.contact-form {
  margin-top: 50px;
}
.contact-form input {
  border-radius: 0;
  height: 44PX;
  border: 1px solid #D1D1D1;
  font-size: 16px;
  color: #6D6D6D;
  padding: 12px;
}
.contact-form input:focus {
  border: 1px solid var(--color-primary);
}
.contact-form .half-input-wrapper {
  display: flex;
  align-items: center;
  gap: 32px;
}
@media only screen and (max-width: 575px) {
  .contact-form .half-input-wrapper {
    flex-direction: column;
    gap: 0;
  }
}
.contact-form .single {
  width: 100%;
  margin-bottom: 23px;
}
.contact-form .single label {
  margin-bottom: 7px;
  font-size: 14px;
  color: #4F4F4F;
  font-weight: 500;
}
.contact-form textarea {
  height: 133px;
  border-radius: 0;
  border: 1px solid #D1D1D1;
  font-size: 16px;
  color: #6D6D6D;
  padding: 12px;
}
.contact-form textarea:focus {
  border: 1px solid var(--color-primary);
}
.contact-form .form-check {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.contact-form .form-check label {
  margin: 0;
  padding-left: 10px;
  font-size: 14px;
  font-weight: 500;
}
.contact-form .form-check label::after, .contact-form .form-check label::before {
  display: none;
}

.single-location-area-contact {
  text-align: center;
}
.single-location-area-contact .icon {
  margin-bottom: 16px;
}
.single-location-area-contact .icon i {
  color: var(--color-primary);
  font-size: 24px;
}
.single-location-area-contact p {
  margin-bottom: 12px;
  font-size: 20px;
}
.single-location-area-contact span {
  color: #5D5D5D;
}

.section-seperator-b {
  border-bottom: 1px solid #D1D1D1;
}

.why-choose-us-faq-area {
  background: linear-gradient(101.07deg, #F8FAFF 2.25%, #FDF8FF 85.45%);
}

.why-choose-faq-thumbnail img {
  width: 100%;
}

.faq-why-choose-left-accordion p {
  margin-bottom: 20px;
}
.faq-why-choose-left-accordion .accordion {
  background: transparent;
  max-width: 80%;
}
@media only screen and (max-width: 991px) {
  .faq-why-choose-left-accordion .accordion {
    max-width: 100%;
  }
}
.faq-why-choose-left-accordion .accordion .accordion-item {
  background-color: transparent;
  background: transparent;
  border: none;
  padding-left: 0;
  padding-right: 0;
  border-bottom: 1px solid #EAF0FF;
  padding: 7px 0;
}
.faq-why-choose-left-accordion .accordion .accordion-item .accordion-header {
  background: transparent;
}
.faq-why-choose-left-accordion .accordion .accordion-item .accordion-header button {
  background: transparent;
  font-size: 18px;
  color: #0C1018;
  border-bottom: none;
  box-shadow: none;
  padding-left: 0;
  padding-right: 0;
}
.faq-why-choose-left-accordion .accordion .accordion-body {
  padding-left: 0;
  padding-right: 0;
}

.single-pricing-wrapper-choose {
  padding: 29px;
  border: 1px solid #D1D1D1;
}
.single-pricing-wrapper-choose .pricing-head {
  text-align: center;
}
.single-pricing-wrapper-choose .pricing-head .pre {
  background: #F1F1FF;
  display: block;
  max-width: max-content;
  margin: auto;
  padding: 4px 17px;
  color: var(--color-primary);
}
.single-pricing-wrapper-choose .pricing-head .pricing-area {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 767px) {
  .single-pricing-wrapper-choose .pricing-head .pricing-area .title {
    margin-bottom: 0;
  }
}
.single-pricing-wrapper-choose .pricing-head .pricing-area .time {
  color: rgba(0, 0, 0, 0.4);
}
.single-pricing-wrapper-choose .rts-btn {
  max-width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
  justify-content: space-between;
}
.single-pricing-wrapper-choose .rts-btn.btn-border:hover::after {
  opacity: 1;
  transform: scaleX(2) scaleY(1.5);
}
.single-pricing-wrapper-choose .body .check-single {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
}
.single-pricing-wrapper-choose .body .check-single p {
  margin: 0;
  font-size: 15px;
}
.single-pricing-wrapper-choose .body .check-single.disable p {
  color: rgba(0, 0, 0, 0.4);
}

.career-banner-wrapper .title {
  font-size: 56px;
}
@media only screen and (max-width: 1199px) {
  .career-banner-wrapper .title {
    font-size: 42px;
  }
}
@media only screen and (max-width: 991px) {
  .career-banner-wrapper .title {
    font-size: 44px;
  }
}
@media only screen and (max-width: 767px) {
  .career-banner-wrapper .title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 479px) {
  .career-banner-wrapper .title {
    font-size: 24px;
  }
}

@media only screen and (max-width: 767px) {
  .career-right-two-wrapper .title br {
    display: none;
  }
}

.career-two-section .check-wrapper-main {
  display: flex;
  align-items: flex-start;
  gap: 30px;
}
@media only screen and (max-width: 575px) {
  .career-two-section .check-wrapper-main {
    gap: 0;
    flex-wrap: wrap;
  }
}
.career-two-section .check-wrapper-main .single-check {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin: 15px 0;
}

.career-video-area-large {
  height: 600px;
  border-radius: 10px !important;
  background-image: url(../images/career/03.webp);
}
@media only screen and (max-width: 479px) {
  .career-video-area-large {
    height: 300px;
  }
}
.career-video-area-large .jarallax-container {
  border-radius: 10px !important;
}

.title-between-area-wrapper-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 991px) {
  .title-between-area-wrapper-main {
    flex-direction: column;
    align-items: flex-start;
  }
  .title-between-area-wrapper-main p {
    margin-left: 0 !important;
  }
}
.title-between-area-wrapper-main .title {
  font-size: 48px;
}
@media only screen and (max-width: 767px) {
  .title-between-area-wrapper-main .title {
    font-size: 36px;
  }
}
.title-between-area-wrapper-main p.disc {
  max-width: 515px;
  margin-left: auto;
  font-size: 18px;
  line-height: 1.5;
}

.single-values-in-action {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 36px 30px;
  border-radius: 0;
  border: 1px solid #EAF0FF;
  transition: 0.4s;
}
@media only screen and (max-width: 575px) {
  .single-values-in-action {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}
.single-values-in-action .icon {
  min-width: max-content;
}
.single-values-in-action .information .title {
  font-weight: 500;
  font-size: 24px;
  margin-bottom: 10px;
}
.single-values-in-action:hover {
  border-color: var(--color-primary);
}

.single-job-opening-card {
  border: 1px solid #DFDBF9;
  padding: 40px;
  transition: 0.3s;
}
@media only screen and (max-width: 767px) {
  .single-job-opening-card {
    padding: 25px;
  }
}
.single-job-opening-card:hover {
  border: 1px solid var(--color-primary);
}
.single-job-opening-card .title {
  font-size: 24px;
  margin-bottom: 20px;
}
.single-job-opening-card p {
  margin-bottom: 15px;
}
.single-job-opening-card p span {
  font-weight: 500;
  font-size: 20px;
  color: #050D20;
}
.single-job-opening-card .tag-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}
@media only screen and (max-width: 575px) {
  .single-job-opening-card .tag-wrapper {
    flex-wrap: wrap;
    gap: 25px;
  }
}
.single-job-opening-card .tag-wrapper span {
  display: block;
  border-radius: 20px;
  border: 1px solid #DFDBF9;
  padding: 5px 16px;
}
.single-job-opening-card .bottom-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
}
@media only screen and (max-width: 767px) {
  .single-job-opening-card .bottom-area {
    flex-wrap: wrap;
    gap: 15px;
  }
}
.single-job-opening-card .bottom-area .selary-range {
  padding: 5px 15px;
  background: #EAF0FF;
  border-radius: 5px;
}
.single-job-opening-card .bottom-area .selary-range p {
  font-weight: 600;
  font-size: 20px;
}
.single-job-opening-card .bottom-area .selary-range p span {
  font-size: 18px;
  font-weight: 400;
}
.single-job-opening-card .bottom-area p {
  margin: 0;
}

.apply-bottom-wrapper {
  margin-top: 60px;
}
.apply-bottom-wrapper .benefits-wrapper-card .single {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
}
.apply-bottom-wrapper .benefits-wrapper-card .single p {
  margin: 0;
}

.single-team-style-one {
  text-align: center;
}
.single-team-style-one .thumbnail {
  overflow: hidden;
}
.single-team-style-one .inner-content {
  margin-top: 30px;
}
.single-team-style-one .inner-content .title {
  font-size: 24px;
  margin-bottom: 8px;
  transition: 0.3s;
}
.single-team-style-one .inner-content .title:hover {
  color: var(--color-primary);
}
.single-team-style-one .inner-content .deg {
  font-size: 16px;
  margin-bottom: 0;
}

.title-team-left .title {
  font-size: 48px;
}
@media only screen and (max-width: 575px) {
  .title-team-left .title {
    font-size: 34px;
  }
}

.team-single-banner-area .single-team-content .title {
  font-size: 56px;
  margin-bottom: 25px;
}
@media only screen and (max-width: 575px) {
  .team-single-banner-area .single-team-content .title {
    font-size: 36px;
  }
}
.team-single-banner-area .single-team-content p {
  margin-bottom: 25px;
}
.team-single-banner-area .counter-main-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 45px;
}
@media only screen and (max-width: 575px) {
  .team-single-banner-area .counter-main-wrapper {
    flex-wrap: wrap;
  }
}
.team-single-banner-area .counter-main-wrapper .single {
  padding: 20px 24px;
  border: 1px solid #EAF0FF;
  border-radius: 5px;
}
.team-single-banner-area .counter-main-wrapper .title {
  font-size: 40px;
  margin-bottom: 10px;
}
.team-single-banner-area .social-area-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}
.team-single-banner-area .social-area-wrapper p {
  padding: 0;
  margin: 0;
  font-family: var(--font-medium);
}
.team-single-banner-area .social-area-wrapper .social-one-area {
  display: flex;
}
.team-single-banner-area .social-area-wrapper .social-one-area ul {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 13px;
}

.award-area-inner-page {
  padding: 80px 110px;
  background: linear-gradient(101.07deg, #F8FAFF 2.25%, #FDF8FF 85.45%);
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .award-area-inner-page {
    padding: 60px 70px;
  }
}
@media only screen and (max-width: 575px) {
  .award-area-inner-page {
    padding: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-inner-page .title br {
    display: none;
  }
}

@media (min-width: 992px) {
  .col-lg-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
}
.thumbnail-consultancy {
  height: 560px;
}
@media only screen and (max-width: 1199px) {
  .thumbnail-consultancy {
    height: 433px;
  }
}
@media only screen and (max-width: 991px) {
  .thumbnail-consultancy {
    height: auto;
  }
}
@media only screen and (max-width: 767px) {
  .thumbnail-consultancy {
    height: auto;
  }
}

.container-consulting {
  max-width: 659px;
  margin: auto;
}
@media only screen and (max-width: 767px) {
  .container-consulting {
    padding: 0 15px;
  }
}

.consulting-step {
  position: relative;
}
.consulting-step .timeline-line {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 60.2%;
  width: 4px;
  height: 100%;
  background: #EAF0FF;
}
@media only screen and (max-width: 767px) {
  .consulting-step .timeline-line {
    left: 0;
  }
}

.single-consulting-one {
  display: flex;
  align-items: center;
  gap: 150px;
  justify-content: space-between;
  position: relative;
  margin-bottom: 60px;
}
.single-consulting-one .timeline-dot {
  position: absolute;
  left: 60.4%;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .timeline-dot {
    left: 0;
  }
}
.single-consulting-one .timeline-dot::before {
  z-index: 1;
  width: 2.1rem;
  height: 2.1rem;
  border-radius: 50%;
  background: var(--color-primary);
  box-shadow: 0 0 15px rgba(82, 56, 255, 0.5);
}
.single-consulting-one .timeline-dot::after {
  border: 5px solid #EAF0FF;
  background: #fff;
  z-index: 0;
  width: 20px;
  height: 20px;
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  transition: 0.3s;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .right-area {
    padding-left: 30px;
  }
}
.single-consulting-one .thumbnail {
  max-width: 300px;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .thumbnail {
    display: none;
  }
}
.single-consulting-one:hover .timeline-dot::after {
  border-color: var(--color-primary);
}

.shedule-consulting-left .title {
  font-size: 48px;
  line-height: 1.2;
}
@media only screen and (max-width: 575px) {
  .shedule-consulting-left .title {
    font-size: 26px;
    line-height: 1.4;
  }
  .shedule-consulting-left .title br {
    display: none;
  }
}
.shedule-consulting-left .check-wrapper p.top {
  font-family: var(--font-medium);
  font-size: 20px;
}
.shedule-consulting-left .check-wrapper .single-check {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}
.shedule-consulting-left p.call {
  font-family: var(--font-medium);
  font-size: 20px;
  margin-top: 35px;
}

.consulting-form {
  padding: 50px;
  border: 1px solid #d1d1d1;
  background: #ffffff;
}
@media only screen and (max-width: 575px) {
  .consulting-form {
    padding: 25px 11px;
  }
}
.consulting-form p {
  font-size: 24px;
  font-family: var(--font-medium);
}
@media only screen and (max-width: 479px) {
  .consulting-form p {
    font-size: 18px;
  }
}
.consulting-form input {
  height: 56px;
  margin-bottom: 20px;
  border-radius: 0;
  padding: 12px;
  color: rgba(0, 0, 0, 0.4);
  border-color: #d1d1d1;
}
.consulting-form .input-half-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media only screen and (max-width: 479px) {
  .consulting-form .input-half-wrapper {
    flex-direction: column;
    gap: 0;
  }
}
.consulting-form .input-half-wrapper .single {
  width: 100%;
}
.consulting-form textarea {
  height: 100px;
  margin-bottom: 20px;
  border-radius: 0;
  padding: 12px;
  color: rgba(0, 0, 0, 0.4);
  border-color: #d1d1d1;
}
.consulting-form textarea:focus {
  border: 1px solid var(--color-primary);
}
.consulting-form button {
  max-width: 100%;
}
.consulting-form button::after {
  left: 50%;
}
.consulting-form button:hover::after {
  width: 100%;
  height: 400px;
}

.bg-gradient-one-industry {
  background: linear-gradient(101.07deg, #F8FAFF 2.25%, #FDF8FF 85.45%);
}

.career-right-two-wrapper.industry .single-wrapper .single-check {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 10px 0;
}
.career-right-two-wrapper.industry p.more {
  max-width: 80%;
}

.thumbnail-industry-thumbnail {
  display: flex;
  align-items: center;
  gap: 25px;
}
@media only screen and (max-width: 991px) {
  .thumbnail-industry-thumbnail {
    margin-top: 20px;
  }
}
.thumbnail-industry-thumbnail .top {
  margin-top: -60px;
  position: relative;
}
@media only screen and (max-width: 991px) {
  .thumbnail-industry-thumbnail .top {
    margin-top: 0;
  }
}

.banner-partner-inner-wrapper {
  text-align: center;
  padding: 120px 0 40px 0;
}
@media only screen and (max-width: 575px) {
  .banner-partner-inner-wrapper {
    padding: 70px 0 40px 0;
  }
}
.banner-partner-inner-wrapper .thumbnail-large {
  height: 500px;
}
@media only screen and (max-width: 991px) {
  .banner-partner-inner-wrapper .thumbnail-large {
    height: 300px;
  }
}
@media only screen and (max-width: 767px) {
  .banner-partner-inner-wrapper .thumbnail-large {
    height: 250px;
  }
}
@media only screen and (max-width: 575px) {
  .banner-partner-inner-wrapper .thumbnail-large {
    height: 200px;
  }
}
@media only screen and (max-width: 479px) {
  .banner-partner-inner-wrapper .thumbnail-large {
    height: auto;
  }
}

.partner-large-xl-image {
  height: 573px;
}
@media only screen and (max-width: 991px) {
  .partner-large-xl-image {
    height: auto;
  }
}

.thumbnail-main-wrapper-choose-us {
  height: 383px;
}
@media only screen and (max-width: 767px) {
  .thumbnail-main-wrapper-choose-us {
    height: auto;
    margin-top: 30px;
  }
}

.brand-partner-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
}
.brand-partner-wrapper p {
  flex-basis: 100%;
  margin: 0;
}
@media only screen and (max-width: 991px) {
  .brand-partner-wrapper p br {
    display: none;
  }
}
.brand-partner-wrapper .partner-icon-wrapper {
  flex-basis: 80%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.brand-partner-wrapper .partner-icon-wrapper a {
  max-width: 120px;
  margin: auto;
}

.review-area-partner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 991px) {
  .review-area-partner {
    flex-wrap: wrap;
    gap: 15px;
  }
}
.review-area-partner .left {
  display: flex;
  align-items: center;
  gap: 18px;
}
.review-area-partner .left img {
  max-width: 178px;
}
.review-area-partner .left .information {
  font-family: var(--font-medium);
  font-size: 16px;
}
.review-area-partner .left .information p {
  margin: 0;
  color: var(--color-primary);
}
.review-area-partner .mid {
  display: flex;
  align-items: center;
  gap: 20px;
}
.review-area-partner .mid .title {
  font-size: 36px;
  margin: 0;
  font-weight: 600;
}
.review-area-partner .mid .info p {
  margin: 0;
  font-family: var(--font-medium);
}
.review-area-partner .mid .info p span {
  color: var(--color-primary);
}
.review-area-partner .end {
  max-width: 201px;
}
.review-area-partner .end p {
  margin-left: auto;
  margin-right: 0;
}

.award-area-inner-page {
  padding: 80px 110px;
  background: linear-gradient(101.07deg, #F8FAFF 2.25%, #FDF8FF 85.45%);
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .award-area-inner-page {
    padding: 60px 70px;
  }
}
@media only screen and (max-width: 575px) {
  .award-area-inner-page {
    padding: 40px;
  }
}
@media only screen and (max-width: 767px) {
  .award-area-inner-page .title br {
    display: none;
  }
}

@media (min-width: 992px) {
  .col-lg-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
}
.thumbnail-consultancy {
  height: 560px;
}
@media only screen and (max-width: 1199px) {
  .thumbnail-consultancy {
    height: 433px;
  }
}
@media only screen and (max-width: 991px) {
  .thumbnail-consultancy {
    height: auto;
  }
}
@media only screen and (max-width: 767px) {
  .thumbnail-consultancy {
    height: auto;
  }
}

.container-consulting {
  max-width: 659px;
  margin: auto;
}
@media only screen and (max-width: 767px) {
  .container-consulting {
    padding: 0 15px;
  }
}

.consulting-step {
  position: relative;
}
.consulting-step .timeline-line {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 60.2%;
  width: 4px;
  height: 100%;
  background: #EAF0FF;
}
@media only screen and (max-width: 767px) {
  .consulting-step .timeline-line {
    left: 0;
  }
}

.single-consulting-one {
  display: flex;
  align-items: center;
  gap: 150px;
  justify-content: space-between;
  position: relative;
  margin-bottom: 60px;
}
.single-consulting-one .timeline-dot {
  position: absolute;
  left: 60.4%;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .timeline-dot {
    left: 0;
  }
}
.single-consulting-one .timeline-dot::before {
  z-index: 1;
  width: 2.1rem;
  height: 2.1rem;
  border-radius: 50%;
  background: var(--color-primary);
  box-shadow: 0 0 15px rgba(82, 56, 255, 0.5);
}
.single-consulting-one .timeline-dot::after {
  border: 5px solid #EAF0FF;
  background: #fff;
  z-index: 0;
  width: 20px;
  height: 20px;
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  transition: 0.3s;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .right-area {
    padding-left: 30px;
  }
}
.single-consulting-one .thumbnail {
  max-width: 300px;
}
@media only screen and (max-width: 767px) {
  .single-consulting-one .thumbnail {
    display: none;
  }
}
.single-consulting-one:hover .timeline-dot::after {
  border-color: var(--color-primary);
}

.shedule-consulting-left .title {
  font-size: 48px;
  line-height: 1.2;
}
@media only screen and (max-width: 575px) {
  .shedule-consulting-left .title {
    font-size: 26px;
    line-height: 1.4;
  }
  .shedule-consulting-left .title br {
    display: none;
  }
}
.shedule-consulting-left .check-wrapper p.top {
  font-family: var(--font-medium);
  font-size: 20px;
}
.shedule-consulting-left .check-wrapper .single-check {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}
.shedule-consulting-left p.call {
  font-family: var(--font-medium);
  font-size: 20px;
  margin-top: 35px;
}

.consulting-form {
  padding: 50px;
  border: 1px solid #d1d1d1;
  background: #ffffff;
}
@media only screen and (max-width: 575px) {
  .consulting-form {
    padding: 25px 11px;
  }
}
.consulting-form p {
  font-size: 24px;
  font-family: var(--font-medium);
}
@media only screen and (max-width: 479px) {
  .consulting-form p {
    font-size: 18px;
  }
}
.consulting-form input {
  height: 56px;
  margin-bottom: 20px;
  border-radius: 0;
  padding: 12px;
  color: rgba(0, 0, 0, 0.4);
  border-color: #d1d1d1;
}
.consulting-form .input-half-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media only screen and (max-width: 479px) {
  .consulting-form .input-half-wrapper {
    flex-direction: column;
    gap: 0;
  }
}
.consulting-form .input-half-wrapper .single {
  width: 100%;
}
.consulting-form textarea {
  height: 100px;
  margin-bottom: 20px;
  border-radius: 0;
  padding: 12px;
  color: rgba(0, 0, 0, 0.4);
  border-color: #d1d1d1;
}
.consulting-form textarea:focus {
  border: 1px solid var(--color-primary);
}
.consulting-form button {
  max-width: 100%;
}
.consulting-form button::after {
  left: 50%;
}
.consulting-form button:hover::after {
  width: 100%;
  height: 400px;
}

.bg-gradient-one-industry {
  background: linear-gradient(101.07deg, #F8FAFF 2.25%, #FDF8FF 85.45%);
}

.career-right-two-wrapper.industry .single-wrapper .single-check {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 10px 0;
}
.career-right-two-wrapper.industry p.more {
  max-width: 80%;
}

.thumbnail-industry-thumbnail {
  display: flex;
  align-items: center;
  gap: 25px;
}
@media only screen and (max-width: 991px) {
  .thumbnail-industry-thumbnail {
    margin-top: 20px;
  }
}
.thumbnail-industry-thumbnail .top {
  margin-top: -60px;
  position: relative;
}
@media only screen and (max-width: 991px) {
  .thumbnail-industry-thumbnail .top {
    margin-top: 0;
  }
}

.coming-soon-wrapper-main {
  text-align: center;
}
.coming-soon-wrapper-main h3 {
  font-size: 62px;
  margin-bottom: 70px;
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrapper-main h3 {
    font-size: 44px;
  }
}
.coming-soon-wrapper-main .para {
  font-size: 18px;
  max-width: 40%;
  margin: auto;
  margin-top: 80px;
  font-size: 22px;
  line-height: 1.6;
}
@media only screen and (max-width: 767px) {
  .coming-soon-wrapper-main .para {
    max-width: 95%;
  }
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrapper-main .para {
    margin-top: 40px;
    max-width: 98%;
    font-size: 18px;
  }
}
.coming-soon-wrapper-main #countdown .timer-section {
  justify-content: center;
  display: flex;
  align-items: center;
  gap: 25px;
}
.coming-soon-wrapper-main #countdown .timer-section .time-unit span {
  font-size: 44px;
  color: var(--color-primary);
}
@media only screen and (max-width: 575px) {
  .coming-soon-wrapper-main #countdown .timer-section .time-unit span {
    font-size: 32px;
  }
}

.h-100-vh {
  height: 100vh;
}

.page-not-found-main {
  text-align: center;
}
.page-not-found-main .title {
  font-size: 260px;
  margin-bottom: 5px;
}
@media only screen and (max-width: 575px) {
  .page-not-found-main .title {
    font-size: 200px;
  }
}
@media only screen and (max-width: 479px) {
  .page-not-found-main .title {
    font-size: 150px;
  }
}
.page-not-found-main .para {
  font-size: 120px;
  font-weight: 400;
}
@media only screen and (max-width: 1199px) {
  .page-not-found-main .para {
    font-size: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .page-not-found-main .para {
    font-size: 60px;
  }
}
@media only screen and (max-width: 575px) {
  .page-not-found-main .para {
    font-size: 40px;
  }
}
.page-not-found-main .rts-btn {
  margin: auto;
}

.side-bar {
  position: fixed;
  overflow: hidden;
  top: 0;
  right: -100%;
  width: 465px;
  padding: 40px 30px;
  padding-top: 50px;
  height: 100%;
  display: block;
  background-color: white;
  backdrop-filter: blur(7px);
  z-index: 1900;
  transition: all 600ms ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow: visible;
}
@media only screen and (max-width: 575px) {
  .side-bar {
    width: 310px;
  }
}
.side-bar .inner-main-wrapper-desk .thumbnail {
  display: flex;
  justify-content: center;
}
.side-bar .inner-main-wrapper-desk .thumbnail img {
  width: 85%;
  margin: auto;
}
.side-bar .inner-main-wrapper-desk .inner-content {
  text-align: center;
  margin-top: 30px;
}
.side-bar .inner-main-wrapper-desk .inner-content p {
  max-width: 95%;
  text-align: center;
  margin: auto;
}
.side-bar .inner-main-wrapper-desk .inner-content .title {
  font-weight: 600;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer {
  padding-top: 50px;
  margin-top: 80px;
  border-top: 1px solid #c2c2c2;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer .title {
  font-weight: 500;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer a.rts-btn {
  margin: auto;
}

.side-bar.show {
  right: 0;
  overflow-y: auto;
}

.side-bar button {
  max-width: max-content;
  margin-right: auto;
  margin-left: -53px;
  margin-top: 0;
  position: absolute;
  border: none;
}
.side-bar button i {
  color: #ffffff;
  height: 50px;
  width: 50px;
  border-radius: 0;
  display: flex;
  align-items: center;
  font-weight: 300;
  justify-content: center;
  margin-left: 14px;
  margin-top: -53px;
  font-size: 27px;
  background: var(--color-primary);
}

.mobile-menu-main nav ul {
  padding: 0 20px;
  display: block;
}
.mobile-menu-main nav ul li {
  margin: 0;
  padding: 0;
}
.mobile-menu-main nav ul li a.main {
  padding: 12px 0 17px 0;
  border-bottom: 1px solid #f3f3f3;
  cursor: pointer;
  font-weight: 500;
}
.mobile-menu-main nav ul li.has-droupdown {
  position: relative;
}
.mobile-menu-main nav ul li.has-droupdown ul {
  padding: 0;
}
.mobile-menu-main nav ul li.has-droupdown ul a {
  padding: 10px 0;
  font-weight: 400;
  font-size: 16px;
}
.mobile-menu-main nav ul li.has-droupdown ul a.tag {
  font-weight: 500;
  margin-top: 15px;
  font-size: 16px;
  border-bottom: 2px solid var(--color-primary);
  padding: 10px 0;
  margin-top: 0;
}
.mobile-menu-main nav ul li.has-droupdown ul li {
  margin: 7px 0 !important;
  border-bottom: 1px solid #f3f3f3;
  margin-top: 0 !important;
}
.mobile-menu-main nav ul li.has-droupdown::after {
  position: absolute;
  content: "\f078";
  font-family: "Font Awesome 6 pro" !important;
  font-size: 16px;
  right: 0;
  font-weight: 400;
  top: 5px;
  padding: 8px 13px;
  color: #fff;
  background: var(--color-primary) !important;
  pointer-events: none;
  cursor: pointer;
}
.mobile-menu-main nav ul li.has-droupdown.mm-active::after {
  content: "\f077";
}
.mobile-menu-main nav ul li.has-droupdown.third-lvl::after {
  font-size: 10px;
  padding: 3px 10px;
}
.mobile-menu-main nav ul li.has-droupdown.third-lvl ul {
  padding: 0 20px;
}
.mobile-menu-main nav ul li.has-droupdown.third-lvl ul li {
  margin: 10px 0 !important;
  position: relative;
  z-index: 1;
  transition: all 0.3s;
  color: #1F1F25;
  font-size: 16px;
}
.mobile-menu-main nav ul li.has-droupdown.third-lvl ul li:hover {
  color: var(--color-primary);
}
.mobile-menu-main nav ul li.has-droupdown.third-lvl ul li a {
  position: absolute;
  width: 100%;
  height: 100%;
  transition: all 0.3s;
  font-size: 16px;
  color: #1F1F25;
}
.mobile-menu-main nav ul li a {
  display: block;
}
.mobile-menu-main .social-wrapper-one {
  margin-top: 50px;
}

.header-one .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown {
  position: relative;
}
.header-one .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown:hover::after {
  color: var(--color-primary);
}
.header-one .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown::after {
  position: absolute;
  content: "\f105";
  font-family: "Font Awesome 6 Pro" !important;
  font-size: 16px;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
}
.header-one .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown .third-lvl {
  margin-left: -4px;
}

.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page {
  padding: 15px 0;
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown {
  position: relative;
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown:hover::after {
  color: var(--color-primary);
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown::after {
  position: absolute;
  content: "\f105";
  font-family: "Font Awesome 6 Pro" !important;
  font-size: 16px;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page .sub-dropdown .third-lvl {
  margin-left: -4px;
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page li {
  margin: 0;
  width: 100%;
}
.header-three .main-nav-desk nav ul li.has-droupdown .submenu.inner-page li a {
  display: block;
  width: 100%;
  padding: 0 15px;
}

.sub-dropdown {
  position: relative !important;
  display: block !important;
}
.sub-dropdown .submenu.third-lvl {
  opacity: 0 !important;
  min-width: 185px !important;
  left: 100% !important;
  top: -13% !important;
  margin: 0;
  border-radius: 0 !important;
}
.sub-dropdown .submenu.third-lvl.base {
  display: block !important;
}
.sub-dropdown:hover .sub-menu-link {
  color: var(--color-primary);
}
.sub-dropdown:hover .submenu.third-lvl.base {
  opacity: 1 !important;
  min-width: 185px !important;
  top: 0 !important;
  right: 3px;
  display: block !important;
}
.sub-dropdown:hover .submenu.third-lvl.base li {
  display: block;
}
.sub-dropdown:hover .submenu.third-lvl.base li a {
  display: block !important;
}

.header-three .sub-dropdown:hover .submenu.third-lvl.base {
  margin-left: -14px !important;
}

.header-two .header-main-wrapper .sub-dropdown:hover .submenu.third-lvl.base {
  margin-left: 90px !important;
}

header.heder-two .sub-dropdown:hover .submenu.third-lvl.base {
  opacity: 1 !important;
  min-width: 185px !important;
  top: 0 !important;
  right: 3px;
  display: block;
  margin-left: 0 !important;
}
header.heder-two .sub-dropdown::after {
  position: absolute;
  content: "\f105";
  font-family: "Font Awesome 6 Pro" !important;
  font-size: 16px;
  right: 20px;
  top: 8px;
  color: #fff;
}
header.heder-two .sub-dropdown:hover a.sub-menu-link {
  color: var(--color-primary) !important;
}
header.heder-two .sub-dropdown:hover::after {
  color: var(--color-primary) !important;
}
header.heder-two .sub-dropdown .submenu.third-lvl.base {
  display: block;
}

header.header-three .sub-dropdown:hover .submenu.third-lvl.base {
  opacity: 1 !important;
  min-width: 185px !important;
  top: 0 !important;
  right: 3px;
  display: block;
  margin-left: 0 !important;
}
header.header-three .sub-dropdown::after {
  position: absolute;
  content: "\f105";
  font-family: "Font Awesome 6 Pro" !important;
  font-size: 16px;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
}
header.header-three .sub-dropdown:hover a.sub-menu-link {
  color: var(--color-primary) !important;
}
header.header-three .sub-dropdown:hover::after {
  color: var(--color-primary) !important;
}
header.header-three .sub-dropdown .submenu.third-lvl.base {
  display: block;
}

.rts-social-border-area.right-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}
.rts-social-border-area.right-sidebar ul li a {
  border: 1px solid var(--color-primary);
  background: var(--color-primary);
  transition: 0.3s;
}
.rts-social-border-area.right-sidebar ul li a i {
  color: #fff;
}
.rts-social-border-area.right-sidebar ul li a:hover {
  transform: scale(1.1);
}
.rts-social-border-area.right-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}
.rts-social-border-area.right-sidebar ul li {
  margin: 0;
  padding: 0;
}
.rts-social-border-area.right-sidebar ul li a {
  height: 39px;
  width: 39px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: 0.3s;
}
.rts-social-border-area.right-sidebar ul li a:hover {
  background: var(--color-primary);
  transform: translateY(-5px);
  border-color: var(--color-primary);
}
.rts-social-border-area.right-sidebar ul li a i {
  color: #fff;
}

.buttons-area.d-flex.g-5 {
  gap: 15px;
}

#anywhere-home {
  cursor: url(../images/banner/shape/close.png), auto;
  background: #0e1013;
  position: fixed;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease-in-out;
  pointer-events: none;
  z-index: 50;
}

#anywhere-home.bgshow {
  background: #0e1013;
  opacity: 70%;
  visibility: visible;
  pointer-events: visible;
  z-index: 999;
  top: 0;
}

.header-style-one {
  position: relative;
}

.with-megamenu .submenu {
  width: 100%;
  padding: 20px;
  border-top: 1px solid #f1f1f1 !important;
}
.with-megamenu .submenu .single-menu {
  padding: 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
  padding: 45px 0;
}
.with-megamenu .submenu .single-menu li {
  display: block;
  width: 100%;
  margin-bottom: 8px;
}
.with-megamenu .submenu .single-menu li a {
  display: block;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: var(--font-primary);
}
.with-megamenu .submenu .single-menu li a:hover {
  gap: 15px;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper {
  display: flex;
  align-items: flex-start;
  box-shadow: none;
  background: #fff;
  padding: 6px 0px !important;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper img {
  transition: 0.3s;
  width: 35px;
  height: auto;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper .info {
  flex-direction: column;
  align-items: flex-start;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper .info .title {
  font-size: 20px;
  font-family: var(--font-medium);
  transition: 0.3s;
  padding-bottom: 5px;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper .info p {
  font-weight: 400;
  font-family: var(--font-primary);
  max-width: 90%;
  color: rgba(29, 29, 29, 0.7019607843);
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper:hover {
  transform: none;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper:hover .info .title {
  color: var(--color-primary);
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper:hover img {
  filter: none;
}
.with-megamenu .submenu .single-menu .single-service-area-wrapper:hover * {
  color: var(--color-body);
}
.with-megamenu .submenu .single-menu .parent-top-industry p {
  margin-bottom: 0;
  font-size: 20px;
  font-family: var(--font-medium);
}
.with-megamenu .submenu .single-menu li .industries {
  padding: 5px 0 !important;
  display: flex;
  align-items: center;
  gap: 7px;
  color: var(--color-body);
}
.with-megamenu .submenu .single-menu li .industries svg path {
  stroke: #6D6D6D;
}
.with-megamenu .submenu .single-menu li .industries:hover {
  background: none;
  color: var(--color-primary);
}

.single-menu.industry-signle-menu li {
  margin-bottom: 1px !important;
}
.single-menu.industry-signle-menu li a {
  font-family: var(--font-primary);
  font-weight: 400 !important;
}

.single-menu.industry-signle-menu {
  padding: 45px !important;
  height: 100%;
  position: relative;
  z-index: 1;
}
.single-menu.industry-signle-menu::after {
  background: #f7f7f7;
  content: "";
  left: 0;
  height: 100%;
  width: 10000%;
  top: 0;
  bottom: 0;
  position: absolute;
  z-index: -1;
}

.has-dropdown .submenu.with-border {
  padding: 6px 0 12px 0;
}
.has-dropdown .submenu.with-border li a {
  border-bottom: 1px solid #f7f7f7;
  font-family: var(--font-primary);
}
.has-dropdown .submenu.with-border li:last-child a {
  border: none;
}

.with-megamenu.margin-single-0 .submenu .single-menu li {
  display: block;
  width: 100%;
  margin-bottom: 0;
}

.breadcrumb-wrapper-one {
  display: flex;
  align-items: center;
  gap: 10px;
}
.breadcrumb-wrapper-one .current {
  color: var(--color-primary);
}

.single-counter-up-one {
  text-align: center;
  padding: 97px 20px;
  border-right: 1px solid rgb(209, 209, 209);
  border-bottom: 1px solid rgb(209, 209, 209);
}
@media only screen and (max-width: 991px) {
  .single-counter-up-one {
    border: none !important;
    padding: 30px;
  }
}
.single-counter-up-one.border-left {
  border-left: 1px solid rgb(209, 209, 209);
}
.single-counter-up-one.border-top {
  border-top: 1px solid rgb(209, 209, 209);
  padding: 40px 20px;
}
@media only screen and (max-width: 767px) {
  .single-counter-up-one.border-top {
    border: none;
  }
}
.single-counter-up-one.border-top .icon-area {
  margin-bottom: 30px;
}
.single-counter-up-one p {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 10px;
}
.single-counter-up-one .title {
  font-size: 64px;
  font-weight: 400;
}
@media only screen and (max-width: 767px) {
  .single-counter-up-one .title {
    font-size: 40px;
  }
}
@media only screen and (max-width: 575px) {
  .single-counter-up-one .title {
    font-size: 44px;
    margin-top: 15px;
  }
}

.counter-up-wrapper-5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 80px 0;
}
@media only screen and (max-width: 767px) {
  .counter-up-wrapper-5 {
    flex-wrap: wrap;
    gap: 40px;
    justify-content: center;
  }
}
.counter-up-wrapper-5 .single-counter-area {
  padding: 0 45px;
  padding-right: 125px;
  border-right: 1px solid rgba(38, 38, 38, 0.205);
}
@media only screen and (max-width: 1199px) {
  .counter-up-wrapper-5 .single-counter-area {
    padding-right: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .counter-up-wrapper-5 .single-counter-area {
    border: none;
    text-align: center;
  }
}
.counter-up-wrapper-5 .single-counter-area:last-child {
  border-right: none;
}
.counter-up-wrapper-5 .single-counter-area .title {
  font-size: 80px;
  color: var(--color-primary);
  margin-bottom: 7px;
}
@media only screen and (max-width: 991px) {
  .counter-up-wrapper-5 .single-counter-area .title {
    font-size: 50px;
    margin-bottom: 20px;
  }
}
.counter-up-wrapper-5 .single-counter-area .title span {
  color: var(--color-primary);
}
.counter-up-wrapper-5 .single-counter-area p {
  margin: 0;
  font-size: 28px;
}
@media only screen and (max-width: 767px) {
  .counter-up-wrapper-5 .single-counter-area p {
    font-size: 18px;
  }
}

.video-counter-bg {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #F5F5FF 100%);
}

[dir=rtl] .single-menu.industry-signle-menu::after {
  right: 0;
}
[dir=rtl] .has-dropdown a i::before {
  left: -17px;
  right: auto;
}
[dir=rtl] .submenu .fa-chevron-right:before {
  content: "\f053";
}
[dir=rtl] .with-megamenu .submenu .single-menu .single-service-area-wrapper .info p {
  text-align: right;
}
[dir=rtl] .with-megamenu .submenu .single-menu li .industries svg {
  transform: rotate(180deg);
}
[dir=rtl] .with-megamenu .submenu .single-menu li {
  text-align: right;
}
[dir=rtl] .right-clippath-wrapper {
  margin-left: -330px;
  margin-right: 130px;
  gap: 12px;
}
@media only screen and (max-width: 1199px) {
  [dir=rtl] .right-clippath-wrapper {
    margin-left: -60px;
    margin-right: 0;
  }
}
[dir=rtl] .banner-wrapper-one span.pre-title::after {
  right: 0;
  left: 0;
  background: linear-gradient(271deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
}
[dir=rtl] .right-clippath-wrapper .shape-image .one {
  right: -190px;
  left: 0;
  transform: rotate(90deg);
}
[dir=rtl] .right-clippath-wrapper .shape-image .two {
  left: 0;
  right: auto;
  transform: rotate(180deg);
}
[dir=rtl] .alrge-video-area .shape-top {
  position: absolute;
  right: 0;
  top: 13%;
}
[dir=rtl] .vedio-icone .video-play-button span {
  transform: translate(-50%, -50%) rotate(60deg);
  left: -14%;
}
[dir=rtl] .large-video-bottom .rts-btn.btn-primary svg {
  transform: rotate(-90deg);
}
[dir=rtl] .solution-expertice-area .rts-btn.btn-primary svg,
[dir=rtl] .case-studies-area .rts-btn.btn-primary svg,
[dir=rtl] .cta-one-wrapper .rts-btn.btn-primary svg {
  transform: rotate(-90deg);
}
[dir=rtl] .case-studies-area .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-pricing-area .body .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-service-style-two .inner .bottom .title svg {
  transform: rotate(-90deg);
}
[dir=rtl] .rts-solution-area .rts-btn.btn-border svg {
  transform: rotate(-90deg);
}
[dir=rtl] .main-area-wrapper-cta .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-solution-style-one .btn-arrow svg {
  transform: rotate(180deg);
}
[dir=rtl] .single-pricing-area-start-2 .footer-pricing .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] header .btn-border svg {
  transform: rotate(-90deg);
}
[dir=rtl] .rts-banner-area-style-narrow .btn-border svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-solution-style-one {
  z-index: 1;
}
[dir=rtl] .single-solution-style-one .right-draw {
  left: 0;
  right: auto;
  z-index: -1;
}
[dir=rtl] .single-solution-style-one.border-left {
  border-left: none;
}
[dir=rtl] .single-solution-style-one {
  border-left: 1px solid #454545;
}
[dir=rtl] .accordion-container-one .accordion-button::after {
  transform: rotate(0);
  margin-left: 0;
  right: auto;
  left: 0;
  position: relative;
  margin-right: auto;
}
[dir=rtl] li.has-dropdown .submenu {
  right: 0;
  left: auto;
}
[dir=rtl] .single-pricing-area-start-2.active .tag {
  right: auto;
  left: 20px;
}
[dir=rtl] .single-blog-style-three .inner .title-wrapper img {
  transform: rotate(-90deg);
}
[dir=rtl] .rts-blog-area .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .more-project-btn img {
  transform: rotate(-90deg);
}
[dir=rtl] .single-service-area-4 .title-area a svg,
[dir=rtl] .single-service-area-wrapper .info svg,
[dir=rtl] .innovative-solution .rts-btn.btn-border svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-blog-list-area-right-content .title-area img,
[dir=rtl] .banner-four-wrapper .right-inner-button a img {
  transform: rotate(-90deg);
}
[dir=rtl] .cta-two-wrapper .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-blog-area-five .inner-content .title-area img {
  transform: rotate(-90deg);
}
[dir=rtl] .rts-blog-area-5 .rts-btn.btn-border img {
  transform: rotate(-90deg);
}
[dir=rtl] .what-we-do-main-wrapper .rts-btn img {
  transform: rotate(-90deg);
}
[dir=rtl] .gap-service-area .rts-btn.btn-border img,
[dir=rtl] .single-pricing-area-start-2 .footer-pricing .rts-btn img,
[dir=rtl] .rts-call-to-action-area-about .rts-btn svg,
[dir=rtl] .single-service-list .arrow-btn img {
  transform: rotate(-90deg);
}
[dir=rtl] .progress-wrap {
  right: auto;
  left: 30px;
}
[dir=rtl] .title-area-center-inner-with-sub span::after {
  right: auto;
  left: -4px;
  background: linear-gradient(274deg, rgba(255, 255, 255, 0) 0%, #f8f9fa 100%);
}
[dir=rtl] .single-testimonials-4 {
  text-align: right;
}
[dir=rtl] .single-testimonials-4 .user-area {
  flex-direction: row-reverse;
}
[dir=rtl] .service-list-between .right-area-thumbnail {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .project-style-5-title-between p.disc {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .active-pricing-5 .tag {
  left: 16px;
  top: 16px;
  right: auto;
}
[dir=rtl] .map-location-area .location-single::after {
  left: -80px;
  right: auto;
}
[dir=rtl] .single-testimonials-about {
  text-align: right;
}
[dir=rtl] .single-testimonials-about .author-area {
  flex-direction: row-reverse;
}
[dir=rtl] .rts-service-banner-area .shape-area-start img.one {
  right: 30%;
  top: 0;
  left: auto;
}
[dir=rtl] .rts-service-banner-area .shape-area-start img.two {
  left: 44%;
  top: 69%;
  right: auto;
  transform: scale(-1);
}
[dir=rtl] .testimonials-border .single-testimonials-area-one {
  text-align: right;
}
[dir=rtl] .single-testimonials-area-one .author-wrapper {
  flex-direction: row-reverse;
}
[dir=rtl] .get-quote-area-service-wrapper .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-large-case-studies-area .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-blog-area-start {
  border: 1px solid #D1D1D1;
}
[dir=rtl] .single-case-studies .inner-content {
  text-align: right;
}
[dir=rtl] .single-case-studies .inner-content .rts-btn {
  margin-left: auto;
  margin-right: 0;
}
[dir=rtl] .rts-banner-three-area .inner-content-wrapper-three .rts-btn svg {
  transform: rotate(-90deg);
}
[dir=rtl] .single-service-style-two .inner .bottom .title img {
  transform: rotate(-90deg);
}
[dir=rtl] .large-video-area .vedio-icone .video-play-button span {
  transform: translate(-50%, -50%) rotate(180deg);
}
[dir=rtl] .single-service-style-two {
  text-align: right;
}
[dir=rtl] .single-service-style-two .inner .bottom .title {
  flex-direction: row-reverse;
}
[dir=rtl] .single-large-case-studies-area-details .single-case-studies .inner-content .right-area {
  margin-right: auto;
  margin-left: auto;
}
[dir=rtl] .col-lg-6.pl--100.pl_md--15.pl_sm--10.pt_md--30.pt_sm--30 {
  padding-right: 100px;
  padding-left: 0 !important;
}
[dir=rtl] .why-choose-pricing-area .accordion button::after,
[dir=rtl] .why-choose-us-faq-area button::after,
[dir=rtl] .faq-why-choose-left-accordion button::after {
  margin-left: 0;
  margin-right: auto;
}
[dir=rtl] .why-choose-pricing-area .rts-btn img {
  transform: rotate(-90deg);
}
[dir=rtl] .rts-single-wized .single-categories li a i {
  transform: rotate(180deg);
}
[dir=rtl] .col-lg-6.pl--50.pl_md--15.pl_sm--10.mt_md--30.pt_sm--30 {
  padding-right: 50px;
  padding-left: 0 !important;
}
[dir=rtl] .title-between-area-wrapper-main p.disc {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .col-lg-6.pl--100.pl_md--15.pl_sm--10 {
  padding-right: 100px;
  padding-left: 0 !important;
}
[dir=rtl] .col-lg-6.pr--40.pr_md--10.pr_sm--10.mb_md--30.mb_sm--25 {
  padding-left: 40px;
  padding-right: 0 !important;
}
[dir=rtl] .col-lg-6.pl--40.pl_md--15.pl_sm--10.mt_md--30.mt_sm--30 {
  padding-left: 10px !important;
  padding-right: 30px !important;
}
[dir=rtl] .single-testimonials-area-one {
  text-align: right;
}
[dir=rtl] .col-lg-7.pl--50.pl_md--15.pl_sm--15.mt_md--30.mt_sm--30 {
  padding-right: 50px;
  padding-left: 0 !important;
}
[dir=rtl] .col-lg-6.pl--30.pl_md--15.pl_sm--10.mt_md--30.mt_sm--30 {
  padding-right: 50px;
  padding-left: 10px !important;
}
[dir=rtl] .single-consulting-one .timeline-dot {
  right: 60.4%;
}
[dir=rtl] .consulting-step .timeline-line {
  right: 60.2%;
  left: auto;
}
[dir=rtl] .col-lg-6.pr--40.pr_md--15.pr_sm--10 {
  padding-left: 40px;
  padding-right: 0 !important;
}
[dir=rtl] .col-lg-6.pr--40.pr_md--0.pr_sm--10.pb_md--30.pb_sm--30 {
  padding-left: 40px;
  padding-right: 0 !important;
}
[dir=rtl] .col-lg-6.pl--40.pl_md--15.pl_sm--10 {
  padding-left: 0 !important;
  padding-right: 40px;
}
[dir=rtl] .col-lg-6.pl--30.pl_md--15.pl_sm--10.pt_md--30.pt_sm--30 {
  padding-left: 10px !important;
  padding-right: 40px;
}
[dir=rtl] .col-lg-6.pr--40.pr_md--0.pr_sm--10.mb_md--30.mb_sm--30 {
  padding-right: 0 !important;
  padding-left: 40px;
}
[dir=rtl] .rts-single-wized .recent-post-single .thumbnail {
  margin-left: 20px;
  margin-right: auto;
}
[dir=rtl] .rts-single-wized .recent-post-single .user span {
  margin-right: 9px;
  margin-left: auto;
}
[dir=rtl] .rts-single-wized .tags-wrapper a {
  margin-left: 10px;
  margin-right: 0;
  margin-bottom: 10px;
}
[dir=rtl] .blog-single-post-listing.details .details-tag h6 {
  margin-left: 15px;
  margin-right: auto;
}
[dir=rtl] .blog-single-post-listing.details .details-share h6 {
  margin-left: 15px;
  margin-right: 0;
}
[dir=rtl] .blog-single-post-listing.details .author-area .thumbnail {
  margin-left: 30px;
  margin-right: 0;
}
[dir=rtl] .blog-single-post-listing.details .check-area-details .single-check i {
  margin-left: 15px;
  margin-right: 0;
}
[dir=rtl] .blog-single-post-listing .blog-listing-content .user-info .single {
  margin-left: 30px;
  margin-right: 0;
}
[dir=rtl] .blog-single-post-listing .blog-listing-content .user-info .single i {
  margin-left: 10px;
  margin-right: 0;
}
[dir=rtl] .contact-form .rts-btn.btn-primary svg {
  transform: rotate(-90deg);
}
[dir=rtl] .contact-form .form-check label {
  padding-right: 10px;
}
[dir=rtl] .pagination-one ul li button.next-page {
  transform: rotate(180deg);
}
[dir=rtl] .accordion-container-one .accordion-button {
  text-align: right;
}
@media only screen and (max-width: 767px) {
  [dir=rtl] .rts-blog-area .shape-bottom {
    display: none;
  }
}
/* Sticky Header Styles */
.header--sticky {
  transition: all 0.3s ease;
}

.header--sticky.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.header--sticky.sticky .header-wrapper-1 {
  padding: 10px 0;
}

.header--sticky.sticky .logo-area img {
  max-height: 50px;
  transition: all 0.3s ease;
}

/*# sourceMappingURL=../maps/style.css.map */
