<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Program extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'image',
        'price',
        'duration',
        'max_participants',
        'schedule',
        'level',
        'requirements',
        'objectives',
        'instructor',
        'location',
        'is_online',
        'is_active',
        'status',
        'start_date',
        'end_date',
        'meta_title',
        'meta_description',
        'sort_order',
        'event_id'
    ];

    protected $casts = [
        'schedule' => 'array',
        'is_online' => 'boolean',
        'is_active' => 'boolean',
        'price' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($program) {
            if (empty($program->slug)) {
                $program->slug = Str::slug($program->title);
            }
        });
        
        static::updating(function ($program) {
            if ($program->isDirty('title') && empty($program->slug)) {
                $program->slug = Str::slug($program->title);
            }
        });
    }

    /**
     * Get the event that this program is linked to.
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scope a query to only include active programs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include upcoming programs.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('status', 'upcoming')
                    ->where('start_date', '>', now());
    }

    /**
     * Scope a query to only include ongoing programs.
     */
    public function scopeOngoing($query)
    {
        return $query->where('status', 'ongoing')
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope a query to filter by level.
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope a query to filter by online/offline.
     */
    public function scopeOnline($query, $isOnline = true)
    {
        return $query->where('is_online', $isOnline);
    }

    /**
     * Get the formatted price attribute.
     */
    public function getFormattedPriceAttribute()
    {
        return $this->price ? '$' . number_format($this->price, 2) : 'Free';
    }

    /**
     * Get the formatted start date attribute.
     */
    public function getFormattedStartDateAttribute()
    {
        return $this->start_date ? $this->start_date->format('M d, Y') : null;
    }

    /**
     * Get the formatted end date attribute.
     */
    public function getFormattedEndDateAttribute()
    {
        return $this->end_date ? $this->end_date->format('M d, Y') : null;
    }

    /**
     * Get the status badge class for UI.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'upcoming' => 'badge-primary',
            'ongoing' => 'badge-success',
            'completed' => 'badge-secondary',
            'cancelled' => 'badge-danger',
            default => 'badge-light'
        };
    }

    /**
     * Check if the program is linked to an event.
     */
    public function hasEvent()
    {
        return !is_null($this->event_id);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}