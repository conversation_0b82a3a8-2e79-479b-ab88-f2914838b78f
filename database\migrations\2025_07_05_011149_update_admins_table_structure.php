<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            if (!Schema::hasColumn('admins', 'name')) {
                $table->string('name')->after('id');
            }
            if (!Schema::hasColumn('admins', 'email')) {
                $table->string('email')->unique()->after('name');
            }
            if (!Schema::hasColumn('admins', 'email_verified_at')) {
                $table->timestamp('email_verified_at')->nullable()->after('email');
            }
            if (!Schema::hasColumn('admins', 'password')) {
                $table->string('password')->after('email_verified_at');
            }
            if (!Schema::hasColumn('admins', 'role')) {
                $table->enum('role', ['admin', 'super_admin'])->default('admin')->after('password');
            }
            if (!Schema::hasColumn('admins', 'status')) {
                $table->enum('status', ['active', 'inactive'])->default('active')->after('role');
            }
            if (!Schema::hasColumn('admins', 'avatar')) {
                $table->string('avatar')->nullable()->after('status');
            }
            if (!Schema::hasColumn('admins', 'remember_token')) {
                $table->rememberToken()->after('avatar');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            $table->dropColumn([
                'name', 'email', 'email_verified_at', 'password', 
                'role', 'status', 'avatar', 'remember_token'
            ]);
            $table->dropTimestamps();
        });
    }
};
