<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Dashboard</h1>
    <div class="text-muted">
        <i class="fas fa-calendar me-1"></i>
        <?php echo e(now()->format('l, F j, Y')); ?>

    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Total Admins</div>
                    <div class="h4 mb-0"><?php echo e($totalAdmins); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Active Admins</div>
                    <div class="h4 mb-0"><?php echo e($activeAdmins); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Super Admins</div>
                    <div class="h4 mb-0"><?php echo e($superAdmins); ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="ms-3">
                    <div class="text-muted small">Settings</div>
                    <div class="h4 mb-0"><?php echo e($totalSettings); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Admins -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Recent Admin Users
                </h5>
            </div>
            <div class="card-body">
                <?php if($recentAdmins->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Avatar</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentAdmins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $admin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($admin->avatar): ?>
                                                <img src="<?php echo e(asset('storage/' . $admin->avatar)); ?>" alt="Avatar" class="rounded-circle" width="40" height="40">
                                            <?php else: ?>
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo e($admin->name); ?></strong>
                                        </td>
                                        <td><?php echo e($admin->email); ?></td>
                                        <td>
                                            <?php if($admin->role === 'super_admin'): ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-crown me-1"></i>Super Admin
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-user-cog me-1"></i>Admin
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($admin->status === 'active'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Active
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-times me-1"></i>Inactive
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo e($admin->created_at->diffForHumans()); ?>

                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="<?php echo e(route('admin.admins.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>View All Admins
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No admin users found</h5>
                        <p class="text-muted">Create your first admin user to get started.</p>
                        <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create Admin
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        Add New Admin
                    </a>
                    <a href="<?php echo e(route('admin.settings.general')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-2"></i>
                        General Settings
                    </a>
                    <a href="<?php echo e(route('admin.settings.seo')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        SEO Settings
                    </a>
                    <a href="<?php echo e(route('admin.settings.contact')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-address-book me-2"></i>
                        Contact Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <div class="text-muted small">Laravel</div>
                            <div class="fw-bold"><?php echo e(app()->version()); ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <div class="text-muted small">PHP</div>
                            <div class="fw-bold"><?php echo e(PHP_VERSION); ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <div class="text-muted small">Environment</div>
                            <div class="fw-bold"><?php echo e(app()->environment()); ?></div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 bg-light rounded">
                            <div class="text-muted small">Debug</div>
                            <div class="fw-bold"><?php echo e(config('app.debug') ? 'On' : 'Off'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Message for First Time Users -->
<?php if($totalAdmins <= 1): ?>
<div class="row">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    Welcome to Admin Panel!
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">Welcome to your new admin panel! Here are some quick steps to get you started:</p>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-user-plus text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Add Admin Users</h6>
                                <small class="text-muted">Create additional admin accounts</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-cog text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Configure Settings</h6>
                                <small class="text-muted">Set up your site preferences</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Security Setup</h6>
                                <small class="text-muted">Configure security settings</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>