<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Site Settings
            [
                'key' => 'site_name',
                'value' => 'PESCOT',
                'type' => 'text',
                'group' => 'site',
                'description' => 'Website name',
                'is_public' => true
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Center for Kingdom and Leadership Studies',
                'type' => 'text',
                'group' => 'site',
                'description' => 'Website tagline',
                'is_public' => true
            ],
            [
                'key' => 'site_description',
                'value' => 'PESCOT - Center for Kingdom and Leadership Studies - Raising Values, Building Leaders, Strengthening Nations',
                'type' => 'textarea',
                'group' => 'site',
                'description' => 'Website description',
                'is_public' => true
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'file',
                'group' => 'site',
                'description' => 'Website logo',
                'is_public' => true
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'site',
                'description' => 'Website favicon',
                'is_public' => true
            ],
            [
                'key' => 'site_timezone',
                'value' => 'UTC',
                'type' => 'text',
                'group' => 'site',
                'description' => 'Website timezone',
                'is_public' => false
            ],
            [
                'key' => 'site_maintenance_mode',
                'value' => false,
                'type' => 'boolean',
                'group' => 'site',
                'description' => 'Enable maintenance mode',
                'is_public' => true
            ],

            // SEO Settings
            [
                'key' => 'seo_keywords',
                'value' => 'PESCOT, leadership, kingdom studies, education',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'SEO keywords',
                'is_public' => true
            ],
            [
                'key' => 'seo_google_analytics',
                'value' => null,
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Google Analytics tracking ID',
                'is_public' => true
            ],
            [
                'key' => 'seo_google_tag_manager',
                'value' => null,
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Google Tag Manager ID',
                'is_public' => true
            ],
            [
                'key' => 'seo_facebook_pixel',
                'value' => null,
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Facebook Pixel ID',
                'is_public' => true
            ],

            // Social Media Settings
            [
                'key' => 'social_facebook',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'description' => 'Facebook page URL',
                'is_public' => true
            ],
            [
                'key' => 'social_twitter',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'description' => 'Twitter profile URL',
                'is_public' => true
            ],
            [
                'key' => 'social_instagram',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'description' => 'Instagram profile URL',
                'is_public' => true
            ],
            [
                'key' => 'social_linkedin',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'description' => 'LinkedIn profile URL',
                'is_public' => true
            ],
            [
                'key' => 'social_youtube',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'description' => 'YouTube channel URL',
                'is_public' => true
            ],

            // Email Settings
            [
                'key' => 'email_from_name',
                'value' => 'PESCOT',
                'type' => 'text',
                'group' => 'email',
                'description' => 'Email from name',
                'is_public' => false
            ],
            [
                'key' => 'email_from_address',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'email',
                'description' => 'Email from address',
                'is_public' => false
            ],
            [
                'key' => 'email_smtp_host',
                'value' => null,
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP host',
                'is_public' => false
            ],
            [
                'key' => 'email_smtp_port',
                'value' => 587,
                'type' => 'integer',
                'group' => 'email',
                'description' => 'SMTP port',
                'is_public' => false
            ],
            [
                'key' => 'email_smtp_username',
                'value' => null,
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP username',
                'is_public' => false
            ],
            [
                'key' => 'email_smtp_password',
                'value' => null,
                'type' => 'password',
                'group' => 'email',
                'description' => 'SMTP password',
                'is_public' => false
            ],
            [
                'key' => 'email_smtp_encryption',
                'value' => 'tls',
                'type' => 'text',
                'group' => 'email',
                'description' => 'SMTP encryption (tls/ssl)',
                'is_public' => false
            ],

            // Theme Settings
            [
                'key' => 'theme_primary_color',
                'value' => '#2678a1db',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Primary theme color',
                'is_public' => true
            ],
            [
                'key' => 'theme_secondary_color',
                'value' => '#6c757d',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Secondary theme color',
                'is_public' => true
            ],
            [
                'key' => 'theme_accent_color',
                'value' => '#28a745',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Accent theme color',
                'is_public' => true
            ],
            [
                'key' => 'theme_text_color',
                'value' => '#333333',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Text color',
                'is_public' => true
            ],
            [
                'key' => 'theme_background_color',
                'value' => '#ffffff',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Background color',
                'is_public' => true
            ],
            [
                'key' => 'theme_font_family',
                'value' => 'Arial, sans-serif',
                'type' => 'text',
                'group' => 'theme',
                'description' => 'Font family',
                'is_public' => true
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
