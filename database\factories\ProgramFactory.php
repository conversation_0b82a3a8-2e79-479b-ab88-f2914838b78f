<?php

namespace Database\Factories;

use App\Models\Program;
use App\Models\Event;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Program>
 */
class ProgramFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        $startDate = $this->faker->dateTimeBetween('now', '+6 months');
        $endDate = $this->faker->dateTimeBetween($startDate, '+1 year');
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(15),
            'image' => null,
            'price' => $this->faker->randomElement([null, $this->faker->randomFloat(2, 50, 500)]),
            'duration' => $this->faker->randomElement(['4 weeks', '6 weeks', '8 weeks', '3 months', '6 months']),
            'max_participants' => $this->faker->numberBetween(10, 100),
            'schedule' => [
                'days' => $this->faker->randomElements(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'], 2),
                'time' => $this->faker->time('H:i'),
                'timezone' => 'UTC'
            ],
            'level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'requirements' => $this->faker->paragraph(),
            'objectives' => $this->faker->paragraphs(2, true),
            'instructor' => $this->faker->name(),
            'location' => $this->faker->randomElement([null, $this->faker->city()]),
            'is_online' => $this->faker->boolean(60), // 60% chance of being online
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'status' => $this->faker->randomElement(['upcoming', 'ongoing', 'completed']),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'meta_title' => $title,
            'meta_description' => $this->faker->sentence(20),
            'sort_order' => $this->faker->numberBetween(0, 100),
            'event_id' => $this->faker->boolean(30) ? Event::factory() : null, // 30% chance of being linked to an event
        ];
    }

    /**
     * Indicate that the program is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the program is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the program is upcoming.
     */
    public function upcoming(): static
    {
        $startDate = $this->faker->dateTimeBetween('+1 week', '+6 months');
        $endDate = $this->faker->dateTimeBetween($startDate, '+1 year');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'upcoming',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Indicate that the program is ongoing.
     */
    public function ongoing(): static
    {
        $startDate = $this->faker->dateTimeBetween('-2 months', 'now');
        $endDate = $this->faker->dateTimeBetween('now', '+6 months');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'ongoing',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Indicate that the program is completed.
     */
    public function completed(): static
    {
        $startDate = $this->faker->dateTimeBetween('-1 year', '-2 months');
        $endDate = $this->faker->dateTimeBetween($startDate, '-1 week');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Indicate that the program is online.
     */
    public function online(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_online' => true,
            'location' => null,
        ]);
    }

    /**
     * Indicate that the program is offline.
     */
    public function offline(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_online' => false,
            'location' => $this->faker->city(),
        ]);
    }

    /**
     * Indicate that the program is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => null,
        ]);
    }

    /**
     * Indicate that the program is linked to an event.
     */
    public function withEvent(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_id' => Event::factory(),
        ]);
    }

    /**
     * Indicate that the program is not linked to an event.
     */
    public function withoutEvent(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_id' => null,
        ]);
    }
}