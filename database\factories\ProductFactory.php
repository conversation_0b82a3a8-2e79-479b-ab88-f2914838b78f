<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $products = [
            [
                'name' => 'Pescot NPK 20:10:10',
                'description' => 'High nitrogen fertilizer perfect for crops requiring vigorous vegetative growth. Ideal for leafy vegetables, maize, and other nitrogen-demanding crops during their growth phase.',
                'short_description' => 'High nitrogen formula for vigorous vegetative growth',
                'sku' => 'PSC-NPK-201010',
                'price' => 15000.00,
                'specifications' => [
                    'Nitrogen (N)' => '20%',
                    'Phosphorus (P₂O₅)' => '10%',
                    'Potassium (K₂O)' => '10%',
                    'Package Size' => '50kg bag',
                    'Application Rate' => '200-300 kg/ha'
                ],
                'features' => [
                    'High nitrogen content for rapid growth',
                    'Balanced phosphorus and potassium',
                    'Suitable for all soil types',
                    'Quick-release formula',
                    'Improves leaf development'
                ]
            ],
            [
                'name' => 'Pescot NPK 15:15:15',
                'description' => 'Balanced fertilizer suitable for all crops and growth stages. This versatile fertilizer ensures complete plant nutrition from planting to harvest.',
                'short_description' => 'Balanced formula for all crops and growth stages',
                'sku' => 'PSC-NPK-151515',
                'price' => 14000.00,
                'specifications' => [
                    'Nitrogen (N)' => '15%',
                    'Phosphorus (P₂O₅)' => '15%',
                    'Potassium (K₂O)' => '15%',
                    'Package Size' => '50kg bag',
                    'Application Rate' => '150-250 kg/ha'
                ],
                'features' => [
                    'Balanced nutrition for all crops',
                    'Suitable for base application',
                    'Promotes overall plant health',
                    'Cost-effective solution',
                    'Versatile application'
                ]
            ],
            [
                'name' => 'Bio Neem Organic Fertilizer',
                'description' => 'Natural neem-based fertilizer that provides nutrients while protecting crops from pests and diseases. 100% organic and environmentally friendly.',
                'short_description' => 'Organic fertilizer with natural pest control properties',
                'sku' => 'PSC-BIO-NEEM',
                'price' => 18000.00,
                'specifications' => [
                    'Type' => 'Organic',
                    'Neem Content' => '100% Natural',
                    'Package Size' => '25kg bag',
                    'Application Rate' => '100-150 kg/ha'
                ],
                'features' => [
                    '100% organic and natural',
                    'Built-in pest control',
                    'Improves soil health',
                    'Environmentally safe',
                    'Slow-release nutrition'
                ]
            ]
        ];

        $product = $this->faker->randomElement($products);

        return [
            'name' => $product['name'],
            'slug' => Str::slug($product['name']),
            'description' => $product['description'],
            'short_description' => $product['short_description'],
            'sku' => $product['sku'],
            'category_id' => 1, // Will be set by seeder
            'price' => $product['price'],
            'compare_price' => $product['price'] * 1.2,
            'cost_price' => $product['price'] * 0.7,
            'stock_quantity' => $this->faker->numberBetween(50, 500),
            'min_stock_level' => 20,
            'weight' => 50.00,
            'dimensions' => '60cm x 40cm x 15cm',
            'status' => 'active',
            'is_featured' => $this->faker->boolean(30),
            'meta_title' => $product['name'] . ' - Pescot Agro Industry',
            'meta_description' => $product['short_description'] . ' - Quality fertilizer from Pescot Agro Industry Limited.',
            'specifications' => $product['specifications'],
            'features' => $product['features'],
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }
}
