@extends('layouts.admin')

@section('title', 'Post Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Post: {{ $post->title }}</h3>
                    <div>
                        <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.posts.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Posts
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Featured Image -->
                            @if($post->featured_image)
                            <div class="mb-4">
                                <img src="{{ $post->featuredImageUrl }}" class="img-fluid rounded" style="max-height: 400px; width: 100%; object-fit: cover;">
                            </div>
                            @endif

                            <!-- Post Content -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Content</h5>
                                </div>
                                <div class="card-body">
                                    @if($post->excerpt)
                                    <div class="alert alert-info">
                                        <strong>Excerpt:</strong> {{ $post->excerpt }}
                                    </div>
                                    @endif

                                    <div class="post-content">
                                        {!! $post->content !!}
                                    </div>
                                </div>
                            </div>

                            <!-- Gallery Images -->
                            @if($post->gallery_images && count($post->gallery_images) > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Gallery Images ({{ count($post->gallery_images) }})</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach($post->gallery_images as $image)
                                        <div class="col-md-3 col-sm-4 col-6 mb-3">
                                            <a href="{{ $post->getGalleryImageUrl($image) }}" data-lightbox="gallery" data-title="Gallery Image">
                                                <img src="{{ $post->getGalleryImageUrl($image) }}" class="img-thumbnail" style="width: 100%; height: 150px; object-fit: cover;">
                                            </a>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- SEO Information -->
                            @if($post->meta_title || $post->meta_description)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Information</h5>
                                </div>
                                <div class="card-body">
                                    @if($post->meta_title)
                                    <div class="mb-3">
                                        <strong>Meta Title:</strong><br>
                                        <span class="text-muted">{{ $post->meta_title }}</span>
                                    </div>
                                    @endif
                                    @if($post->meta_description)
                                    <div>
                                        <strong>Meta Description:</strong><br>
                                        <span class="text-muted">{{ $post->meta_description }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endif

                            <!-- Comments Section -->
                            @if($post->comments_enabled)
                            <div class="card mt-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">Comments ({{ $post->comments()->count() }})</h5>
                                    <a href="{{ route('admin.comments.index', ['post' => $post->id]) }}" class="btn btn-sm btn-primary">
                                        Manage Comments
                                    </a>
                                </div>
                                <div class="card-body">
                                    @if($post->comments()->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Author</th>
                                                        <th>Comment</th>
                                                        <th>Status</th>
                                                        <th>Date</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($post->comments()->latest()->take(5)->get() as $comment)
                                                    <tr>
                                                        <td>{{ $comment->name }}</td>
                                                        <td>{{ Str::limit($comment->comment, 50) }}</td>
                                                        <td>
                                                            <span class="badge badge-{{ $comment->status === 'approved' ? 'success' : ($comment->status === 'pending' ? 'warning' : 'danger') }}">
                                                                {{ ucfirst($comment->status) }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $comment->created_at->format('M d, Y') }}</td>
                                                        <td>
                                                            <a href="{{ route('admin.comments.show', $comment) }}" class="btn btn-xs btn-info">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        @if($post->comments()->count() > 5)
                                        <div class="text-center mt-2">
                                            <a href="{{ route('admin.comments.index', ['post' => $post->id]) }}" class="btn btn-sm btn-outline-primary">
                                                View All Comments
                                            </a>
                                        </div>
                                        @endif
                                    @else
                                        <p class="text-muted mb-0">No comments yet.</p>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="col-md-4">
                            <!-- Post Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Post Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <span class="badge badge-{{ $post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning') }}">
                                                    {{ ucfirst($post->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Author:</th>
                                            <td>{{ $post->author->name ?? 'Unknown' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Category:</th>
                                            <td>
                                                @if($post->category)
                                                    <a href="{{ route('admin.categories.show', $post->category) }}" class="text-decoration-none">
                                                        {{ $post->category->name }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">No Category</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Featured:</th>
                                            <td>
                                                @if($post->featured)
                                                    <span class="badge badge-warning">Yes</span>
                                                @else
                                                    <span class="text-muted">No</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Comments:</th>
                                            <td>
                                                @if($post->comments_enabled)
                                                    <span class="badge badge-success">Enabled</span>
                                                @else
                                                    <span class="badge badge-secondary">Disabled</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Views:</th>
                                            <td><span class="badge badge-info">{{ $post->views ?? 0 }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Slug:</th>
                                            <td><code>{{ $post->slug }}</code></td>
                                        </tr>
                                        <tr>
                                            <th>Published:</th>
                                            <td>
                                                @if($post->published_at)
                                                    {{ $post->published_at->format('M d, Y H:i') }}
                                                @else
                                                    <span class="text-muted">Not Published</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Created:</th>
                                            <td>{{ $post->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>Updated:</th>
                                            <td>{{ $post->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Tags -->
                            @if($post->tags->count() > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Tags ({{ $post->tags->count() }})</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($post->tags as $tag)
                                        <a href="{{ route('admin.tags.show', $tag) }}" class="badge badge-secondary mr-1 mb-1 text-decoration-none">
                                            {{ $tag->name }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <!-- Quick Actions -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> Edit Post
                                        </a>

                                        <button type="button" class="btn btn-{{ $post->status === 'published' ? 'secondary' : 'success' }} btn-sm"
                                                onclick="toggleStatus({{ $post->id }})">
                                            <i class="fas fa-{{ $post->status === 'published' ? 'eye-slash' : 'eye' }}"></i>
                                            {{ $post->status === 'published' ? 'Unpublish' : 'Publish' }}
                                        </button>

                                        <button type="button" class="btn btn-{{ $post->featured ? 'outline-warning' : 'warning' }} btn-sm"
                                                onclick="toggleFeatured({{ $post->id }})">
                                            <i class="fas fa-star"></i>
                                            {{ $post->featured ? 'Remove Featured' : 'Mark Featured' }}
                                        </button>

                                        @if($post->status === 'published')
                                        <a href="#" class="btn btn-info btn-sm" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> View on Site
                                        </a>
                                        @endif

                                        <button type="button" class="btn btn-danger btn-sm" onclick="deletePost({{ $post->id }})">
                                            <i class="fas fa-trash"></i> Delete Post
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-right">
                                                <h4 class="text-primary">{{ $post->views ?? 0 }}</h4>
                                                <small class="text-muted">Views</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-success">{{ $post->comments()->count() }}</h4>
                                            <small class="text-muted">Comments</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-right">
                                                <h5 class="text-info">{{ $post->comments()->where('status', 'approved')->count() }}</h5>
                                                <small class="text-muted">Approved</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-warning">{{ $post->comments()->where('status', 'pending')->count() }}</h5>
                                            <small class="text-muted">Pending</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this post? This action cannot be undone and will also delete all associated comments.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
<style>
.post-content {
    line-height: 1.6;
}
.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
}
.post-content blockquote {
    border-left: 4px solid #2678a1db;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
<script>
function toggleStatus(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

function toggleFeatured(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-featured`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating featured status');
            }
        },
        error: function() {
            alert('Error updating featured status');
        }
    });
}

function deletePost(postId) {
    $('#deleteForm').attr('action', `/admin/posts/${postId}`);
    $('#deleteModal').modal('show');
}

// Initialize lightbox
lightbox.option({
    'resizeDuration': 200,
    'wrapAround': true
});
</script>
@endpush
