<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GalleryController extends Controller
{
    /**
     * Display a listing of the gallery items.
     */
    public function index(Request $request)
    {
        $query = Gallery::query();

        // Filter by type
        if ($request->has('type') && $request->type !== '') {
            $query->where('type', $request->type);
        }

        // Filter by category
        if ($request->has('category') && $request->category !== '') {
            $query->where('category', $request->category);
        }

        // Search
        if ($request->has('search') && $request->search !== '') {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('alt_text', 'like', '%' . $request->search . '%');
            });
        }

        $gallery = $query->latest()->paginate(20);
        $categories = Gallery::getCategories();

        return view('admin.gallery.index', compact('gallery', 'categories'));
    }

    /**
     * Show the form for creating a new gallery item.
     */
    public function create()
    {
        return view('admin.gallery.create');
    }

    /**
     * Store a newly created gallery item in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'alt_text' => 'nullable|string|max:255',
            'type' => 'required|in:image,video',
            'category' => 'required|string|max:100',
            'file' => 'required_if:type,image|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'video_url' => 'required_if:type,video|url',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle file upload for images
        if ($request->type === 'image' && $request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . Str::slug($request->title) . '.' . $file->getClientOriginalExtension();
            $data['file_path'] = $file->storeAs('gallery', $filename, 'public');
            $data['file_size'] = $file->getSize();
            $data['mime_type'] = $file->getMimeType();
        }

        // Handle video URL
        if ($request->type === 'video') {
            $data['file_path'] = $request->video_url;
        }

        Gallery::create($data);

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Gallery item created successfully.');
    }

    /**
     * Display the specified gallery item.
     */
    public function show(Gallery $gallery)
    {
        return view('admin.gallery.show', compact('gallery'));
    }

    /**
     * Show the form for editing the specified gallery item.
     */
    public function edit(Gallery $gallery)
    {
        return view('admin.gallery.edit', compact('gallery'));
    }

    /**
     * Update the specified gallery item in storage.
     */
    public function update(Request $request, Gallery $gallery)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'alt_text' => 'nullable|string|max:255',
            'type' => 'required|in:image,video',
            'category' => 'required|string|max:100',
            'file' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'video_url' => 'required_if:type,video|url',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle file upload for images
        if ($request->type === 'image' && $request->hasFile('file')) {
            // Delete old file
            if ($gallery->file_path && $gallery->type === 'image') {
                Storage::disk('public')->delete($gallery->file_path);
            }

            $file = $request->file('file');
            $filename = time() . '_' . Str::slug($request->title) . '.' . $file->getClientOriginalExtension();
            $data['file_path'] = $file->storeAs('gallery', $filename, 'public');
            $data['file_size'] = $file->getSize();
            $data['mime_type'] = $file->getMimeType();
        }

        // Handle video URL
        if ($request->type === 'video') {
            // Delete old file if switching from image to video
            if ($gallery->type === 'image' && $gallery->file_path) {
                Storage::disk('public')->delete($gallery->file_path);
            }
            $data['file_path'] = $request->video_url;
            $data['file_size'] = null;
            $data['mime_type'] = 'video';
        }

        $gallery->update($data);

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Gallery item updated successfully.');
    }

    /**
     * Remove the specified gallery item from storage.
     */
    public function destroy(Gallery $gallery)
    {
        // Delete file if it's an image
        if ($gallery->type === 'image' && $gallery->file_path) {
            Storage::disk('public')->delete($gallery->file_path);
        }

        $gallery->delete();

        return redirect()->route('admin.gallery.index')
            ->with('success', 'Gallery item deleted successfully.');
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Gallery $gallery)
    {
        $gallery->update([
            'is_featured' => !$gallery->is_featured
        ]);

        return response()->json([
            'success' => true,
            'is_featured' => $gallery->is_featured,
            'message' => 'Gallery item featured status updated successfully.'
        ]);
    }

    /**
     * Bulk upload images
     */
    public function bulkUpload(Request $request)
    {
        $request->validate([
            'files.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'category' => 'required|string|max:100',
        ]);

        $uploadedCount = 0;

        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('gallery', $filename, 'public');

                Gallery::create([
                    'title' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                    'type' => 'image',
                    'category' => $request->category,
                    'file_path' => $filePath,
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'alt_text' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
                ]);

                $uploadedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$uploadedCount} images uploaded successfully."
        ]);
    }

    /**
     * Bulk actions for gallery items
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,feature,unfeature,category',
            'items' => 'required|array',
            'items.*' => 'exists:galleries,id',
            'new_category' => 'required_if:action,category|string|max:100',
        ]);

        $galleryItems = Gallery::whereIn('id', $request->items);

        switch ($request->action) {
            case 'delete':
                foreach ($galleryItems->get() as $item) {
                    if ($item->type === 'image' && $item->file_path) {
                        Storage::disk('public')->delete($item->file_path);
                    }
                }
                $galleryItems->delete();
                $message = 'Selected items deleted successfully.';
                break;
            case 'feature':
                $galleryItems->update(['is_featured' => true]);
                $message = 'Selected items marked as featured successfully.';
                break;
            case 'unfeature':
                $galleryItems->update(['is_featured' => false]);
                $message = 'Selected items unmarked as featured successfully.';
                break;
            case 'category':
                $galleryItems->update(['category' => $request->new_category]);
                $message = 'Selected items moved to new category successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Get gallery categories
     */
    public function getCategories()
    {
        $categories = Gallery::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category');

        return response()->json($categories);
    }
}
