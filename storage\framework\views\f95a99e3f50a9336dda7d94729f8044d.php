<?php $__env->startSection('title', 'Admin Users Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Admin Users</h3>
                    <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Admin
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.admins.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search admins..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="role" class="form-control">
                                    <option value="">All Roles</option>
                                    <option value="super_admin" <?php echo e(request('role') == 'super_admin' ? 'selected' : ''); ?>>Super Admin</option>
                                    <option value="admin" <?php echo e(request('role') == 'admin' ? 'selected' : ''); ?>>Admin</option>
                                    <option value="moderator" <?php echo e(request('role') == 'moderator' ? 'selected' : ''); ?>>Moderator</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('admin.admins.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <select class="form-select me-2" id="bulkAction" style="width: auto;">
                                    <option value="">Bulk Actions</option>
                                    <option value="activate">Activate</option>
                                    <option value="deactivate">Deactivate</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button class="btn btn-outline-primary" id="applyBulkAction">Apply</button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                Showing <?php echo e($admins->firstItem() ?? 0); ?> to <?php echo e($admins->lastItem() ?? 0); ?> of <?php echo e($admins->total()); ?> admins
                            </small>
                        </div>
                    </div>

                    <!-- Admins Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>Admin</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $admins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $admin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="admin-checkbox" value="<?php echo e($admin->id); ?>">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <?php if($admin->avatar): ?>
                                                    <img src="<?php echo e(asset('storage/' . $admin->avatar)); ?>" 
                                                         alt="<?php echo e($admin->name); ?>" 
                                                         class="rounded-circle" 
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold" 
                                                         style="width: 40px; height: 40px;">
                                                        <?php echo e(strtoupper(substr($admin->name, 0, 2))); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($admin->name); ?></h6>
                                                <small class="text-muted">ID: <?php echo e($admin->id); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?php echo e($admin->email); ?></span>
                                        <?php if($admin->email_verified_at): ?>
                                            <i class="fas fa-check-circle text-success ms-1" title="Email Verified"></i>
                                        <?php else: ?>
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="Email Not Verified"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($admin->role_badge_class); ?>">
                                            <?php echo e($admin->role_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($admin->status_badge_class); ?>">
                                            <?php echo e($admin->status_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($admin->last_login_at): ?>
                                            <span title="<?php echo e($admin->last_login_at->format('Y-m-d H:i:s')); ?>">
                                                <?php echo e($admin->last_login_at->diffForHumans()); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">Never</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.admins.show', $admin)); ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.admins.edit', $admin)); ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($admin->id !== auth()->id()): ?>
                                                <button class="btn btn-sm btn-outline-<?php echo e($admin->status === 'active' ? 'warning' : 'success'); ?>" 
                                                        onclick="toggleStatus(<?php echo e($admin->id); ?>)"
                                                        title="<?php echo e($admin->status === 'active' ? 'Deactivate' : 'Activate'); ?>">
                                                    <i class="fas fa-<?php echo e($admin->status === 'active' ? 'user-slash' : 'user-check'); ?>"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteAdmin(<?php echo e($admin->id); ?>)"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="badge bg-info">Current User</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-users-cog fa-3x mb-3"></i>
                                            <h5>No admin users found</h5>
                                            <p>Start by creating your first admin user.</p>
                                            <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Create Admin
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($admins->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($admins->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.admin-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Toggle admin status
function toggleStatus(adminId) {
    if (confirm('Are you sure you want to change the status of this admin?')) {
        fetch(`/admin/admins/${adminId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating admin status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating admin status');
        });
    }
}

// Delete admin
function deleteAdmin(adminId) {
    if (confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/admins/${adminId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk actions
document.getElementById('applyBulkAction').addEventListener('click', function() {
    const action = document.getElementById('bulkAction').value;
    const selectedAdmins = Array.from(document.querySelectorAll('.admin-checkbox:checked')).map(cb => cb.value);
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    if (selectedAdmins.length === 0) {
        alert('Please select at least one admin');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedAdmins.length} admin(s)?`)) {
        fetch('/admin/admins/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                admins: selectedAdmins
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error performing bulk action');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error performing bulk action');
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/admins/index.blade.php ENDPATH**/ ?>