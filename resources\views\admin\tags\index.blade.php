@extends('layouts.admin')

@section('title', 'Tags Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Tags Management</h3>
                    <a href="{{ route('admin.tags.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Tag
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.tags.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ request('search') }}" placeholder="Search by name or description...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort">Sort By</label>
                                    <select class="form-control" id="sort" name="sort">
                                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                                        <option value="posts_count" {{ request('sort') === 'posts_count' ? 'selected' : '' }}>Posts Count</option>
                                        <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Created Date</option>
                                        <option value="updated_at" {{ request('sort') === 'updated_at' ? 'selected' : '' }}>Updated Date</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-search"></i> Filter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <form id="bulkForm" method="POST" action="{{ route('admin.tags.bulk-action') }}">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="action" class="form-control" required>
                                        <option value="">Select Bulk Action</option>
                                        <option value="activate">Activate</option>
                                        <option value="deactivate">Deactivate</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted">{{ $tags->total() }} total tags</span>
                            </div>
                        </div>

                        <!-- Tags Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Name</th>
                                        <th>Slug</th>
                                        <th>Description</th>
                                        <th>Posts Count</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($tags as $tag)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_ids[]" value="{{ $tag->id }}" class="tag-checkbox">
                                        </td>
                                        <td>
                                            <strong>{{ $tag->name }}</strong>
                                        </td>
                                        <td>
                                            <code>{{ $tag->slug }}</code>
                                        </td>
                                        <td>
                                            @if($tag->description)
                                                {{ Str::limit($tag->description, 50) }}
                                            @else
                                                <span class="text-muted">No description</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $tag->posts_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-{{ $tag->status === 'active' ? 'success' : 'secondary' }}" 
                                                    onclick="toggleStatus({{ $tag->id }})">
                                                {{ $tag->status === 'active' ? 'Active' : 'Inactive' }}
                                            </button>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $tag->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.tags.show', $tag) }}" class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.tags.edit', $tag) }}" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteTag({{ $tag->id }})" title="Delete"
                                                        {{ $tag->posts_count > 0 ? 'disabled' : '' }}>
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-tags fa-3x mb-3"></i>
                                                <h5>No tags found</h5>
                                                <p>Start by creating your first tag.</p>
                                                <a href="{{ route('admin.tags.create') }}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> Create Tag
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    @if($tags->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $tags->firstItem() }} to {{ $tags->lastItem() }} of {{ $tags->total() }} results
                            </p>
                        </div>
                        <div>
                            {{ $tags->appends(request()->query())->links() }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this tag?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This tag will be removed from all associated posts.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Select All functionality
$('#selectAll').change(function() {
    $('.tag-checkbox').prop('checked', this.checked);
});

$('.tag-checkbox').change(function() {
    if (!this.checked) {
        $('#selectAll').prop('checked', false);
    } else if ($('.tag-checkbox:checked').length === $('.tag-checkbox').length) {
        $('#selectAll').prop('checked', true);
    }
});

// Bulk action confirmation
function confirmBulkAction() {
    const selectedTags = $('.tag-checkbox:checked').length;
    const action = $('select[name="action"]').val();
    
    if (selectedTags === 0) {
        alert('Please select at least one tag.');
        return false;
    }
    
    if (action === '') {
        alert('Please select an action.');
        return false;
    }
    
    const actionText = action === 'delete' ? 'delete' : action;
    return confirm(`Are you sure you want to ${actionText} ${selectedTags} selected tag(s)?`);
}

// Toggle status
function toggleStatus(tagId) {
    $.ajax({
        url: `/admin/tags/${tagId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

// Delete tag
function deleteTag(tagId) {
    $('#deleteForm').attr('action', `/admin/tags/${tagId}`);
    $('#deleteModal').modal('show');
}

// Auto-submit form on filter change
$('#status, #sort').change(function() {
    $(this).closest('form').submit();
});

// Clear search on escape
$('#search').keyup(function(e) {
    if (e.keyCode === 27) { // Escape key
        $(this).val('');
    }
});
</script>
@endpush