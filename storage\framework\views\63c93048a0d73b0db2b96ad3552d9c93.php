<?php $__env->startSection('title', 'FAQs Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">FAQs Management</h3>
                    <a href="<?php echo e(route('admin.faqs.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New FAQ
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.faqs.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search FAQs..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('admin.faqs.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- FAQs Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Question</th>
                                    <th>Answer</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?php echo e(Str::limit($faq->question, 60)); ?></h6>
                                            <small class="text-muted">ID: <?php echo e($faq->id); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?php echo e(Str::limit($faq->answer, 80)); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($faq->status_badge_class); ?>">
                                            <?php echo e($faq->status_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?php echo e($faq->sort_order); ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.faqs.show', $faq)); ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.faqs.edit', $faq)); ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-<?php echo e($faq->status === 'active' ? 'warning' : 'success'); ?>" 
                                                    onclick="toggleStatus(<?php echo e($faq->id); ?>)"
                                                    title="<?php echo e($faq->status === 'active' ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($faq->status === 'active' ? 'eye-slash' : 'eye'); ?>"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteFaq(<?php echo e($faq->id); ?>)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-question-circle fa-3x mb-3"></i>
                                            <h5>No FAQs found</h5>
                                            <p>Start by creating your first FAQ.</p>
                                            <a href="<?php echo e(route('admin.faqs.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Create FAQ
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($faqs->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($faqs->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle FAQ status
function toggleStatus(faqId) {
    if (confirm('Are you sure you want to change the status of this FAQ?')) {
        fetch(`/admin/faqs/${faqId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating FAQ status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating FAQ status');
        });
    }
}

// Delete FAQ
function deleteFaq(faqId) {
    if (confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/faqs/${faqId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/faqs/index.blade.php ENDPATH**/ ?>