@extends('admin.layouts.app')

@section('title', 'Programs Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Programs Management</h3>
                    <a href="{{ route('admin.programs.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Program
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.programs.index') }}" class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" name="search" class="form-control" placeholder="Search programs..." value="{{ request('search') }}">
                                </div>
                                <div class="col-md-2">
                                    <select name="status" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="upcoming" {{ request('status') === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                                        <option value="ongoing" {{ request('status') === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="level" class="form-select">
                                        <option value="">All Levels</option>
                                        <option value="beginner" {{ request('level') === 'beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="intermediate" {{ request('level') === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="advanced" {{ request('level') === 'advanced' ? 'selected' : '' }}>Advanced</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="is_active" class="form-select">
                                        <option value="">All</option>
                                        <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="is_online" class="form-select">
                                        <option value="">Online/Offline</option>
                                        <option value="1" {{ request('is_online') === '1' ? 'selected' : '' }}>Online</option>
                                        <option value="0" {{ request('is_online') === '0' ? 'selected' : '' }}>Offline</option>
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form id="bulk-action-form" method="POST" action="{{ route('admin.programs.bulk-action') }}">
                                @csrf
                                <div class="d-flex align-items-center gap-2">
                                    <select name="action" class="form-select" style="width: auto;">
                                        <option value="">Bulk Actions</option>
                                        <option value="activate">Activate</option>
                                        <option value="deactivate">Deactivate</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-secondary" onclick="return confirm('Are you sure you want to perform this action?')">
                                        Apply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Programs Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Instructor</th>
                                    <th>Level</th>
                                    <th>Status</th>
                                    <th>Price</th>
                                    <th>Participants</th>
                                    <th>Event</th>
                                    <th>Active</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($programs as $program)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="programs[]" value="{{ $program->id }}" class="program-checkbox" form="bulk-action-form">
                                    </td>
                                    <td>
                                        @if($program->image)
                                            <img src="{{ Storage::url($program->image) }}" alt="{{ $program->title }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $program->title }}</strong>
                                            <br>
                                            <small class="text-muted">{{ Str::limit($program->short_description, 50) }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $program->instructor ?? 'N/A' }}</td>
                                    <td>
                                        @if($program->level)
                                            <span class="badge bg-info">{{ ucfirst($program->level) }}</span>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $program->status_badge_class }}">
                                            {{ ucfirst($program->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $program->formatted_price }}</td>
                                    <td>{{ $program->max_participants ?? 'Unlimited' }}</td>
                                    <td>
                                        @if($program->event)
                                            <a href="{{ route('admin.events.show', $program->event) }}" class="text-decoration-none">
                                                <i class="fas fa-calendar-alt"></i> {{ Str::limit($program->event->title, 20) }}
                                            </a>
                                        @else
                                            <span class="text-muted">No Event</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   data-id="{{ $program->id }}" 
                                                   {{ $program->is_active ? 'checked' : '' }}>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.programs.show', $program) }}" class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.programs.edit', $program) }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.programs.duplicate', $program) }}" class="btn btn-sm btn-outline-secondary" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </a>
                                            <form action="{{ route('admin.programs.destroy', $program) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this program?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                            <p>No programs found.</p>
                                            <a href="{{ route('admin.programs.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Create First Program
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($programs->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $programs->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.program-checkbox').prop('checked', this.checked);
    });

    // Individual checkbox change
    $('.program-checkbox').change(function() {
        if (!this.checked) {
            $('#select-all').prop('checked', false);
        } else if ($('.program-checkbox:checked').length === $('.program-checkbox').length) {
            $('#select-all').prop('checked', true);
        }
    });

    // Status toggle functionality
    $('.status-toggle').change(function() {
        const programId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/programs/${programId}/toggle-status`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Failed to update status');
                    // Revert the toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Failed to update status');
                // Revert the toggle
                $(this).prop('checked', !isActive);
            }
        });
    });
});
</script>
@endpush