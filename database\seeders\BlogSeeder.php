<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Post;
use App\Models\User;
use App\Models\Category;
use App\Models\Admin;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have categories
        if (Category::count() === 0) {
            Category::create([
                'name' => 'Technology',
                'slug' => 'technology',
                'description' => 'Technology related posts',
                'status' => true,
                'sort_order' => 1
            ]);
            Category::create([
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'Business related posts',
                'status' => true,
                'sort_order' => 2
            ]);
            Category::create([
                'name' => 'Lifestyle',
                'slug' => 'lifestyle',
                'description' => 'Lifestyle related posts',
                'status' => true,
                'sort_order' => 3
            ]);
            Category::create([
                'name' => 'Education',
                'slug' => 'education',
                'description' => 'Education related posts',
                'status' => true,
                'sort_order' => 4
            ]);
            Category::create([
                'name' => 'Health',
                'slug' => 'health',
                'description' => 'Health related posts',
                'status' => true,
                'sort_order' => 5
            ]);
        }

        // Ensure we have admins
        if (Admin::count() === 0) {
            Admin::create([
                'name' => 'Admin User 1',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'status' => 'active'
            ]);
            Admin::create([
                'name' => 'Admin User 2',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'status' => 'active'
            ]);
            Admin::create([
                'name' => 'Admin User 3',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'status' => 'active'
            ]);
        }

        // Create 25 published blog posts
        Post::factory()
            ->count(25)
            ->published()
            ->create();

        // Create 8 featured blog posts
        Post::factory()
            ->count(8)
            ->featured()
            ->published()
            ->create();

        // Create 5 popular blog posts
        Post::factory()
            ->count(5)
            ->popular()
            ->published()
            ->create();

        // Create 7 draft blog posts
        Post::factory()
            ->count(7)
            ->draft()
            ->create();

        // Create some archived posts
        Post::factory()
            ->count(3)
            ->create([
                'status' => 'archived'
            ]);
    }
}