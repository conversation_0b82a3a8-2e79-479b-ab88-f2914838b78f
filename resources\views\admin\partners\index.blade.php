@extends('layouts.admin')

@section('title', 'Partners Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Partners Management</h3>
                    <a href="{{ route('admin.partners.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Partner
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.partners.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search partners..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.partners.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Partners Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Partner</th>
                                    <th>Type</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($partners as $partner)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($partner->logo)
                                                <img src="{{ asset('storage/' . $partner->logo) }}" 
                                                     alt="{{ $partner->name }}" 
                                                     class="rounded me-3" 
                                                     style="width: 50px; height: 50px; object-fit: contain;">
                                            @else
                                                <div class="rounded bg-light d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-handshake text-muted"></i>
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-0">{{ $partner->name }}</h6>
                                                @if($partner->is_featured)
                                                    <span class="badge badge-warning">Featured</span>
                                                @endif
                                                <small class="text-muted d-block">ID: {{ $partner->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($partner->type)
                                            <span class="badge badge-info">{{ ucfirst($partner->type) }}</span>
                                        @else
                                            <span class="text-muted">Not specified</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($partner->email)
                                            <div>
                                                <i class="fas fa-envelope text-muted me-1"></i>
                                                <a href="mailto:{{ $partner->email }}">{{ $partner->email }}</a>
                                            </div>
                                        @endif
                                        @if($partner->phone)
                                            <div>
                                                <i class="fas fa-phone text-muted me-1"></i>
                                                <a href="tel:{{ $partner->phone }}">{{ $partner->phone }}</a>
                                            </div>
                                        @endif
                                        @if($partner->website)
                                            <div>
                                                <i class="fas fa-globe text-muted me-1"></i>
                                                <a href="{{ $partner->website }}" target="_blank">Website</a>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $partner->status_badge_class }}">
                                            {{ $partner->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.partners.show', $partner) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.partners.edit', $partner) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-{{ $partner->status === 'active' ? 'warning' : 'success' }}" 
                                                    onclick="toggleStatus({{ $partner->id }})"
                                                    title="{{ $partner->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $partner->status === 'active' ? 'eye-slash' : 'eye' }}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deletePartner({{ $partner->id }})"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-handshake fa-3x mb-3"></i>
                                            <h5>No partners found</h5>
                                            <p>Start by adding your first partner.</p>
                                            <a href="{{ route('admin.partners.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Add Partner
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($partners->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $partners->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle partner status
function toggleStatus(partnerId) {
    if (confirm('Are you sure you want to change the status of this partner?')) {
        fetch(`/admin/partners/${partnerId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating partner status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating partner status');
        });
    }
}

// Delete partner
function deletePartner(partnerId) {
    if (confirm('Are you sure you want to delete this partner? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/partners/${partnerId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
