@extends('layouts.admin')

@section('title', 'Settings Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Settings Management</h3>
                    <div>
                        <button type="button" class="btn btn-info mr-2" onclick="clearCache()">
                            <i class="fas fa-sync"></i> Clear Cache
                        </button>

                        <div class="btn-group">
                            <button type="button" class="btn btn-success" onclick="exportSettings()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#importModal">
                                <i class="fas fa-upload"></i> Import
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="settingsForm" method="POST" action="{{ route('admin.settings.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <!-- Settings Tabs -->
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            @foreach($groups as $groupKey => $groupLabel)
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link {{ $loop->first ? 'active' : '' }}" 
                                       id="{{ $groupKey }}-tab" 
                                       data-toggle="tab" 
                                       href="#{{ $groupKey }}" 
                                       role="tab">
                                        <i class="fas fa-{{ $groupKey === 'site' ? 'globe' : ($groupKey === 'seo' ? 'search' : ($groupKey === 'social' ? 'share-alt' : ($groupKey === 'email' ? 'envelope' : ($groupKey === 'theme' ? 'palette' : 'cog')))) }}"></i>
                                        {{ $groupLabel }}
                                        @if(isset($settings[$groupKey]) && $settings[$groupKey]->count() > 0)
                                            <span class="badge badge-secondary ml-1">{{ $settings[$groupKey]->count() }}</span>
                                        @endif
                                    </a>
                                </li>
                            @endforeach
                        </ul>

                        <!-- Settings Tab Content -->
                        <div class="tab-content" id="settingsTabContent">
                            @foreach($groups as $groupKey => $groupLabel)
                                <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}" 
                                     id="{{ $groupKey }}" 
                                     role="tabpanel">
                                    <div class="row mt-3">
                                        @if(isset($settings[$groupKey]) && $settings[$groupKey]->count() > 0)
                                            @foreach($settings[$groupKey] as $setting)
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-group">
                                                        <label for="setting_{{ $setting->key }}" class="font-weight-bold">
                                                            {{ ucwords(str_replace('_', ' ', $setting->key)) }}
                                                            @if($setting->is_public)
                                                                <span class="badge badge-info ml-1" title="Public setting">Public</span>
                                                            @endif
                                                        </label>
                                                        
                                                        @if($setting->description)
                                                            <small class="form-text text-muted mb-2">{{ $setting->description }}</small>
                                                        @endif
                                                        
                                                        @switch($setting->type)
                                                            @case('textarea')
                                                                <textarea name="settings[{{ $setting->key }}]" 
                                                                         id="setting_{{ $setting->key }}" 
                                                                         class="form-control" 
                                                                         rows="3"
                                                                         placeholder="Enter {{ strtolower(str_replace('_', ' ', $setting->key)) }}">{{ old('settings.' . $setting->key, $setting->value) }}</textarea>
                                                                @break
                                                            
                                                            @case('boolean')
                                                                <div class="custom-control custom-switch">
                                                                    <input type="checkbox" 
                                                                           class="custom-control-input" 
                                                                           id="setting_{{ $setting->key }}" 
                                                                           name="settings[{{ $setting->key }}]" 
                                                                           value="1"
                                                                           {{ old('settings.' . $setting->key, $setting->value) ? 'checked' : '' }}>
                                                                    <label class="custom-control-label" for="setting_{{ $setting->key }}">
                                                                        {{ $setting->value ? 'Enabled' : 'Disabled' }}
                                                                    </label>
                                                                </div>
                                                                @break
                                                            
                                                            @case('color')
                                                                <div class="input-group">
                                                                    <input type="color" 
                                                                           name="settings[{{ $setting->key }}]" 
                                                                           id="setting_{{ $setting->key }}" 
                                                                           class="form-control form-control-color" 
                                                                           value="{{ old('settings.' . $setting->key, $setting->value ?: '#000000') }}"
                                                                           style="width: 60px;">
                                                                    <input type="text" 
                                                                           class="form-control ml-2" 
                                                                           value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                           readonly>
                                                                </div>
                                                                @break
                                                            
                                                            @case('email')
                                                                <input type="email" 
                                                                       name="settings[{{ $setting->key }}]" 
                                                                       id="setting_{{ $setting->key }}" 
                                                                       class="form-control" 
                                                                       value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                       placeholder="Enter email address">
                                                                @break
                                                            
                                                            @case('url')
                                                                <input type="url" 
                                                                       name="settings[{{ $setting->key }}]" 
                                                                       id="setting_{{ $setting->key }}" 
                                                                       class="form-control" 
                                                                       value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                       placeholder="https://example.com">
                                                                @break
                                                            
                                                            @case('integer')
                                                                <input type="number" 
                                                                       name="settings[{{ $setting->key }}]" 
                                                                       id="setting_{{ $setting->key }}" 
                                                                       class="form-control" 
                                                                       value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                       placeholder="Enter number">
                                                                @break
                                                            
                                                            @case('password')
                                                                <div class="input-group">
                                                                    <input type="password" 
                                                                           name="settings[{{ $setting->key }}]" 
                                                                           id="setting_{{ $setting->key }}" 
                                                                           class="form-control" 
                                                                           value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                           placeholder="Enter password">
                                                                    <div class="input-group-append">
                                                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('setting_{{ $setting->key }}')">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                @break
                                                            
                                                            @default
                                                                <input type="text" 
                                                                       name="settings[{{ $setting->key }}]" 
                                                                       id="setting_{{ $setting->key }}" 
                                                                       class="form-control" 
                                                                       value="{{ old('settings.' . $setting->key, $setting->value) }}"
                                                                       placeholder="Enter {{ strtolower(str_replace('_', ' ', $setting->key)) }}">
                                                        @endswitch
                                                        
                                                        @error('settings.' . $setting->key)
                                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="col-12">
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <strong>No settings found for {{ $groupLabel }}.</strong>
                                                    <br>
                                                    <small>Settings should be automatically initialized. Please refresh the page or contact your system administrator if this persists.</small>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo"></i> Reset
                                        </button>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save"></i> Save All Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Settings</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.settings.import') }}" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="settings_file">Select Settings File (JSON)</label>
                        <input type="file" class="form-control-file" id="settings_file" name="settings_file" accept=".json" required>
                        <small class="form-text text-muted">Upload a JSON file containing settings data.</small>
                    </div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> Importing settings will overwrite existing values with the same keys.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Import Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.nav-tabs .nav-link {
    border-radius: 0.25rem 0.25rem 0 0;
}

.nav-tabs .nav-link.active {
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #f8f9fa;
}

.tab-content {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1rem;
    border-radius: 0 0 0.25rem 0.25rem;
}

.form-control-color {
    height: 38px;
    padding: 0.375rem 0.75rem;
}

.custom-control-label::before {
    border-radius: 1rem;
}

.custom-control-label::after {
    border-radius: 1rem;
}

.badge {
    font-size: 0.7rem;
}

.alert {
    border-radius: 0.375rem;
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}
</style>
@endpush

@push('scripts')
<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Color picker sync
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(function(colorInput) {
        const textInput = colorInput.nextElementSibling;
        
        colorInput.addEventListener('change', function() {
            textInput.value = this.value;
        });
        
        textInput.addEventListener('input', function() {
            if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                colorInput.value = this.value;
            }
        });
    });
    
    // Switch label updates
    const switches = document.querySelectorAll('.custom-control-input[type="checkbox"]');
    switches.forEach(function(switchInput) {
        const label = switchInput.nextElementSibling;
        
        switchInput.addEventListener('change', function() {
            label.textContent = this.checked ? 'Enabled' : 'Disabled';
        });
    });
});

// Clear cache
function clearCache() {
    if (confirm('Are you sure you want to clear the settings cache?')) {
        fetch('{{ route("admin.settings.clear-cache") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache cleared successfully!');
            } else {
                alert('Failed to clear cache.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing cache.');
        });
    }
}



// Export settings
function exportSettings() {
    window.open('{{ route("admin.settings.export") }}', '_blank');
}

// Reset form
function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('settingsForm').reset();
        
        // Reset switch labels
        const switches = document.querySelectorAll('.custom-control-input[type="checkbox"]');
        switches.forEach(function(switchInput) {
            const label = switchInput.nextElementSibling;
            label.textContent = switchInput.checked ? 'Enabled' : 'Disabled';
        });
        
        // Reset color inputs
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(function(colorInput) {
            const textInput = colorInput.nextElementSibling;
            textInput.value = colorInput.value;
        });
    }
}

// Form submission with loading state
document.getElementById('settingsForm').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds as fallback
    setTimeout(function() {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>
@endpush