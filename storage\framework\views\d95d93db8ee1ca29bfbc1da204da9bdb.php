<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $__env->yieldContent('title', 'Admin Panel'); ?> - <?php echo e(config('app.name')); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 250px;
            --primary-color: #023317;
            --secondary-color: #90d276;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgb(232, 232, 232);
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .sidebar-menu .nav-link {
            color: rgba(255, 255, 255);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background-color: rgba(3, 23, 3);
            border-left: 3px solid white;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid #e9ecef;
        }
        
        .content-wrapper {
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            transform: translateY(-1px);
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4 class="mb-0">
                <i class="fas fa-cogs me-2"></i>
                Admin Panel
            </h4>
        </div>
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e(request()->routeIs('admin.admins.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.admins.index')); ?>">
                        <i class="fas fa-users-cog me-2"></i>
                        Admin Users
                    </a>
                </li>

                <!-- Content Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.posts.*') || request()->routeIs('admin.categories.*') || request()->routeIs('admin.comments.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#contentMenu">
                        <i class="fas fa-edit me-2"></i>
                        Content Management
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.posts.*') || request()->routeIs('admin.categories.*') || request()->routeIs('admin.comments.*') ? 'show' : ''); ?>" id="contentMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.posts.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.posts.index')); ?>">
                                    <i class="fas fa-blog me-2"></i>
                                    Blog Posts
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.categories.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.categories.index')); ?>">
                                    <i class="fas fa-tags me-2"></i>
                                    Categories
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.comments.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.comments.index')); ?>">
                                    <i class="fas fa-comments me-2"></i>
                                    Comments
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Business Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.products.*') || request()->routeIs('admin.services.*') || request()->routeIs('admin.programs.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#businessMenu">
                        <i class="fas fa-industry me-2"></i>
                        Business Management
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.products.*') || request()->routeIs('admin.services.*') || request()->routeIs('admin.programs.*') ? 'show' : ''); ?>" id="businessMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.products.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.products.index')); ?>">
                                    <i class="fas fa-boxes me-2"></i>
                                    Products
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.services.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.services.index')); ?>">
                                    <i class="fas fa-handshake me-2"></i>
                                    Services
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.programs.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.programs.index')); ?>">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Programs
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Media Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.gallery.*') || request()->routeIs('admin.files.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#mediaMenu">
                        <i class="fas fa-images me-2"></i>
                        Media Management
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.gallery.*') || request()->routeIs('admin.files.*') ? 'show' : ''); ?>" id="mediaMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.gallery.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.gallery.index')); ?>">
                                    <i class="fas fa-photo-video me-2"></i>
                                    Gallery
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.files.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.files.index')); ?>">
                                    <i class="fas fa-folder-open me-2"></i>
                                    File Manager
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Website Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.events.*') || request()->routeIs('admin.faqs.*') || request()->routeIs('admin.testimonials.*') || request()->routeIs('admin.partners.*') || request()->routeIs('admin.contacts.*') || request()->routeIs('admin.newsletters.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#websiteMenu">
                        <i class="fas fa-globe-americas me-2"></i>
                        Website Management
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.events.*') || request()->routeIs('admin.faqs.*') || request()->routeIs('admin.testimonials.*') || request()->routeIs('admin.partners.*') || request()->routeIs('admin.contacts.*') || request()->routeIs('admin.newsletters.*') ? 'show' : ''); ?>" id="websiteMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.events.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.events.index')); ?>">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Events
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.faqs.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.faqs.index')); ?>">
                                    <i class="fas fa-question-circle me-2"></i>
                                    FAQs
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.testimonials.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.testimonials.index')); ?>">
                                    <i class="fas fa-quote-right me-2"></i>
                                    Testimonials
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.partners.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.partners.index')); ?>">
                                    <i class="fas fa-handshake me-2"></i>
                                    Partners
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.contacts.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.contacts.index')); ?>">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Messages
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.newsletters.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.newsletters.index')); ?>">
                                    <i class="fas fa-newspaper me-2"></i>
                                    Newsletter Subscribers
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#settingsMenu">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </a>
                    <div class="collapse <?php echo e(request()->routeIs('admin.settings.*') ? 'show' : ''); ?>" id="settingsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.general') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.general')); ?>">
                                    <i class="fas fa-globe me-2"></i>
                                    General
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.seo') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.seo')); ?>">
                                    <i class="fas fa-search me-2"></i>
                                    SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.contact') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.contact')); ?>">
                                    <i class="fas fa-address-book me-2"></i>
                                    Contact
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.social') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.social')); ?>">
                                    <i class="fas fa-share-alt me-2"></i>
                                    Social Media
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.smtp') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.smtp')); ?>">
                                    <i class="fas fa-envelope me-2"></i>
                                    SMTP
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.recaptcha') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.recaptcha')); ?>">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    reCAPTCHA
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e(request()->routeIs('admin.settings.newsletter') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.newsletter')); ?>">
                                    <i class="fas fa-newspaper me-2"></i>
                                    Newsletter
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <button class="btn btn-link d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <?php if(auth('admin')->user()->avatar): ?>
                                <img src="<?php echo e(asset('storage/' . auth('admin')->user()->avatar)); ?>" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                            <?php else: ?>
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            <?php endif; ?>
                            <?php echo e(auth('admin')->user()->name); ?>

                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?php echo e(route('admin.admins.show', auth('admin')->id())); ?>">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="<?php echo e(route('admin.logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-wrapper">
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><i class="fas fa-exclamation-circle me-2"></i><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/layouts/admin.blade.php ENDPATH**/ ?>