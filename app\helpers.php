<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        return app('settings')->get($key, $default);
    }
}

if (!function_exists('settings')) {
    /**
     * Get the settings service instance
     *
     * @return \App\Services\SettingsService
     */
    function settings()
    {
        return app('settings');
    }
}

if (!function_exists('public_settings')) {
    /**
     * Get all public settings
     *
     * @return array
     */
    function public_settings()
    {
        return app('settings')->getPublicSettings();
    }
}

if (!function_exists('site_setting')) {
    /**
     * Get a site setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function site_setting($key, $default = null)
    {
        $siteSettings = app('settings')->getSiteConfig();
        return $siteSettings[$key] ?? $default;
    }
}

if (!function_exists('theme_setting')) {
    /**
     * Get a theme setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function theme_setting($key, $default = null)
    {
        $themeSettings = app('settings')->getThemeSettings();
        return $themeSettings[$key] ?? $default;
    }
}