<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Display settings management page
     */
    public function index()
    {
        $groups = [
            'site' => 'Site Settings',
            'seo' => 'SEO Settings',
            'social' => 'Social Media',
            'email' => 'Email Settings',
            'theme' => 'Theme Settings',
            'general' => 'General Settings'
        ];

        $settings = [];
        foreach ($groups as $group => $label) {
            $settings[$group] = Setting::where('group', $group)->get();
        }

        return view('admin.settings.index', compact('settings', 'groups'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'nullable'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            foreach ($request->input('settings', []) as $key => $value) {
                $setting = Setting::where('key', $key)->first();
                
                if ($setting) {
                    // Handle different input types
                    if ($setting->type === 'boolean') {
                        $value = $request->has("settings.{$key}") ? '1' : '0';
                    } elseif ($setting->type === 'array' || $setting->type === 'json') {
                        $value = is_array($value) ? json_encode($value) : $value;
                    }
                    
                    $setting->update(['value' => $value]);
                }
            }

            // Clear settings cache
            $this->settingsService->clearCache();

            return redirect()->back()->with('success', 'Settings updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults()
    {
        try {
            $this->settingsService->initializeDefaults();
            return redirect()->back()->with('success', 'Default settings initialized successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to initialize defaults: ' . $e->getMessage());
        }
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        try {
            $this->settingsService->clearCache();
            return redirect()->back()->with('success', 'Settings cache cleared successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Export settings
     */
    public function export()
    {
        try {
            $settings = Setting::all()->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'type' => $setting->type,
                    'group' => $setting->group,
                    'description' => $setting->description,
                    'is_public' => $setting->is_public
                ];
            });

            $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.json';
            
            return response()->json($settings)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to export settings: ' . $e->getMessage());
        }
    }

    /**
     * Import settings
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings_file' => 'required|file|mimes:json'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->with('error', 'Please select a valid JSON file.');
        }

        try {
            $file = $request->file('settings_file');
            $content = file_get_contents($file->getPathname());
            $settings = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return redirect()->back()->with('error', 'Invalid JSON file format.');
            }

            $imported = 0;
            foreach ($settings as $settingData) {
                if (isset($settingData['key'])) {
                    Setting::updateOrCreate(
                        ['key' => $settingData['key']],
                        [
                            'value' => $settingData['value'] ?? '',
                            'type' => $settingData['type'] ?? 'text',
                            'group' => $settingData['group'] ?? 'general',
                            'description' => $settingData['description'] ?? '',
                            'is_public' => $settingData['is_public'] ?? false
                        ]
                    );
                    $imported++;
                }
            }

            // Clear settings cache
            $this->settingsService->clearCache();

            return redirect()->back()->with('success', "Successfully imported {$imported} settings!");
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to import settings: ' . $e->getMessage());
        }
    }

    /**
     * Get settings as JSON (for API)
     */
    public function getSettings(Request $request)
    {
        $group = $request->get('group');
        $publicOnly = $request->boolean('public_only', false);

        try {
            if ($group) {
                $settings = $this->settingsService->getByGroup($group);
            } elseif ($publicOnly) {
                $settings = $this->settingsService->getPublicSettings();
            } else {
                $settings = Setting::all()->mapWithKeys(function ($setting) {
                    return [$setting->key => $setting->value];
                });
            }

            return response()->json([
                'success' => true,
                'settings' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a single setting (for AJAX)
     */
    public function updateSetting(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string',
            'value' => 'nullable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid input data'
            ], 400);
        }

        try {
            $key = $request->input('key');
            $value = $request->input('value');
            
            $setting = Setting::where('key', $key)->first();
            
            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            // Handle boolean values
            if ($setting->type === 'boolean') {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN) ? '1' : '0';
            }

            $setting->update(['value' => $value]);

            return response()->json([
                'success' => true,
                'message' => 'Setting updated successfully',
                'setting' => [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'type' => $setting->type
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}