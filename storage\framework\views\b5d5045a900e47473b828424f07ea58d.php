<?php $__env->startSection('title', 'Partners Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Partners Management</h3>
                    <a href="<?php echo e(route('admin.partners.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Partner
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.partners.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search partners..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('admin.partners.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Partners Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Partner</th>
                                    <th>Type</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $partners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($partner->logo): ?>
                                                <img src="<?php echo e(asset('storage/' . $partner->logo)); ?>" 
                                                     alt="<?php echo e($partner->name); ?>" 
                                                     class="rounded me-3" 
                                                     style="width: 50px; height: 50px; object-fit: contain;">
                                            <?php else: ?>
                                                <div class="rounded bg-light d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-handshake text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($partner->name); ?></h6>
                                                <?php if($partner->is_featured): ?>
                                                    <span class="badge badge-warning">Featured</span>
                                                <?php endif; ?>
                                                <small class="text-muted d-block">ID: <?php echo e($partner->id); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($partner->type): ?>
                                            <span class="badge badge-info"><?php echo e(ucfirst($partner->type)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Not specified</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($partner->email): ?>
                                            <div>
                                                <i class="fas fa-envelope text-muted me-1"></i>
                                                <a href="mailto:<?php echo e($partner->email); ?>"><?php echo e($partner->email); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($partner->phone): ?>
                                            <div>
                                                <i class="fas fa-phone text-muted me-1"></i>
                                                <a href="tel:<?php echo e($partner->phone); ?>"><?php echo e($partner->phone); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($partner->website): ?>
                                            <div>
                                                <i class="fas fa-globe text-muted me-1"></i>
                                                <a href="<?php echo e($partner->website); ?>" target="_blank">Website</a>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($partner->status_badge_class); ?>">
                                            <?php echo e($partner->status_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.partners.show', $partner)); ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.partners.edit', $partner)); ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-<?php echo e($partner->status === 'active' ? 'warning' : 'success'); ?>" 
                                                    onclick="toggleStatus(<?php echo e($partner->id); ?>)"
                                                    title="<?php echo e($partner->status === 'active' ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($partner->status === 'active' ? 'eye-slash' : 'eye'); ?>"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deletePartner(<?php echo e($partner->id); ?>)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-handshake fa-3x mb-3"></i>
                                            <h5>No partners found</h5>
                                            <p>Start by adding your first partner.</p>
                                            <a href="<?php echo e(route('admin.partners.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Add Partner
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($partners->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($partners->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle partner status
function toggleStatus(partnerId) {
    if (confirm('Are you sure you want to change the status of this partner?')) {
        fetch(`/admin/partners/${partnerId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating partner status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating partner status');
        });
    }
}

// Delete partner
function deletePartner(partnerId) {
    if (confirm('Are you sure you want to delete this partner? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/partners/${partnerId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/partners/index.blade.php ENDPATH**/ ?>