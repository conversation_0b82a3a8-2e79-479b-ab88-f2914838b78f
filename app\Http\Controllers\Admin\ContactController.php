<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Contact::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('subject', 'like', '%' . $request->search . '%')
                  ->orWhere('message', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter by read status
        if ($request->filled('read_status')) {
            if ($request->read_status === 'read') {
                $query->read();
            } elseif ($request->read_status === 'unread') {
                $query->unread();
            }
        }

        $contacts = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.contacts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'status' => 'required|in:new,in_progress,resolved,closed',
            'admin_notes' => 'nullable|string',
            'ip_address' => 'nullable|ip',
            'user_agent' => 'nullable|string'
        ]);

        // Set IP and User Agent if not provided
        if (empty($validated['ip_address'])) {
            $validated['ip_address'] = $request->ip();
        }
        if (empty($validated['user_agent'])) {
            $validated['user_agent'] = $request->userAgent();
        }

        Contact::create($validated);

        return redirect()->route('admin.contacts.index')
                        ->with('success', 'Contact inquiry created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        // Mark as read when viewing
        if (!$contact->is_read) {
            $contact->markAsRead();
        }

        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contact $contact)
    {
        return view('admin.contacts.edit', compact('contact'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contact $contact)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'status' => 'required|in:new,in_progress,resolved,closed',
            'admin_notes' => 'nullable|string'
        ]);

        $contact->update($validated);

        return redirect()->route('admin.contacts.index')
                        ->with('success', 'Contact inquiry updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
                        ->with('success', 'Contact inquiry deleted successfully.');
    }

    /**
     * Mark contact as read
     */
    public function markAsRead(Contact $contact)
    {
        $contact->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'Contact marked as read.',
            'is_read' => true
        ]);
    }

    /**
     * Mark contact as unread
     */
    public function markAsUnread(Contact $contact)
    {
        $contact->markAsUnread();
        
        return response()->json([
            'success' => true,
            'message' => 'Contact marked as unread.',
            'is_read' => false
        ]);
    }

    /**
     * Update contact status
     */
    public function updateStatus(Request $request, Contact $contact)
    {
        $validated = $request->validate([
            'status' => 'required|in:new,in_progress,resolved,closed'
        ]);

        $contact->update($validated);
        
        return response()->json([
            'success' => true,
            'message' => 'Contact status updated successfully.',
            'status' => $contact->status
        ]);
    }

    /**
     * Bulk action for contacts
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:mark_read,mark_unread,delete,update_status',
            'contact_ids' => 'required|array',
            'contact_ids.*' => 'exists:contacts,id',
            'status' => 'required_if:action,update_status|in:new,in_progress,resolved,closed'
        ]);

        $contacts = Contact::whereIn('id', $validated['contact_ids']);
        
        switch ($validated['action']) {
            case 'mark_read':
                $contacts->update(['read_at' => now()]);
                $message = 'Contacts marked as read.';
                break;
            case 'mark_unread':
                $contacts->update(['read_at' => null]);
                $message = 'Contacts marked as unread.';
                break;
            case 'update_status':
                $contacts->update(['status' => $validated['status']]);
                $message = 'Contact status updated successfully.';
                break;
            case 'delete':
                $contacts->delete();
                $message = 'Contacts deleted successfully.';
                break;
        }

        return redirect()->route('admin.contacts.index')
                        ->with('success', $message);
    }
}
