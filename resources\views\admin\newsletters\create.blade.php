@extends('layouts.admin')

@section('title', 'Add Newsletter Subscriber')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Add Newsletter Subscriber</h3>
                    <a href="{{ route('admin.newsletters.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <form action="{{ route('admin.newsletters.store') }}" method="POST" id="newsletterForm">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Subscriber Information -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscriber Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                           id="email" name="email" value="{{ old('email') }}" required>
                                                    @error('email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Primary email address for newsletter subscription</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name">Full Name</label>
                                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                           id="name" name="name" value="{{ old('name') }}">
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: Subscriber's full name</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="phone">Phone Number</label>
                                                    <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                                           id="phone" name="phone" value="{{ old('phone') }}">
                                                    @error('phone')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: Contact phone number</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">Location</label>
                                                    <input type="text" class="form-control @error('location') is-invalid @enderror"
                                                           id="location" name="location" value="{{ old('location') }}">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: City, country or region</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Newsletter Preferences -->
                                        <div class="form-group">
                                            <label>Newsletter Preferences</label>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_weekly"
                                                               name="preferences[]" value="weekly_digest"
                                                               {{ in_array('weekly_digest', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_weekly">
                                                            Weekly Digest
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_breaking"
                                                               name="preferences[]" value="breaking_news"
                                                               {{ in_array('breaking_news', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_breaking">
                                                            Breaking News
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_promotions"
                                                               name="preferences[]" value="promotions"
                                                               {{ in_array('promotions', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_promotions">
                                                            Promotions
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_events"
                                                               name="preferences[]" value="events"
                                                               {{ in_array('events', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_events">
                                                            Events & Updates
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_products"
                                                               name="preferences[]" value="product_updates"
                                                               {{ in_array('product_updates', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_products">
                                                            Product Updates
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_tips"
                                                               name="preferences[]" value="tips_tutorials"
                                                               {{ in_array('tips_tutorials', old('preferences', [])) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_tips">
                                                            Tips & Tutorials
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Select the types of newsletters this subscriber wants to receive</small>
                                        </div>

                                        <!-- Additional Notes -->
                                        <div class="form-group">
                                            <label for="notes">Admin Notes</label>
                                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                                      id="notes" name="notes" rows="3"
                                                      placeholder="Internal notes about this subscriber...">{{ old('notes') }}</textarea>
                                            @error('notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Internal notes (not visible to subscriber)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-4">
                                <!-- Subscription Settings -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscription Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                <option value="unsubscribed" {{ old('status') === 'unsubscribed' ? 'selected' : '' }}>Unsubscribed</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="is_verified"
                                                       name="is_verified" value="1"
                                                       {{ old('is_verified', '1') ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_verified">
                                                    Email Verified
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Mark as verified to skip email confirmation</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="subscribed_at">Subscription Date</label>
                                            <input type="datetime-local" class="form-control @error('subscribed_at') is-invalid @enderror"
                                                   id="subscribed_at" name="subscribed_at"
                                                   value="{{ old('subscribed_at', now()->format('Y-m-d\TH:i')) }}">
                                            @error('subscribed_at')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">When the subscription was created</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="source">Subscription Source</label>
                                            <select class="form-control @error('source') is-invalid @enderror" id="source" name="source">
                                                <option value="manual" {{ old('source', 'manual') === 'manual' ? 'selected' : '' }}>Manual (Admin)</option>
                                                <option value="website" {{ old('source') === 'website' ? 'selected' : '' }}>Website Form</option>
                                                <option value="popup" {{ old('source') === 'popup' ? 'selected' : '' }}>Popup</option>
                                                <option value="footer" {{ old('source') === 'footer' ? 'selected' : '' }}>Footer</option>
                                                <option value="social_media" {{ old('source') === 'social_media' ? 'selected' : '' }}>Social Media</option>
                                                <option value="import" {{ old('source') === 'import' ? 'selected' : '' }}>Import</option>
                                                <option value="api" {{ old('source') === 'api' ? 'selected' : '' }}>API</option>
                                                <option value="other" {{ old('source') === 'other' ? 'selected' : '' }}>Other</option>
                                            </select>
                                            @error('source')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="ip_address">IP Address</label>
                                            <input type="text" class="form-control @error('ip_address') is-invalid @enderror"
                                                   id="ip_address" name="ip_address"
                                                   value="{{ old('ip_address', request()->ip()) }}">
                                            @error('ip_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">IP address when subscribed</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="user_agent">User Agent</label>
                                            <textarea class="form-control @error('user_agent') is-invalid @enderror"
                                                      id="user_agent" name="user_agent" rows="3"
                                                      placeholder="Browser/device information...">{{ old('user_agent', request()->userAgent()) }}</textarea>
                                            @error('user_agent')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Browser/device information</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Preview Card -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscriber Preview</h5>
                                    </div>
                                    <div class="card-body" id="subscriberPreview">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-envelope fa-2x mb-2"></i>
                                            <p>Enter subscriber details to see preview</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary mr-2">
                                    <i class="fas fa-save"></i> Create Subscriber
                                </button>
                                <button type="button" class="btn btn-success mr-2" id="saveAndSendWelcome">
                                    <i class="fas fa-envelope"></i> Create & Send Welcome Email
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.newsletters.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.custom-control-label {
    font-weight: normal;
}

.form-text {
    font-size: 0.875rem;
}

#subscriberPreview .subscriber-info {
    text-align: left;
}

#subscriberPreview .subscriber-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #2678a1db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

#subscriberPreview .preferences-list {
    list-style: none;
    padding: 0;
}

#subscriberPreview .preferences-list li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

#subscriberPreview .preferences-list li:last-child {
    border-bottom: none;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Real-time preview update
    function updatePreview() {
        const email = $('#email').val();
        const name = $('#name').val();
        const phone = $('#phone').val();
        const location = $('#location').val();
        const status = $('#status').val();
        const isVerified = $('#is_verified').is(':checked');
        const source = $('#source').val();

        // Get selected preferences
        const preferences = [];
        $('input[name="preferences[]"]:checked').each(function() {
            preferences.push($(this).next('label').text().trim());
        });

        if (email) {
            const avatar = email.charAt(0).toUpperCase();
            let previewHtml = `
                <div class="subscriber-info">
                    <div class="subscriber-avatar">${avatar}</div>
                    <div class="text-center mb-3">
                        <strong>${name || 'No name provided'}</strong><br>
                        <span class="text-muted">${email}</span>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <span class="badge badge-${status === 'active' ? 'success' : (status === 'inactive' ? 'warning' : 'danger')}">
                                ${status.charAt(0).toUpperCase() + status.slice(1)}
                            </span>
                        </div>
                        <div class="col-6">
                            <span class="badge badge-${isVerified ? 'success' : 'warning'}">
                                ${isVerified ? 'Verified' : 'Unverified'}
                            </span>
                        </div>
                    </div>

                    ${phone ? `<p><i class="fas fa-phone"></i> ${phone}</p>` : ''}
                    ${location ? `<p><i class="fas fa-map-marker-alt"></i> ${location}</p>` : ''}
                    <p><i class="fas fa-source"></i> Source: ${source.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>

                    ${preferences.length > 0 ? `
                        <div class="mt-3">
                            <strong>Preferences:</strong>
                            <ul class="preferences-list mt-2">
                                ${preferences.map(pref => `<li><i class="fas fa-check text-success"></i> ${pref}</li>`).join('')}
                            </ul>
                        </div>
                    ` : '<p class="text-muted">No preferences selected</p>'}
                </div>
            `;

            $('#subscriberPreview').html(previewHtml);
        } else {
            $('#subscriberPreview').html(`
                <div class="text-center text-muted">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <p>Enter subscriber details to see preview</p>
                </div>
            `);
        }
    }

    // Update preview on input change
    $('#email, #name, #phone, #location, #status, #source').on('input change', updatePreview);
    $('#is_verified').on('change', updatePreview);
    $('input[name="preferences[]"]').on('change', updatePreview);

    // Initial preview update
    updatePreview();

    // Form validation
    $('#newsletterForm').on('submit', function(e) {
        const email = $('#email').val().trim();

        if (!email) {
            e.preventDefault();
            alert('Email address is required.');
            $('#email').focus();
            return false;
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            $('#email').focus();
            return false;
        }
    });

    // Save and send welcome email
    $('#saveAndSendWelcome').on('click', function() {
        // Add hidden input to indicate welcome email should be sent
        $('<input>').attr({
            type: 'hidden',
            name: 'send_welcome_email',
            value: '1'
        }).appendTo('#newsletterForm');

        $('#newsletterForm').submit();
    });

    // Auto-fill current date/time for subscription date
    if (!$('#subscribed_at').val()) {
        const now = new Date();
        const formattedDate = now.toISOString().slice(0, 16);
        $('#subscribed_at').val(formattedDate);
    }

    // Character counter for notes
    $('#notes').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after(`<small class="form-text text-muted char-counter"></small>`);
        }

        $(this).next('.char-counter').text(`${currentLength}/${maxLength} characters`);

        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger');
        }
    });
});
</script>
@endpush
