<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ServiceController extends Controller
{
    /**
     * Display a listing of the services.
     */
    public function index(Request $request)
    {
        $query = Service::with(['category']);

        // Filter by category
        if ($request->has('category_id') && $request->category_id !== '') {
            $query->where('category_id', $request->category_id);
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->has('search') && $request->search !== '') {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        $services = $query->latest()->paginate(15);
        $categories = Category::where('status', 'active')->get();

        return view('admin.services.index', compact('services', 'categories'));
    }

    /**
     * Show the form for creating a new service.
     */
    public function create()
    {
        $categories = Category::where('status', 'active')->get();
        return view('admin.services.create', compact('categories'));
    }

    /**
     * Store a newly created service in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'nullable|exists:categories,id',
            'price' => 'nullable|numeric|min:0',
            'duration' => 'nullable|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:active,inactive',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'features' => 'nullable|array',
            'benefits' => 'nullable|array',
            'requirements' => 'nullable|array',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('services', 'public');
        }

        // Handle gallery images
        if ($request->hasFile('gallery')) {
            $galleryImages = [];
            foreach ($request->file('gallery') as $image) {
                $galleryImages[] = $image->store('services/gallery', 'public');
            }
            $data['gallery'] = $galleryImages;
        }

        Service::create($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified service.
     */
    public function show(Service $service)
    {
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service.
     */
    public function edit(Service $service)
    {
        $categories = Category::where('status', 'active')->get();
        return view('admin.services.edit', compact('service', 'categories'));
    }

    /**
     * Update the specified service in storage.
     */
    public function update(Request $request, Service $service)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'nullable|exists:categories,id',
            'price' => 'nullable|numeric|min:0',
            'duration' => 'nullable|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:active,inactive',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'features' => 'nullable|array',
            'benefits' => 'nullable|array',
            'requirements' => 'nullable|array',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($service->featured_image) {
                Storage::disk('public')->delete($service->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('services', 'public');
        }

        // Handle gallery images
        if ($request->hasFile('gallery')) {
            // Delete old gallery images
            if ($service->gallery) {
                foreach ($service->gallery as $image) {
                    Storage::disk('public')->delete($image);
                }
            }
            
            $galleryImages = [];
            foreach ($request->file('gallery') as $image) {
                $galleryImages[] = $image->store('services/gallery', 'public');
            }
            $data['gallery'] = $galleryImages;
        }

        $service->update($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified service from storage.
     */
    public function destroy(Service $service)
    {
        // Delete featured image
        if ($service->featured_image) {
            Storage::disk('public')->delete($service->featured_image);
        }

        // Delete gallery images
        if ($service->gallery) {
            foreach ($service->gallery as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'Service deleted successfully.');
    }

    /**
     * Toggle service status
     */
    public function toggleStatus(Service $service)
    {
        $service->update([
            'status' => $service->status === 'active' ? 'inactive' : 'active'
        ]);

        return response()->json([
            'success' => true,
            'status' => $service->status,
            'message' => 'Service status updated successfully.'
        ]);
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Service $service)
    {
        $service->update([
            'is_featured' => !$service->is_featured
        ]);

        return response()->json([
            'success' => true,
            'is_featured' => $service->is_featured,
            'message' => 'Service featured status updated successfully.'
        ]);
    }

    /**
     * Bulk actions for services
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate,feature,unfeature',
            'services' => 'required|array',
            'services.*' => 'exists:services,id'
        ]);

        $services = Service::whereIn('id', $request->services);

        switch ($request->action) {
            case 'delete':
                foreach ($services->get() as $service) {
                    if ($service->featured_image) {
                        Storage::disk('public')->delete($service->featured_image);
                    }
                    if ($service->gallery) {
                        foreach ($service->gallery as $image) {
                            Storage::disk('public')->delete($image);
                        }
                    }
                }
                $services->delete();
                $message = 'Selected services deleted successfully.';
                break;
            case 'activate':
                $services->update(['status' => 'active']);
                $message = 'Selected services activated successfully.';
                break;
            case 'deactivate':
                $services->update(['status' => 'inactive']);
                $message = 'Selected services deactivated successfully.';
                break;
            case 'feature':
                $services->update(['is_featured' => true]);
                $message = 'Selected services marked as featured successfully.';
                break;
            case 'unfeature':
                $services->update(['is_featured' => false]);
                $message = 'Selected services unmarked as featured successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }
}
