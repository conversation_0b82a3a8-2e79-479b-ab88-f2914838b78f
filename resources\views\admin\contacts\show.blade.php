@extends('layouts.admin')

@section('title', 'Contact Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        Contact Details
                        @if(!$contact->is_read)
                            <span class="badge badge-warning ml-2">UNREAD</span>
                        @endif
                        <span class="badge badge-{{ $contact->status === 'new' ? 'primary' : ($contact->status === 'in_progress' ? 'warning' : ($contact->status === 'resolved' ? 'success' : 'secondary')) }} ml-2">
                            {{ ucfirst(str_replace('_', ' ', $contact->status)) }}
                        </span>
                    </h3>
                    <div>
                        <a href="{{ route('admin.contacts.edit', $contact) }}" class="btn btn-warning mr-2">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Contacts
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Contact Details -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Contact Information</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <label>Full Name:</label>
                                                <p class="info-value">{{ $contact->name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <label>Email Address:</label>
                                                <p class="info-value">
                                                    <a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <label>Phone Number:</label>
                                                <p class="info-value">
                                                    @if($contact->phone)
                                                        <a href="tel:{{ $contact->phone }}">{{ $contact->phone }}</a>
                                                    @else
                                                        <span class="text-muted">Not provided</span>
                                                    @endif
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="info-group">
                                                <label>Company:</label>
                                                <p class="info-value">
                                                    {{ $contact->company ?: 'Not provided' }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <label>Subject:</label>
                                        <p class="info-value">{{ $contact->subject }}</p>
                                    </div>
                                    <div class="info-group">
                                        <label>Message:</label>
                                        <div class="message-content">
                                            {!! nl2br(e($contact->message)) !!}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if($contact->admin_notes)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">Admin Notes</h4>
                                </div>
                                <div class="card-body">
                                    <div class="admin-notes">
                                        {!! nl2br(e($contact->admin_notes)) !!}
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Reply Section -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">Reply to Contact</h4>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('admin.contacts.reply', $contact) }}" method="POST" id="replyForm">
                                        @csrf
                                        <div class="form-group">
                                            <label for="reply_subject">Subject</label>
                                            <input type="text" class="form-control" id="reply_subject" name="reply_subject"
                                                   value="Re: {{ $contact->subject }}" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="reply_message">Message</label>
                                            <textarea class="form-control" id="reply_message" name="reply_message"
                                                      rows="6" required placeholder="Type your reply here..."></textarea>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="mark_resolved" name="mark_resolved" value="1">
                                                <label class="custom-control-label" for="mark_resolved">Mark as resolved after sending</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-reply"></i> Send Reply
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Sidebar -->
                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Quick Actions</h4>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group-vertical w-100">
                                        <a href="{{ route('admin.contacts.edit', $contact) }}" class="btn btn-warning btn-sm mb-2">
                                            <i class="fas fa-edit"></i> Edit Contact
                                        </a>
                                        @if(!$contact->is_read)
                                            <button type="button" class="btn btn-success btn-sm mb-2" onclick="markAsRead({{ $contact->id }})">
                                                <i class="fas fa-envelope-open"></i> Mark as Read
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-secondary btn-sm mb-2" onclick="markAsUnread({{ $contact->id }})">
                                                <i class="fas fa-envelope"></i> Mark as Unread
                                            </button>
                                        @endif
                                        <div class="dropdown mb-2">
                                            <button class="btn btn-info btn-sm dropdown-toggle w-100" type="button" data-toggle="dropdown">
                                                <i class="fas fa-tasks"></i> Change Status
                                            </button>
                                            <div class="dropdown-menu w-100">
                                                <a class="dropdown-item" href="#" onclick="changeStatus({{ $contact->id }}, 'new')">
                                                    <i class="fas fa-circle text-primary"></i> New
                                                </a>
                                                <a class="dropdown-item" href="#" onclick="changeStatus({{ $contact->id }}, 'in_progress')">
                                                    <i class="fas fa-clock text-warning"></i> In Progress
                                                </a>
                                                <a class="dropdown-item" href="#" onclick="changeStatus({{ $contact->id }}, 'resolved')">
                                                    <i class="fas fa-check text-success"></i> Resolved
                                                </a>
                                                <a class="dropdown-item" href="#" onclick="changeStatus({{ $contact->id }}, 'closed')">
                                                    <i class="fas fa-times text-secondary"></i> Closed
                                                </a>
                                            </div>
                                        </div>
                                        <a href="mailto:{{ $contact->email }}?subject=Re: {{ urlencode($contact->subject) }}" class="btn btn-primary btn-sm mb-2">
                                            <i class="fas fa-envelope"></i> Send Email
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteContact({{ $contact->id }})">
                                            <i class="fas fa-trash"></i> Delete Contact
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">Contact Information</h4>
                                </div>
                                <div class="card-body">
                                    <div class="info-item">
                                        <strong>Status:</strong>
                                        <span class="badge badge-{{ $contact->status === 'new' ? 'primary' : ($contact->status === 'in_progress' ? 'warning' : ($contact->status === 'resolved' ? 'success' : 'secondary')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $contact->status)) }}
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Priority:</strong>
                                        <span class="badge badge-{{ $contact->priority === 'urgent' ? 'danger' : ($contact->priority === 'high' ? 'warning' : ($contact->priority === 'medium' ? 'info' : 'secondary')) }}">
                                            {{ ucfirst($contact->priority) }}
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Read Status:</strong>
                                        <span class="badge badge-{{ $contact->is_read ? 'success' : 'warning' }}">
                                            {{ $contact->is_read ? 'Read' : 'Unread' }}
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Created:</strong>
                                        <span>{{ $contact->created_at->format('M d, Y H:i') }}</span>
                                        <small class="text-muted d-block">{{ $contact->created_at->diffForHumans() }}</small>
                                    </div>
                                    <div class="info-item">
                                        <strong>Updated:</strong>
                                        <span>{{ $contact->updated_at->format('M d, Y H:i') }}</span>
                                        <small class="text-muted d-block">{{ $contact->updated_at->diffForHumans() }}</small>
                                    </div>
                                    @if($contact->replied_at)
                                    <div class="info-item">
                                        <strong>Replied:</strong>
                                        <span>{{ $contact->replied_at->format('M d, Y H:i') }}</span>
                                        <small class="text-muted d-block">{{ $contact->replied_at->diffForHumans() }}</small>
                                    </div>
                                    @endif
                                    <div class="info-item">
                                        <strong>Contact ID:</strong>
                                        <span>#{{ $contact->id }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Technical Information -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">Technical Information</h4>
                                </div>
                                <div class="card-body">
                                    <div class="info-item">
                                        <strong>IP Address:</strong>
                                        <span>{{ $contact->ip_address ?: 'Not recorded' }}</span>
                                    </div>
                                    @if($contact->user_agent)
                                    <div class="info-item">
                                        <strong>User Agent:</strong>
                                        <span class="user-agent" title="{{ $contact->user_agent }}">
                                            {{ Str::limit($contact->user_agent, 50) }}
                                        </span>
                                        @if(strlen($contact->user_agent) > 50)
                                            <button type="button" class="btn btn-link btn-sm p-0" data-toggle="modal" data-target="#userAgentModal">
                                                <i class="fas fa-expand-alt"></i>
                                            </button>
                                        @endif
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Contact Statistics -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4 class="card-title">Statistics</h4>
                                </div>
                                <div class="card-body">
                                    <div class="info-item">
                                        <strong>Message Length:</strong>
                                        <span>{{ strlen($contact->message) }} characters</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>Word Count:</strong>
                                        <span>{{ str_word_count($contact->message) }} words</span>
                                    </div>
                                    @if($contact->admin_notes)
                                    <div class="info-item">
                                        <strong>Has Admin Notes:</strong>
                                        <span class="badge badge-info">Yes</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this contact?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- User Agent Modal -->
@if($contact->user_agent && strlen($contact->user_agent) > 50)
<div class="modal fade" id="userAgentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Agent Information</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Full User Agent String:</label>
                    <textarea class="form-control" rows="4" readonly>{{ $contact->user_agent }}</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
.info-group {
    margin-bottom: 1.5rem;
}

.info-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.info-value {
    color: #6c757d;
    margin-bottom: 0;
    word-wrap: break-word;
}

.info-item {
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: #495057;
    display: inline-block;
    min-width: 100px;
}

.info-item span {
    color: #6c757d;
    word-wrap: break-word;
}

.message-content {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    border-left: 4px solid #2678a1db;
    margin-top: 0.5rem;
    line-height: 1.6;
}

.admin-notes {
    background-color: #fff3cd;
    padding: 1rem;
    border-radius: 0.25rem;
    border-left: 4px solid #ffc107;
    line-height: 1.6;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
}

.btn-group-vertical .btn {
    border-radius: 0.25rem !important;
}

.user-agent {
    font-family: monospace;
    font-size: 0.875rem;
}

.dropdown-menu {
    min-width: 100%;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
}
</style>
@endpush

@push('scripts')
<script>
// Mark as read
function markAsRead(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-read`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as read');
            }
        },
        error: function() {
            alert('Error marking contact as read');
        }
    });
}

// Mark as unread
function markAsUnread(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-unread`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as unread');
            }
        },
        error: function() {
            alert('Error marking contact as unread');
        }
    });
}

// Change status
function changeStatus(contactId, status) {
    $.ajax({
        url: `/admin/contacts/${contactId}/status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            status: status
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating contact status');
            }
        },
        error: function() {
            alert('Error updating contact status');
        }
    });
}

// Delete contact
function deleteContact(contactId) {
    $('#deleteForm').attr('action', `/admin/contacts/${contactId}`);
    $('#deleteModal').modal('show');
}

// Auto-mark as read when viewing
@if(!$contact->is_read)
$(document).ready(function() {
    // Mark as read after 3 seconds of viewing
    setTimeout(function() {
        markAsRead({{ $contact->id }});
    }, 3000);
});
@endif
</script>
@endpush
