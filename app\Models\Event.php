<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'short_description',
        'event_date',
        'location',
        'image',
        'price',
        'max_participants',
        'is_active',
        'status',
        'contact_info'
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'contact_info' => 'array',
        'is_active' => 'boolean',
        'price' => 'decimal:2'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('status', 'upcoming')
                    ->where('event_date', '>', now());
    }

    public function getFormattedDateAttribute()
    {
        return $this->event_date->format('M d, Y');
    }

    public function getFormattedTimeAttribute()
    {
        return $this->event_date->format('h:i A');
    }
}
