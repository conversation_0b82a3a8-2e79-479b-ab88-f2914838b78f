@extends('layouts.admin')

@section('title', 'Newsletter Subscriber Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Newsletter Subscriber Details</h3>
                    <div>
                        <a href="{{ route('admin.newsletters.edit', $newsletter) }}" class="btn btn-warning mr-2">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.newsletters.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <!-- Subscriber Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user"></i> Subscriber Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2 text-center">
                                            <div class="subscriber-avatar mb-3">
                                                {{ strtoupper(substr($newsletter->email, 0, 1)) }}
                                            </div>
                                        </div>
                                        <div class="col-md-10">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Name:</strong> {{ $newsletter->name ?: 'Not provided' }}</p>
                                                    <p><strong>Email:</strong>
                                                        <a href="mailto:{{ $newsletter->email }}">{{ $newsletter->email }}</a>
                                                        @if($newsletter->is_verified)
                                                            <span class="badge badge-success ml-2">
                                                                <i class="fas fa-check"></i> Verified
                                                            </span>
                                                        @else
                                                            <span class="badge badge-warning ml-2">
                                                                <i class="fas fa-clock"></i> Unverified
                                                            </span>
                                                        @endif
                                                    </p>
                                                    <p><strong>Phone:</strong> {{ $newsletter->phone ?: 'Not provided' }}</p>
                                                    <p><strong>Location:</strong> {{ $newsletter->location ?: 'Not provided' }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Status:</strong>
                                                        <span class="badge badge-{{ $newsletter->status === 'active' ? 'success' : ($newsletter->status === 'inactive' ? 'warning' : 'danger') }}">
                                                            {{ ucfirst($newsletter->status) }}
                                                        </span>
                                                    </p>
                                                    <p><strong>Source:</strong> {{ ucwords(str_replace('_', ' ', $newsletter->source)) }}</p>
                                                    <p><strong>Subscribed:</strong> {{ $newsletter->created_at->format('M d, Y H:i') }}</p>
                                                    <p><strong>Last Updated:</strong> {{ $newsletter->updated_at->format('M d, Y H:i') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($newsletter->preferences)
                                        @php
                                            $preferences = json_decode($newsletter->preferences, true) ?? [];
                                        @endphp
                                        @if(count($preferences) > 0)
                                            <hr>
                                            <div class="row">
                                                <div class="col-12">
                                                    <h6><strong>Newsletter Preferences:</strong></h6>
                                                    <div class="row">
                                                        @foreach($preferences as $preference)
                                                            <div class="col-md-4 mb-2">
                                                                <span class="badge badge-info">
                                                                    <i class="fas fa-check"></i> {{ ucwords(str_replace('_', ' ', $preference)) }}
                                                                </span>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endif

                                    @if($newsletter->notes)
                                        <hr>
                                        <div class="row">
                                            <div class="col-12">
                                                <h6><strong>Admin Notes:</strong></h6>
                                                <div class="alert alert-info">
                                                    {{ $newsletter->notes }}
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Activity & Statistics -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line"></i> Activity & Statistics
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-info"><i class="fas fa-envelope-open"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Email Opens</span>
                                                    <span class="info-box-number">{{ $newsletter->email_opens ?? 0 }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-success"><i class="fas fa-mouse-pointer"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Link Clicks</span>
                                                    <span class="info-box-number">{{ $newsletter->link_clicks ?? 0 }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-warning"><i class="fas fa-calendar"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Days Subscribed</span>
                                                    <span class="info-box-number">{{ $newsletter->created_at->diffInDays(now()) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-primary"><i class="fas fa-percentage"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Open Rate</span>
                                                    <span class="info-box-number">
                                                        @if(($newsletter->emails_sent ?? 0) > 0)
                                                            {{ round((($newsletter->email_opens ?? 0) / $newsletter->emails_sent) * 100, 1) }}%
                                                        @else
                                                            0%
                                                        @endif
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <p><strong>Last Activity:</strong>
                                                @if($newsletter->last_activity_at)
                                                    {{ $newsletter->last_activity_at->format('M d, Y H:i') }}
                                                    <small class="text-muted">({{ $newsletter->last_activity_at->diffForHumans() }})</small>
                                                @else
                                                    <span class="text-muted">No activity recorded</span>
                                                @endif
                                            </p>
                                            <p><strong>Emails Sent:</strong> {{ $newsletter->emails_sent ?? 0 }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Last Email Opened:</strong>
                                                @if($newsletter->last_email_opened_at)
                                                    {{ $newsletter->last_email_opened_at->format('M d, Y H:i') }}
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </p>
                                            <p><strong>Bounce Count:</strong> {{ $newsletter->bounce_count ?? 0 }}</p>
                                        </div>
                                    </div>

                                    @if($newsletter->unsubscribed_at)
                                        <div class="alert alert-warning mt-3">
                                            <h6><i class="fas fa-exclamation-triangle"></i> Unsubscribed</h6>
                                            <p><strong>Date:</strong> {{ $newsletter->unsubscribed_at->format('M d, Y H:i') }}</p>
                                            @if($newsletter->unsubscribe_reason)
                                                <p><strong>Reason:</strong> {{ $newsletter->unsubscribe_reason }}</p>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Technical Information -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-cog"></i> Technical Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>IP Address:</strong> {{ $newsletter->ip_address ?: 'Not recorded' }}</p>
                                            <p><strong>Subscriber ID:</strong> {{ $newsletter->id }}</p>
                                            <p><strong>Email Verification Token:</strong>
                                                @if($newsletter->verification_token)
                                                    <code>{{ substr($newsletter->verification_token, 0, 20) }}...</code>
                                                @else
                                                    <span class="text-muted">Not available</span>
                                                @endif
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>User Agent:</strong>
                                                @if($newsletter->user_agent)
                                                    <button type="button" class="btn btn-sm btn-outline-info" data-toggle="modal" data-target="#userAgentModal">
                                                        View Details
                                                    </button>
                                                @else
                                                    <span class="text-muted">Not recorded</span>
                                                @endif
                                            </p>
                                            <p><strong>Created:</strong> {{ $newsletter->created_at->format('M d, Y H:i:s') }}</p>
                                            <p><strong>Updated:</strong> {{ $newsletter->updated_at->format('M d, Y H:i:s') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group-vertical w-100">
                                        <a href="{{ route('admin.newsletters.edit', $newsletter) }}" class="btn btn-warning mb-2">
                                            <i class="fas fa-edit"></i> Edit Subscriber
                                        </a>
                                        <button type="button" class="btn btn-{{ $newsletter->status === 'active' ? 'warning' : 'success' }} mb-2"
                                                onclick="toggleStatus({{ $newsletter->id }})">
                                            <i class="fas fa-{{ $newsletter->status === 'active' ? 'pause' : 'play' }}"></i>
                                            {{ $newsletter->status === 'active' ? 'Deactivate' : 'Activate' }}
                                        </button>
                                        @if(!$newsletter->is_verified)
                                            <button type="button" class="btn btn-success mb-2" onclick="verifySubscriber({{ $newsletter->id }})">
                                                <i class="fas fa-check"></i> Verify Email
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-info mb-2" onclick="sendTestEmail({{ $newsletter->id }})">
                                            <i class="fas fa-envelope"></i> Send Test Email
                                        </button>
                                        <button type="button" class="btn btn-primary mb-2" onclick="exportSubscriber({{ $newsletter->id }})">
                                            <i class="fas fa-download"></i> Export Data
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteNewsletter({{ $newsletter->id }})">
                                            <i class="fas fa-trash"></i> Delete Subscriber
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Subscription Summary -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Subscription Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-3">
                                        <div class="subscriber-avatar-large">
                                            {{ strtoupper(substr($newsletter->email, 0, 1)) }}
                                        </div>
                                        <h6 class="mt-2">{{ $newsletter->name ?: 'Anonymous Subscriber' }}</h6>
                                        <p class="text-muted">{{ $newsletter->email }}</p>
                                    </div>

                                    <div class="row text-center">
                                        <div class="col-6">
                                            <span class="badge badge-{{ $newsletter->status === 'active' ? 'success' : ($newsletter->status === 'inactive' ? 'warning' : 'danger') }} p-2">
                                                {{ ucfirst($newsletter->status) }}
                                            </span>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge badge-{{ $newsletter->is_verified ? 'success' : 'warning' }} p-2">
                                                {{ $newsletter->is_verified ? 'Verified' : 'Unverified' }}
                                            </span>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="small">
                                        <p><i class="fas fa-calendar"></i> Subscribed: {{ $newsletter->created_at->format('M d, Y') }}</p>
                                        <p><i class="fas fa-clock"></i> {{ $newsletter->created_at->diffForHumans() }}</p>
                                        <p><i class="fas fa-source"></i> Source: {{ ucwords(str_replace('_', ' ', $newsletter->source)) }}</p>
                                        @if($newsletter->location)
                                            <p><i class="fas fa-map-marker-alt"></i> {{ $newsletter->location }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">Subscribed</h6>
                                                <p class="timeline-text">{{ $newsletter->created_at->format('M d, Y H:i') }}</p>
                                            </div>
                                        </div>

                                        @if($newsletter->is_verified)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-success"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Email Verified</h6>
                                                    <p class="timeline-text">{{ $newsletter->email_verified_at ? $newsletter->email_verified_at->format('M d, Y H:i') : 'Verified' }}</p>
                                                </div>
                                            </div>
                                        @endif

                                        @if($newsletter->last_activity_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-info"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Last Activity</h6>
                                                    <p class="timeline-text">{{ $newsletter->last_activity_at->format('M d, Y H:i') }}</p>
                                                </div>
                                            </div>
                                        @endif

                                        @if($newsletter->unsubscribed_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-danger"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Unsubscribed</h6>
                                                    <p class="timeline-text">{{ $newsletter->unsubscribed_at->format('M d, Y H:i') }}</p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Agent Modal -->
@if($newsletter->user_agent)
<div class="modal fade" id="userAgentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Agent Information</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Full User Agent String:</label>
                    <textarea class="form-control" rows="4" readonly>{{ $newsletter->user_agent }}</textarea>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Browser:</strong> <span id="browserInfo">Analyzing...</span></p>
                        <p><strong>Operating System:</strong> <span id="osInfo">Analyzing...</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Device Type:</strong> <span id="deviceInfo">Analyzing...</span></p>
                        <p><strong>Mobile:</strong> <span id="mobileInfo">Analyzing...</span></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this newsletter subscription?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone. All subscriber data and activity history will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Permanently</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.subscriber-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #2678a1db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    margin: 0 auto;
}

.subscriber-avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #2678a1db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
    margin: 0 auto;
}

.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 1.2rem;
    margin-right: 1rem;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-box-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #2678a1db;
}

.timeline-title {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.timeline-text {
    margin: 0;
    font-size: 0.8rem;
    color: #6c757d;
}

.btn-group-vertical .btn {
    margin-bottom: 0.5rem;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
</style>
@endpush

@push('scripts')
<script>
// Auto-mark as read after 3 seconds
setTimeout(function() {
    // This would typically update a 'viewed' status
    console.log('Newsletter subscriber viewed');
}, 3000);

// Toggle newsletter status
function toggleStatus(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating newsletter status');
            }
        },
        error: function() {
            alert('Error updating newsletter status');
        }
    });
}

// Verify subscriber
function verifySubscriber(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/verify`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error verifying subscriber');
            }
        },
        error: function() {
            alert('Error verifying subscriber');
        }
    });
}

// Send test email
function sendTestEmail(newsletterId) {
    if (confirm('Send a test email to this subscriber?')) {
        $.ajax({
            url: `/admin/newsletters/${newsletterId}/send-test`,
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Test email sent successfully!');
                } else {
                    alert('Error sending test email');
                }
            },
            error: function() {
                alert('Error sending test email');
            }
        });
    }
}

// Export subscriber data
function exportSubscriber(newsletterId) {
    window.open(`/admin/newsletters/${newsletterId}/export`, '_blank');
}

// Delete newsletter
function deleteNewsletter(newsletterId) {
    $('#deleteForm').attr('action', `/admin/newsletters/${newsletterId}`);
    $('#deleteModal').modal('show');
}

// Parse user agent information
@if($newsletter->user_agent)
$('#userAgentModal').on('shown.bs.modal', function() {
    const userAgent = '{{ addslashes($newsletter->user_agent) }}';

    // Simple user agent parsing (in a real app, you'd use a proper library)
    let browser = 'Unknown';
    let os = 'Unknown';
    let device = 'Desktop';
    let mobile = 'No';

    // Browser detection
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';
    else if (userAgent.includes('Opera')) browser = 'Opera';

    // OS detection
    if (userAgent.includes('Windows')) os = 'Windows';
    else if (userAgent.includes('Mac')) os = 'macOS';
    else if (userAgent.includes('Linux')) os = 'Linux';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iOS')) os = 'iOS';

    // Mobile detection
    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
        mobile = 'Yes';
        device = 'Mobile';
    } else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) {
        device = 'Tablet';
    }

    $('#browserInfo').text(browser);
    $('#osInfo').text(os);
    $('#deviceInfo').text(device);
    $('#mobileInfo').text(mobile);
});
@endif
</script>
@endpush
