// Service Section
.service-itemsv1 {
	display: flex;
	align-items: center;
	border-radius: 10px;
	border: 1px solid $p200-clr;
	background: $white-clr;
	padding: 30px;
	.icons {
		width: 65px;
		height: 65px;
	}
	.content {
		border-left: 2px solid $p200-clr;
		padding-left: 20px;
		margin-left: 20px;
		.title {
			font-size: 30px;
			line-height: 40px;
			font-family: $heading-font;
			color: $p900-clr;
			font-weight: 400;
			display: block;
			margin-bottom: 8px;
			transition: all 0.4s;
		}
		p {
			color: $p800-clr;
			margin-bottom: 30px;
			line-height: 30px;
		}
		.arrows {
			display: flex;
			align-items: center;
			gap: 22px;
			font-size: 15px;
			font-style: normal;
			font-weight: 400;
			line-height: 24px;
			letter-spacing: 3px;
			transition: all 0.4s;
			i {
				color: $p1-clr;
			}
			&:hover {
				color: $p900-clr;
			}
		}
	}
	&:hover {
		.content {
			.title {
				color: $p1-clr;
			}
			.arrows {
				color: $p1-clr;
			}
		}
	}
	@include breakpoint(max-xxl) {
		padding: 20px;
		.icons {
			width: 50px;
			height: 50px;
		}
		.content {
			padding-left: 15px;
			margin-left: 15px;
			.title {
				font-size: 26px;
				line-height: 40px;
				margin-bottom: 8px;
			}
			p {
				margin-bottom: 20px;
				line-height: 30px;
			}
			.arrows {
				display: flex;
				align-items: center;
				gap: 22px;
				font-size: 15px;
				font-style: normal;
				font-weight: 400;
				line-height: 24px;
				letter-spacing: 3px;
				transition: all 0.4s;
				i {
					color: $p1-clr;
				}
				&:hover {
					color: $p900-clr;
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		padding: 20px;
		display: grid;
		.icons {
			width: 50px;
			height: 50px;
		}
		.content {
			padding-left: 0px;
			margin-left: 0px;
			padding-top: 15px;
			margin-top: 15px;
			border-left: unset;
			border-top: 1px solid $p200-clr;
			.title {
				font-size: 26px;
				line-height: 40px;
				margin-bottom: 8px;
			}
			p {
				margin-bottom: 20px;
				line-height: 30px;
			}
			.arrows {
				display: flex;
				align-items: center;
				gap: 22px;
				font-size: 15px;
				font-style: normal;
				font-weight: 400;
				line-height: 24px;
				letter-spacing: 3px;
				transition: all 0.4s;
				i {
					color: $p1-clr;
				}
				&:hover {
					color: $p900-clr;
				}
			}
		}
	}
}
//version Two
.servicev2-section {
	position: relative;
	z-index: 1;
	&::before {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 270px;
		content: "";
		background: $p900-clr;
		z-index: -1;
	}
}
.service-itemsv02 {
	border-radius: 10px;
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.4s;
	.thumb {
		overflow: hidden;
		border-radius: 0;
		.mimg {
			border-radius: 0px;
			overflow: hidden;
			transition: all 0.4s;
		}
	}
	.content {
		padding: 20px 30px 40px 40px;
		width: 100%;
		border-radius: 0px 0px 5px 5px;
		background: $p900-clr;
		.iocns-box {
			width: 100px;
			height: 100px;
			background: $p1-clr;
			border-radius: 0px 50px;
			margin-top: -70px;
			z-index: 1;
			position: relative;
			margin-bottom: 20px;
		}
		.title {
			font-size: 30px;
			font-weight: 400;
			font-family: $heading-font;
			font-weight: 400;
			line-height: 40px;
			color: $white-clr;
			margin-bottom: 10px;
			display: block;
		}
		p {
			color: $white-clr;
			margin-bottom: 30px;
		}
		.arrows {
			font-size: 15px;
			font-weight: 400;
			line-height: 24px;
			letter-spacing: 3px;
			font-family: $body-font;
			color: $white-clr;
			i {
				color: $white-clr;
				padding-left: 10px;
				font-size: 14px;
			}
		}
	}
	&:hover {
		border-color: $p2-clr;
		.mimg {
			transform: scale(1.1);
		}
		.content {
			.title {
				color: $p1-clr;
			}
		}
	}
	&.service-style03 {
		border: 1px solid $p200-clr;
		.content {
			display: flex;
			justify-content: center;
			padding: 24px 40px 30px;
			background: #f4fbf5;
			gap: 25px;
			border: unset !important;
			.title {
				margin-bottom: 0;
				color: $p900-clr;
			}
		}
		&:hover {
			border-color: $p1-clr;
		}
	}

	@include breakpoint(max-xxl) {
		&.service-style03 {
			.content {
				display: flex;
				padding: 19px 24px 22px;
				gap: 15px;
				.title {
					font-size: 26px;
					line-height: 32px;
				}
			}
		}
		.content {
			padding: 20px 10px 30px 24px;
		}
	}
	@include breakpoint(max-xl) {
		&.service-style03 {
			.content {
				display: flex;
				padding: 16px 17px 14px;
				gap: 10px;
				.title {
					font-size: 20px;
					line-height: 28px;
				}
				img {
					width: 44px;
				}
			}
		}
		.content {
			padding: 20px 10px 20px 20px;
			.iocns-box {
				width: 80px;
				height: 80px;
				margin-top: -70px;
				margin-bottom: 16px;
				img {
					width: 40px;
				}
			}
			.title {
				font-size: 21px;
				line-height: 40px;
				margin-bottom: 8px;
			}
			p {
				font-size: 14px;
				line-height: 20px;
				margin-bottom: 16px;
			}
			.arrows {
				font-size: 15px;
				font-weight: 400;
				line-height: 24px;
				letter-spacing: 3px;
				font-family: $body-font;
				color: $white-clr;
				i {
					color: $white-clr;
					padding-left: 10px;
					font-size: 14px;
				}
			}
		}
	}
}
// Service Details Section

// Feature Section
.feature-itemsv1 {
	display: flex;
	align-items: center;
	gap: 20px;
	padding: 30px 30px 30px 0;
	background: $white-clr;
	position: relative;
	z-index: 1;
	&::before {
		position: absolute;
		right: 0;
		top: 0;
		width: 80%;
		height: 100%;
		content: "";
		border: 1px solid $p200-clr;
		z-index: -1;
		border-radius: 10px;
	}
	img {
		border-radius: 10px;
	}
	.content {
		.title {
			font-size: 20px;
			font-weight: 400;
			line-height: 30px;
			color: $p900-clr;
			font-family: $heading-font;
			margin-bottom: 10px;
			display: block;
		}
		p {
			font-size: 14px;
			font-weight: 400;
			line-height: 160%;
			color: $p800-clr;
			margin-bottom: 10px;
		}
		h5 {
			color: $p1-clr;
			font-size: 20px;
			font-family: $heading-font;
			margin-bottom: 15px;
		}
		.add-tocart {
			border-radius: 100px;
			border: 1px solid $p200-clr;
			font-size: 15px;
			font-weight: 400;
			line-height: 24px;
			letter-spacing: 3px;
			padding: 10px 18px;
			font-family: $body-font;
			color: $p900-clr;
			display: inline-block;
			transition: all 0.4s;
			&:hover {
				background: $p1-clr;
				color: $white-clr;
			}
		}
	}
	&:hover {
		.content {
			.title {
				color: $p1-clr;
			}
			.add-tocart {
				background: $p1-clr;
				color: $white-clr;
			}
		}
	}
	@include breakpoint(max-xxl) {
		gap: 18px;
		padding: 20px 15px 25px 0;
		.f-thumb {
			width: 42%;
		}
		.content {
			.title {
				margin-bottom: 8px;
			}
			h5 {
				margin-bottom: 14px;
			}
			.add-tocart {
				font-size: 12px;
				line-height: 22px;
				letter-spacing: 1.2px;
				padding: 10px 14px;
			}
		}
	}
}
// Feature Section

// Who We Are Section
.who-section {
	position: relative;
	z-index: 1;
	.who-element {
		position: absolute;
		left: 0;
		z-index: -1;
		bottom: 60px;
	}
}
.who-box {
	background: $box-clr;
	padding: 50px 15px;
	text-align: center;
	border-radius: 20px;
	width: 100%;
	.icon {
		width: 65px;
		height: 65px;
		background: $p2-clr;
		border-radius: 20px;
		margin: 0 auto 30px;
		img {
			width: 40px;
			height: 40px;
		}
	}
	.title {
		font-size: 30px;
		font-weight: 400;
		font-family: $heading-font;
		color: $white-clr;
		line-height: 40px;
		transition: all 0.4s;
	}
	&:hover {
		.title {
			color: $p1-clr;
		}
	}
	@include breakpoint(max-xl) {
		padding: 32px 15px;
		.icon {
			margin: 0 auto 20px;
		}
		.title {
			font-size: 22px;
			line-height: 30px;
		}
	}
}
.who-contact-wrap {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 20px;
	padding: 60px;
	h3 {
		margin-bottom: 20px;
	}
	input,
	textarea {
		width: 100%;
		padding: 14px 20px;
		border-radius: 20px;
		background: $p900-clr;
		color: $white-clr;
		border: unset;
		outline: none;
		font-size: 14px;
	}
	button {
		width: 100%;
		padding: 18px 16px;
		text-align: center;
	}
	::placeholder {
		color: $white-clr;
	}
	.nice-select {
		border-radius: 20px;
		background: $p900-clr;
		padding: 14px 20px;
		.current {
			color: $white-clr;
			font-size: 14px;
		}
		.list {
			right: 0;
			background: $p900-clr;
			padding: 10px;
			li {
				border: unset;
				background: $p900-clr;
			}
		}
		&::after {
			right: 16px;
			top: 25px;
			border-color: $p1-clr;
		}
	}
	@include breakpoint(max-xl) {
		padding: 30px;
	}
	@include breakpoint(max-sm) {
		padding: 24px;
	}
}
// Who We Are Section

// Working v01 Section
.Working-section {
	position: relative;
	overflow: hidden;
	z-index: 1;
	.working-slilli {
		position: absolute;
		top: 0;
		left: 60px;
		z-index: -1;
		animation: updown 2s linear infinite;
	}
	.processs-text {
		display: flex;
		align-items: center;
		gap: 10px;
		justify-content: center;
		flex-wrap: wrap;
		a {
			display: flex;
			align-items: center;
			gap: 10px;
			color: $p1-clr;
		}
	}
	.working-arrows-one {
		position: absolute;
		top: -50px;
		left: 50%;
		transform: translateX(-50%);
		width: 129px;
		object-fit: contain;
	}
	.working-arrows-two {
		position: absolute;
		bottom: -50px;
		left: 250px;
		width: 129px;
		object-fit: contain;
	}
	.working-arrows-three {
		position: absolute;
		bottom: -50px;
		right: 250px;
		width: 129px;
		object-fit: contain;
	}
	.working-green {
		position: absolute;
		bottom: 0;
		left: 0;
	}
}
.working-common-head {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	margin-bottom: 115px;
	.section-title {
		h2 {
			color: $white-clr;
		}
	}
	.working-pra {
		color: $white-clr;
		max-width: 500px;
		font-size: 16px;
	}
	@include breakpoint(max-lg) {
		display: grid;
		justify-content: center;
		text-align: center;
		gap: 16px;
		margin-bottom: 85px;
		.working-pra {
			font-size: 14px;
		}
	}
}
.working-proces-items01 {
	border-radius: 10px;
	background: rgba(255, 255, 255, 0.05);
	padding: 24px 30px 30px;
	position: relative;
	z-index: 1;
	overflow: hidden;
	.step-working {
		position: absolute;
		top: 0;
		right: 0;
		z-index: -1;
		transition: all 0.5s;
	}
	.step-text {
		font-size: 20px;
		font-weight: 400;
		line-height: 30px;
		font-family: $heading-font;
		color: $p900-clr;
		position: absolute;
		right: 26px;
		top: 22px;
	}
	h3 {
		font-size: 30px;
		font-weight: 400;
		font-family: $heading-font;
	}
	&:hover {
		.step-working {
			transform: scale(1.08);
		}
	}
	@include breakpoint(max-xl) {
		padding: 14px 14px 16px;
		.step-working {
			width: 60px;
		}
		.step-text {
			font-size: 18px;
			right: 16px;
			top: 10px;
		}
		h3 {
			font-size: 24px;
			font-weight: 400;
		}
	}
}
// Working v01 Section

// Feature v02 Section
.feature-sectionv02 {
	position: relative;
	overflow: hidden;
	z-index: 1;
	.feature-element2 {
		position: absolute;
		left: 0;
		bottom: 107px;
		z-index: -1;
	}
	&::before {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 107px;
		content: "";
		z-index: -1;
		background: $white-clr;
	}
	@include breakpoint(max-xxl) {
		.about-contentv1 {
			h2 {
				font-size: 40px;
			}
			P {
				font-size: 15px;
			}
		}
	}
	@include breakpoint(max-xl) {
		padding-bottom: 70px;
		.feature-element2 {
			bottom: 0;
		}
		&::before {
			display: none;
		}
		.about-contentv1 {
			h2 {
				font-size: 40px;
			}
			P {
				font-size: 15px;
			}
		}
	}
	@include breakpoint(max-md) {
		.about-contentv1 {
			h2 {
				font-size: 36px;
			}
		}
	}
	@include breakpoint(max-sm) {
		.about-contentv1 {
			h2 {
				font-size: 32px;
			}
		}
	}
}
// Feature v02 Section

// Feature Details Section
.servicevm-section {
	.service-itemsv02 {
		border-radius: 20px;
		.thumb {
			border-radius: 20px;
			img {
				border-radius: 20px;
			}
		}
	}
}
.service-details-left {
	.service-bambo-box {
		background: url(../img/service/service-bambo.png) no-repeat center center;
		background-size: cover;
		position: relative;
		z-index: 1;
		border-radius: 20px;
		overflow: hidden;
		padding: 50px 20px;
		text-align: center;
		&::before {
			position: absolute;
			width: 100%;
			height: 100%;
			content: "";
			top: 0;
			left: 0;
			z-index: -1;
			background: rgba(253, 214, 31, 0.5);
		}
		h3 {
			font-size: 30px;
			font-weight: 400;
			line-height: 40px;
			color: $white-clr;
		}
		.fast {
			color: $white-clr;
			font-size: 16px;
			font-weight: 400;
			line-height: 30px;
			font-family: $body-font;
		}
		.call {
			font-family: "Noto Serif";
			font-size: 36px;
			font-weight: 400;
			line-height: 36px;
			color: $white-clr;
			display: grid;
			gap: 30px;
			justify-content: center;
			margin-top: 27px;
			.call-icon {
				width: 90px;
				height: 90px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: $p1-clr;
				margin: 0 auto;
				i {
					font-size: 30px;
				}
			}
		}
		@include breakpoint(max-xl) {
			padding: 30px 20px;
			h3 {
				font-size: 26px;
				font-weight: 400;
				line-height: 40px;
			}
			.call {
				font-size: 24px;
				line-height: 36px;
				display: grid;
				gap: 20px;
				margin-top: 27px;
				.call-icon {
					width: 60px;
					height: 60px;
					margin: 0 auto;
					i {
						font-size: 20px;
					}
				}
			}
		}
	}
	.faq {
		border-radius: 20px;
		border: 1px solid $p200-clr;
		background: rgba(42, 185, 57, 0.05);
		padding: 40px 40px;
		.accordion-single {
			padding: 0 0;
			background: transparent;
			box-shadow: none;
			padding-bottom: 16px;
			border-radius: 0;
			border-bottom: 1px solid $p200-clr;
			&.active {
				.header-area {
					button::after {
						background: $white-clr !important;
						color: $p1-clr;
						transform: rotate(30deg);
						transition: all 0.5s;
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: "Font Awesome 5 Free";
						content: "\2b" !important;
						font-weight: 900;
						width: 40px;
						min-width: 40px;
						height: 40px;
						background: $p100-clr;
						color: $p1-clr;
					}
				}
			}

			.header-area {
				button::after {
					background: $white-clr;
				}
				.accordion-btn {
					font-size: 20px;
					font-weight: 500;
					line-height: 30px;
					color: $p900-clr;
				}
			}
			.content-area {
				padding: 6px;
				.content-body {
					p {
						color: $p800-clr;
					}
				}
			}
		}
		@include breakpoint(max-xl) {
			padding: 20px 20px;
			.accordion-single {
				.header-area {
					.accordion-btn {
						font-size: 16px;
						font-weight: 500;
						line-height: 30px;
						color: $p900-clr;
					}
				}
			}
		}
	}
}
.service-wrapper-right {
	.thumb {
		border-radius: 20px;
		width: 100%;
		img {
			width: 100%;
			border-radius: 20px;
		}
	}
	.cont-box {
		h3 {
			color: $p900-clr;
			margin-bottom: 22px;
			font-weight: 400;
			font-size: 36px;
		}
		P {
			color: $p800-clr;
			font-size: 16px;
		}
	}
	.working-process-details {
		margin: 40px 0;
		display: flex;
		gap: 140px;
		.working-proces-items01 {
			background: $p100-clr;
			width: 300px;
			h3 {
				color: $p900-clr !important;
			}
			.details-iconbox {
				border-radius: 39px 4.307px 39px 0px;
				background: $p2-clr;
				padding: 17px;
				position: absolute;
				right: 0;
				top: 0;
			}
		}
	}
	.service-single-thumb {
		display: flex;
		gap: 30px;
		width: 100%;
		img {
			border-radius: 20px;
			width: 100%;
		}
	}
	@include breakpoint(max-md) {
		.working-process-details {
			margin: 24px 0;
			display: flex;
			flex-wrap: wrap;
			gap: 10px 22px;
			.working-proces-items01 {
				background: $p100-clr;
				width: 300px;
				padding: 30px 19px;
				h3 {
					color: $p900-clr !important;
				}
				.details-iconbox {
					padding: 17px;
					img{
						width: 32px;
					}
				}
			}
		}
		.service-single-thumb {
			gap: 10px;
		}
		.cont-box {
			h3 {
				margin-bottom: 16px;
				font-size: 24px;
			}
			P {
				color: $p800-clr;
				font-size: 15px;
			}
		}
	}
}
// Feature Details Section
