@extends('layouts.admin')

@section('title', 'Gallery Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Gallery</h3>
                    <div>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#bulkUploadModal">
                            <i class="fas fa-upload me-2"></i>Bulk Upload
                        </button>
                        <a href="{{ route('admin.gallery.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Item
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.gallery.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" placeholder="Search gallery..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="type" class="form-control">
                                    <option value="">All Types</option>
                                    <option value="image" {{ request('type') == 'image' ? 'selected' : '' }}>Images</option>
                                    <option value="video" {{ request('type') == 'video' ? 'selected' : '' }}>Videos</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="category" class="form-control">
                                    <option value="">All Categories</option>
                                    @if(isset($categories))
                                        @foreach($categories as $category)
                                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                                {{ $category }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="featured" class="form-control">
                                    <option value="">All Items</option>
                                    <option value="1" {{ request('featured') == '1' ? 'selected' : '' }}>Featured</option>
                                    <option value="0" {{ request('featured') == '0' ? 'selected' : '' }}>Not Featured</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.gallery.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Gallery Grid -->
                    @if(isset($gallery) && $gallery->count() > 0)
                        <div class="row">
                            @foreach($gallery as $item)
                                <div class="col-md-3 mb-4">
                                    <div class="card h-100">
                                        <div class="position-relative">
                                            @if($item->is_image)
                                                <img src="{{ $item->thumbnail_url }}" 
                                                     class="card-img-top" 
                                                     alt="{{ $item->alt_text }}"
                                                     style="height: 200px; object-fit: cover;">
                                            @else
                                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                                    <i class="fas fa-play-circle fa-3x text-primary"></i>
                                                </div>
                                            @endif
                                            
                                            <!-- Badges -->
                                            <div class="position-absolute top-0 start-0 p-2">
                                                <span class="badge {{ $item->type_badge_class }}">{{ $item->type_label }}</span>
                                                @if($item->is_featured)
                                                    <span class="badge bg-warning">Featured</span>
                                                @endif
                                            </div>
                                            
                                            <!-- Actions -->
                                            <div class="position-absolute top-0 end-0 p-2">
                                                <div class="btn-group-vertical" role="group">
                                                    <button class="btn btn-sm btn-outline-light" 
                                                            onclick="toggleFeatured({{ $item->id }})"
                                                            title="{{ $item->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}">
                                                        <i class="fas fa-star {{ $item->is_featured ? 'text-warning' : '' }}"></i>
                                                    </button>
                                                    <a href="{{ route('admin.gallery.edit', $item) }}" 
                                                       class="btn btn-sm btn-outline-light" 
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-light text-danger" 
                                                            onclick="deleteItem({{ $item->id }})"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="card-body">
                                            <h6 class="card-title">{{ Str::limit($item->title, 30) }}</h6>
                                            <p class="card-text small text-muted">{{ Str::limit($item->description, 60) }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">{{ $item->category }}</small>
                                                <small class="text-muted">{{ $item->formatted_file_size }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5>No gallery items found</h5>
                            <p class="text-muted">Start by uploading your first image or video.</p>
                            <a href="{{ route('admin.gallery.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Gallery Item
                            </a>
                        </div>
                    @endif

                    <!-- Pagination -->
                    @if(isset($gallery) && $gallery->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $gallery->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Upload Modal -->
<div class="modal fade" id="bulkUploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Upload</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkUploadForm" action="{{ route('admin.gallery.bulk-upload') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="files" class="form-label">Select Files</label>
                        <input type="file" class="form-control" id="files" name="files[]" multiple accept="image/*,video/*">
                        <div class="form-text">You can select multiple images and videos.</div>
                    </div>
                    <div class="mb-3">
                        <label for="bulk_category" class="form-label">Category</label>
                        <input type="text" class="form-control" id="bulk_category" name="category" placeholder="Enter category">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="bulkUploadForm" class="btn btn-primary">Upload Files</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle featured status
function toggleFeatured(itemId) {
    fetch(`/admin/gallery/${itemId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating featured status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating featured status');
    });
}

// Delete gallery item
function deleteItem(itemId) {
    if (confirm('Are you sure you want to delete this gallery item? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/gallery/${itemId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
