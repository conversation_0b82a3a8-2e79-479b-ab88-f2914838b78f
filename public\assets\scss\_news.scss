// BLog Style
.blog-stylev1 {
	position: relative;
	z-index: 1;
	.carrot-left {
		position: absolute;
		left: 70px;
		top: 100px;
		z-index: -1;
		animation: updown 2s linear infinite;
	}
	.carrot-right {
		position: absolute;
		right: 0;
		top: 0;
		z-index: -1;
	}
}
.blog-itemsv1 {
	.thumb {
		position: relative;
		overflow: hidden;
		border-radius: 10px;
		img {
			overflow: hidden;
			border-radius: 10px;
			transition: all 0.5s;
		}
		.dates {
			font-size: 20px;
			font-weight: 400;
			line-height: 30px;
			font-family: $heading-font;
			background: $p100-clr;
			border-radius: 5px 0;
			padding: 5px 20px;
			color: $p900-clr;
			position: absolute;
			top: 12px;
			left: 12px;
		}
	}
	.content {
		border-radius: 5px 0px 5px 5px;
		background: $white-clr;
		box-shadow: 0px 10px 30px 0px rgba(42, 185, 57, 0.1);
		padding: 30px;
		width: 90%;
		margin: -60px 0 0 auto;
		z-index: 1;
		position: relative;
		.comment-inner {
			margin-bottom: 20px;
		}
		.title {
			font-size: 30px;
			font-weight: 400;
			font-family: $heading-font;
			font-weight: 400;
			line-height: 40px;
			color: $p900-clr;
			margin-bottom: 10px;
			display: block;
		}
		p {
			color: $p800-clr;
			margin-bottom: 30px;
		}
		.arrows {
			font-size: 15px;
			font-weight: 400;
			line-height: 24px;
			letter-spacing: 3px;
			font-family: $body-font;
			color: $p900-clr;
		}
	}
	&:hover {
		img {
			transform: scale(1.1);
		}
		.content {
			.title {
				color: $p1-clr;
			}
		}
	}
	&.blog-itemsv2 {
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		overflow: hidden;
		.thumb {
			overflow: hidden;
			border-radius: 0;
			img {
				border-radius: 0px;
				overflow: hidden;
				transition: all 0.4s;
			}
		}
		.comment-inner {
			background: $p100-clr;
			padding: 10px 30px;
			li {
				a {
					i {
						color: $p1-clr;
					}
				}
			}
		}
		.content {
			padding: 20px 10px 30px 30px;
			width: 100%;
			margin-top: 0;
			border-radius: 0px 0px 5px 5px;
			border: 2px solid $p200-clr;
			border-top: unset;
			.arrows {
				i {
					color: $p1-clr;
				}
			}
		}
		&:hover {
			img {
				transform: scale(1.1);
			}
			.content {
				.title {
					color: $p1-clr;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		.content {
			padding: 24px;
			width: 91%;
			margin: -60px 0 0 auto;
			.comment-inner {
				margin-bottom: 12px;
			}
			.title {
				font-size: 25px;
				line-height: 35px;
				margin-bottom: 10px;
			}
			p {
				color: $p800-clr;
				margin-bottom: 20px;
			}
			.arrows {
				font-size: 15px;
				font-weight: 400;
				line-height: 24px;
				letter-spacing: 3px;
				font-family: $body-font;
				color: $p900-clr;
			}
		}
		&.blog-itemsv2 {
			.comment-inner {
				padding: 10px 20px;
				gap: 7px 14px;
				flex-wrap: wrap;
				li {
					a {
						font-size: 13px;
					}
				}
			}
			.content {
				padding: 17px 8px 20px 20px;
				.title {
					font-size: 26px;
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		.content {
			padding: 24px;
			width: 100%;
			margin: 0px 0 0 auto;
			.comment-inner {
				margin-bottom: 12px;
			}
			.title {
				font-size: 25px;
				line-height: 35px;
				margin-bottom: 10px;
			}
			p {
				color: $p800-clr;
				margin-bottom: 20px;
			}
			.arrows {
				font-size: 15px;
				font-weight: 400;
				line-height: 24px;
				letter-spacing: 3px;
				font-family: $body-font;
				color: $p900-clr;
			}
		}
		&.blog-itemsv2 {
			.comment-inner {
				padding: 10px 20px;
				gap: 7px 14px;
				flex-wrap: wrap;
				li {
					a {
						font-size: 13px;
					}
				}
			}
			.content {
				padding: 17px 8px 20px 20px;
				.title {
					font-size: 20px;
					line-height: 26px;
				}
			}
		}
	}
}
.comment-inner {
	display: flex;
	align-items: center;
	gap: 20px;
	li {
		a {
			display: flex;
			align-items: center;
			font-size: 14px;
			font-weight: 400;
			line-height: 160%;
			gap: 5px;
			color: $p800-clr;
			i {
				color: $p900-clr;
			}
		}
	}
}
// BLog Details Style
.blog-right-bar {
	display: grid;
	gap: 40px;
	.common-style-box {
		border-radius: 20px;
		background: $p100-clr;
		padding: 38px 40px 40px;
	}
	.box {
		.wid-title {
			h3 {
				font-size: 30px;
				font-weight: 400;
				line-height: 40px;
				color: $p900-clr;
				margin-bottom: 20px;
			}
		}
		.search-widget {
			form {
				width: 100%;
				position: relative;
				display: flex;
				align-items: center;
				background: $white-clr;
				padding: 0px 20px;
				border-radius: 100px;
				input {
					background-color: $white-clr;
					font-size: 14px;
					padding: 14px 0px;
					width: 100%;
					border: none;
					color: $p800-clr;
					background: transparent;
				}
				button {
					outline: none;
					border: unset;
					color: $p1-clr;
				}
				::placeholder {
					color: $p800-clr;
				}
			}
		}
		.category {
			ul {
				display: grid;
				gap: 18px;
				li {
					a {
						position: relative;
						display: flex;
						align-items: center;
						gap: 20px;
						font-size: 20px;
						color: $p900-clr;
						font-family: $heading-font;
						font-weight: 400;
						line-height: 30px;
						&:hover {
							gap: 10px;
							color: $p1-clr;
						}
					}
				}
			}
		}
		.recent-postwrap {
			display: grid;
			gap: 40px;
			.recent-items {
				display: flex;
				gap: 20px;
				.recent-thumb {
					border-radius: 20px;
					img {
						border-radius: 20px;
					}
				}
				.recent-content {
					span {
						font-size: 14px;
						font-family: $body-font;
						color: $p800-clr;
					}
					i {
						color: $p1-clr;
						font-size: 13px;
					}
					a {
						margin-top: 3px;
						font-size: 20px;
						font-weight: 400;
						line-height: 30px;
						color: $p900-clr;
						font-family: $heading-font;
					}
				}
				&:hover {
					.recent-content {
						a {
							color: $p1-clr;
						}
					}
				}
			}
		}
		.tagwrap {
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			a {
				background: $white-clr;
				border-radius: 100px;
				padding: 5px 10px;
				font-size: 14px;
				font-weight: 400;
				line-height: 160%;
				font-family: $body-font;
				display: inline-block;
				&:hover {
					background: $p1-clr;
					color: $white-clr;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		gap: 30px;
		.common-style-box {
			padding: 26px 26px 26px;
		}
		.box {
			.recent-postwrap {
				display: grid;
				gap: 30px;
				.recent-items {
					display: flex;
					gap: 16px;
					.recent-content {
						span {
							font-size: 14px;
							font-family: $body-font;
							color: $p800-clr;
						}
						i {
							color: $p1-clr;
							font-size: 13px;
						}
						a {
							margin-top: 3px;
							font-size: 18px;
							font-weight: 400;
							line-height: 30px;
							color: $p900-clr;
							font-family: $heading-font;
						}
					}
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		.box {
			.recent-postwrap {
				display: grid;
				gap: 30px;
				.recent-items {
					display: flex;
					gap: 14px;
					.recent-content {
						span {
							font-size: 12px;
						}
						a {
							margin-top: 3px;
							font-size: 16px;
							line-height: 24px;
						}
					}
				}
			}
			.category {
				ul {
					display: grid;
					gap: 16px;
					li {
						a {
							position: relative;
							display: flex;
							align-items: center;
							gap: 16px;
							font-size: 17px;
							color: $p900-clr;
							font-family: $heading-font;
							font-weight: 400;
							line-height: 30px;
							&:hover {
								gap: 10px;
								color: $p1-clr;
							}
						}
					}
				}
			}
		}
	}
}
.blog-details-big {
	display: grid;
	gap: 60px;
	.blog-details-leftitem {
		background: $white-clr;
		transition: all 0.5s;
		.thumb {
			width: 100%;
			overflow: hidden;
			transition: all 0.4s;
			border-radius: 5px 5px 0 0;
			img {
				width: 100%;
				overflow: hidden;
				transition: all 0.4s;
				border-radius: 5px 5px 0 0;
			}
			.date-badge {
				border-radius: 0px 5px 0px 10px;
				background: $p1-clr;
				padding: 6px 16px;
				font-size: 16px;
				font-weight: 400;
				line-height: 30px;
				font-family: $body-font;
				color: $white-clr;
				position: absolute;
				top: 0;
				right: 0;
			}
		}
		.content {
			border-radius: 0px 0px 5px 5px;
			background: $white-clr;
			box-shadow: 0px 10px 30px 0px rgba(42, 185, 57, 0.1);
			padding: 0px 40px 60px;
			margin-top: -22px;
			.comment-inner {
				background: $p1-clr;
				padding: 10px 20px;
				display: inline-flex;
				margin-bottom: 30px;
				position: relative;
				z-index: 1;
				li {
					a {
						color: rgba(255, 255, 255, 0.8);
						i {
							color: $white-clr;
						}
					}
				}
			}
			.titles {
				font-size: 36px;
				font-weight: 400;
				line-height: 36px;
				color: $p900-clr;
				font-family: $heading-font;
				margin-bottom: 26px;
				display: block;
			}
			p {
				font-size: 16px;
				font-weight: 400;
				line-height: 30px;
				color: $p800-clr;
				margin-bottom: 40px;
			}
			.cmn-btn {
				i {
					color: $p900-clr !important;
				}
			}
		}
		&:hover {
			.thumb {
				img {
					transform: scale(1.08);
				}
			}
			.titles {
				color: $p1-clr;
			}
			.cmn-btn {
				background: $p1-clr;
				color: $white-clr;
				i {
					color: $white-clr !important;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		gap: 40px;
	}
	@include breakpoint(max-xl) {
		gap: 30px;
		.blog-details-leftitem {
			.content {
				padding: 20px 20px 24px;
				margin-top: -0px;
				.comment-inner {
					padding: 8px 16px;
					margin-bottom: 24px;
					flex-wrap: wrap;
					gap: 5px 20px;
				}
				.titles {
					font-size: 28px;
					line-height: 30px;
					margin-bottom: 16px;
				}
				p {
					font-size: 15px;
					line-height: 26px;
					margin-bottom: 25px;
				}
				.cmn-btn {
					i {
						color: $p900-clr !important;
					}
				}
			}
			&:hover {
				.thumb {
					img {
						transform: scale(1.08);
					}
				}
				.titles {
					color: $p1-clr;
				}
				.cmn-btn {
					background: $p1-clr;
					color: $white-clr;
					i {
						color: $white-clr !important;
					}
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		gap: 30px;
		.blog-details-leftitem {
			.content {
				.titles {
					font-size: 24px;
					line-height: 30px;
					margin-bottom: 16px;
				}
				p {
					font-size: 14px;
					line-height: 26px;
					margin-bottom: 25px;
				}
			}
		}
	}
}

//BLog Post Details
.blog-post-details {
	.explore-details-content {
		margin-bottom: 80px;
		h2 {
			color: $p900-clr;
			margin-bottom: 30px;
		}
		.thumb {
			img {
				border-radius: 20px;
			}
		}
		.comment-inner {
			display: flex;
			flex-wrap: wrap;
			gap: 5px 30px;
			margin-bottom: 30px;
			li {
				a {
					font-size: 14px;
					color: $p800-clr;
				}
			}
		}
		p {
			font-size: 16px;
			color: $p800-clr;
			font-family: $body-font;
		}
		.fist-pra {
			margin-bottom: 30px;
		}
		.quote-box {
			border-radius: 20px;
			border: 2px solid $p200-clr;
			padding: 40px;
			img {
				margin-bottom: 20px;
			}
			p {
				font-size: 16px;
				color: $p800-clr;
				margin-bottom: 26px;
			}
			h5 {
				font-size: 20px;
				font-weight: 400;
				line-height: 30px;
				color: $p900-clr;
			}
		}
		h3 {
			color: $p900-clr;
			font-size: 36px;
			font-weight: 400;
			margin-bottom: 26px;
		}
		.blog-single-thumb {
			display: flex;
			gap: 28px;
			.thumb {
				img {
					border-radius: 20px;
				}
			}
		}
	}
	.maybe-liking {
		h3 {
			color: $p900-clr;
		}
		.thumb {
			width: 100%;
			img {
				width: 100%;
				border-radius: 20px;
			}
		}
		p {
			font-size: 16px;
			color: $p800-clr;
		}
	}
	.who-contact-wrap {
		padding: 0 0;
		h3 {
			color: $p900-clr;
		}
		form {
			input,
			textarea {
				background: $p100-clr;
				color: $p800-clr;
			}
			::placeholder {
				color: $p800-clr;
			}
			button {
				width: initial;
				padding: 20px 42px;
			}
			.nice-select {
				background: $p100-clr;
				color: $p800-clr;
				.current {
					color: $p800-clr;
				}
				.list {
					background: $white-clr;
					li {
						background: $white-clr;
					}
				}
			}
		}
	}
	.replay-single-box {
		border-radius: 20px;
		border: 1px solid $p200-clr;
		padding: 40px;
		background: $white-clr;
		display: flex;
		gap: 20px;
		.content {
			.man-info-area {
				display: flex;
				justify-content: space-between;
			}
			width: calc(100% - 65px);
		}
		.krishana {
			border-radius: 20px;
			width: 65px;
			height: 65px;
		}
		.man-info {
			h3 {
				font-size: 30px;
				color: $p900-clr;
				font-weight: 400;
			}
			span {
				color: $p800-clr;
				font-size: 16px;
				font-family: $body-font;
			}
		}
		.replys {
			border-radius: 100px;
			border: 1px solid $p800-clr;
			padding: 8px 24px;
			font-size: 15px;
			font-weight: 400;
			line-height: 24px;
			letter-spacing: 3px;
			color: $p900-clr;
			font-family: $body-font;
			width: 133px;
			height: 44px;
		}
		p {
			font-size: 16px;
			color: $p800-clr;
			margin-bottom: 26px;
		}
		h5 {
			font-size: 20px;
			font-weight: 400;
			line-height: 30px;
			color: $p900-clr;
		}
	}
	.component-arrows {
		border-radius: 20px;
		border: 1px solid $p200-clr;
		padding: 30px;
		background: $white-clr;
		display: flex;
		justify-content: space-between;
		gap: 16px;
		.arrow-item {
			display: flex;
			align-items: center;
			gap: 20px;
			h5 {
				font-size: 20px;
				font-weight: 400;
				color: $p900-clr;
			}
			.arrows {
				width: 50px;
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid $p200-clr;
				i {
					font-size: 16px;
					color: $p900-clr;
				}
				&.active,
				&:hover {
					background: $p1-clr;
					border-color: $p1-clr;
					i {
						color: $white-clr;
					}
				}
			}
		}
		.cusline {
			height: 50px;
			width: 1px;
			background: $p1-clr;
		}
	}
	.social-tag-wrapper {
		border-top: 1px solid #e9efe5;
		border-bottom: 1px solid #e9efe5;
		padding: 24px 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 14px 30px;
		.left-tag {
			display: flex;
			align-items: center;
			gap: 28px;
			h5 {
				font-size: 20px;
				font-weight: 400;
				color: $p900-clr;
			}
			.tag-list {
				display: flex;
				gap: 12px;
				li {
					a {
						font-size: 14px;
						font-weight: 400;
						line-height: 160%;
						padding: 5px 10px;
						border: 1px solid $p200-clr;
						display: inline-block;
						color: $p800-clr;
						&:hover {
							background: $p1-clr;
							border-color: $p1-clr;
							color: $white-clr;
						}
					}
				}
			}
		}
		.social-wrapper {
			display: flex;
			gap: 10px;
			align-items: center;
			a {
				border-radius: 0;
				background: $p100-clr;
				i {
					color: $p900-clr;
					transition: all 0.4s;
				}
				svg {
					path {
						fill: $p900-clr;
						transition: all 0.4s;
					}
				}
				&:hover {
					background: $p1-clr;
					i {
						color: $white-clr;
					}
					svg {
						path {
							fill: $white-clr;
						}
					}
				}
			}
		}
	}
	@include breakpoint(max-sm) {
		.explore-details-content {
			margin-bottom: 50px;
			h2 {
				color: $p900-clr;
				margin-bottom: 16px;
			}
			.comment-inner {
				gap: 6px 30px;
				margin-bottom: 20px;
			}
			p {
				font-size: 14px;
			}
			.fist-pra {
				margin-bottom: 12px;
			}
			.quote-box {
				padding: 20px;
				img {
					margin-bottom: 16px;
					width: 44px;
				}
				p {
					font-size: 14px;
					margin-bottom: 14px;
				}
			}
			h3 {
				color: $p900-clr;
				font-size: 26px;
				font-weight: 400;
				margin-bottom: 15px;
			}
			.blog-single-thumb {
				display: flex;
				gap: 10px;
				.thumb {
					img {
						border-radius: 20px;
					}
				}
			}
		}
		.replay-single-box {
			padding: 20px;
			display: flex;
			flex-wrap: wrap;
			gap: 15px 30px;
			.content {
				.man-info-area {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;
					gap: 10px;
				}
				width: calc(100% - 65px);
			}
			.krishana {
				border-radius: 20px;
				width: 65px;
				height: 65px;
			}
			.man-info {
				h3 {
					font-size: 24px;
					font-weight: 400;
				}
				span {
					font-size: 14px;
				}
			}
			p {
				font-size: 14px;
				color: $p800-clr;
				margin-bottom: 26px;
			}
			h5 {
				font-size: 20px;
				font-weight: 400;
				line-height: 30px;
				color: $p900-clr;
			}
		}
	}
}
// BLog Style

// Product List Style
.product-list-section {
	.all-catagorys {
		display: grid;
		grid-template-columns: 47% 47%;
		gap: 24px;
		justify-content: space-between;
		@include breakpoint(max-md) {
			grid-template-columns: 49% 49%;
			gap: 20px;
		}
		@include breakpoint(max-sm) {
			grid-template-columns: 99%;
			gap: 20px;
		}
	}
}
.product-list-leftbar {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	background: $white-clr;
	padding: 30px 40px;
	.total-price {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: $p900-clr;
		font-weight: 500;
		font-family: $heading-font;
	}
	.filter-btns {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: $p900-clr;
		font-weight: 500;
		font-family: $heading-font;
		border: 1px solid $p200-clr;
		border-radius: 100px;
		padding: 4px 10px;
	}
	.title {
		margin-bottom: 15px;
	}
	h5 {
		font-size: 20px;
		font-weight: 400;
		line-height: 30px;
		color: $p900-clr;
	}
	.product-title {
		margin-bottom: 20px;
	}
	.product-cate {
		.product-list {
			display: grid;
			gap: 10px;
			li {
				a {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 14px;
					font-weight: 400;
					line-height: 160%;
					color: $p800-clr;
					font-family: $body-font;
					.name {
						font-size: 14px;
						font-weight: 400;
						line-height: 22px;
						font-family: $heading-font;
						color: $p900-clr;
					}
				}
			}
		}
	}
	.show-filter {
		font-size: 14px;
		font-weight: 400;
		line-height: 160%;
		margin-top: 30px;
		margin-bottom: 16px;
		color: $p800-clr;
		display: block;
	}
	.colo-filter {
		.color-style {
			display: grid;
			gap: 14px;
			li {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.color-item {
					display: flex;
					align-items: center;
					gap: 10px;
					font-size: 14px;
					color: $p900-clr;
					font-family: $body-font;
				}
				.clr-step {
					width: 15px;
					height: 15px;
					border-radius: 50%;
					&.v1 {
						background: #f8ef23;
					}
					&.v2 {
						background: #191919;
					}
					&.v3 {
						background: #fff;
						box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px,
							rgba(17, 17, 26, 0.1) 0px 0px 8px;
					}
					&.v4 {
						background: #1f4e3d;
					}
					&.v5 {
						background: #172df2;
					}
					&.v6 {
						background: #f72c2c;
					}
					&.v7 {
						background: #eff3ed;
					}
				}
				.step {
					width: 24px;
					height: 24px;
					border-radius: 50%;
					background: $p100-clr;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					color: $p900-clr;
					font-family: $body-font;
				}
			}
		}
	}
	.product-status {
		margin-top: 30px;
		.color-item {
			display: flex;
			align-items: center;
			gap: 5px;
			font-size: 14px;
			color: $p900-clr;
			font-family: $body-font;
		}
		.clr-step {
			width: 11px;
			height: 11px;
			&.v1 {
				background: #f8ef23;
			}
			&.v2 {
				background: #d9d9d9;
			}
		}
	}
	@include breakpoint(max-xl) {
		padding: 24px 20px;
	}
}

.pricing-range-area {
	.wrapper {
		width: 180px;
	}
	.price-input {
		width: 100%;
		display: flex;
		background: $p100-clr;
		border-radius: 100px;
		padding: 2px 15px;
		justify-content: space-between;
		margin-bottom: 15px;
		.separator {
			color: $p900-clr;
			font-size: 14px;
			font-family: $heading-font;
		}
		.field {
			display: flex;
			width: 100%;
			height: 40px;
			input {
				width: 100%;
				height: 100%;
				outline: none;
				color: $p900-clr;
				font-family: $heading-font;
				border: unset;
				outline: none;
				background: transparent;
			}
			::placeholder {
				color: $p900-clr;
			}
		}
	}
	input[type="number"]::-webkit-outer-spin-button,
	input[type="number"]::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	.price-input .separator {
		width: 130px;
		display: flex;
		font-size: 19px;
		align-items: center;
		justify-content: center;
	}
	.slider {
		height: 3px;
		position: relative;
		background: #ddd;
		border-radius: 5px;
	}
	.slider .progress {
		height: 100%;
		left: 25%;
		right: 25%;
		position: absolute;
		border-radius: 5px;
		background: $p900-clr;
	}
	.range-input {
		position: relative;
	}
	.range-input input {
		position: absolute;
		width: 100%;
		height: 5px;
		top: -4px;
		background: none;
		pointer-events: none;
		-webkit-appearance: none;
		-moz-appearance: none;
	}
	input[type="range"]::-webkit-slider-thumb {
		height: 11px;
		width: 11px;
		border-radius: 50%;
		background: $p900-clr;
		pointer-events: auto;
		-webkit-appearance: none;
		box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
	}
	input[type="range"]::-moz-range-thumb {
		height: 11px;
		width: 11px;
		border: none;
		border-radius: 50%;
		background: $p900-clr;
		pointer-events: auto;
		-moz-appearance: none;
		box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
	}
}
// Product Details Style
.product-details-section{
	.container{
		@include breakpoint(xl){
			max-width: 1500px;
			margin: 0 auto;
		}
	}
}
.product-details-leftbar {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	background: $white-clr;
	padding: 40px 40px;
	.product-categori-select {
		display: flex;
		align-items: center;
		gap: 15px;
		border-bottom: 1px solid $p200-clr;
		padding-bottom: 10px;
		margin-bottom: 20px;
		i {
			font-size: 26px;
			color: $p900-clr;
		}
		.nice-select {
			.current {
				font-size: 20px;
				font-style: normal;
				font-weight: 400;
				line-height: 30px;
				font-family: $heading-font;
				color: $p900-clr;
			}
			&::after {
				right: 0;
				border-color: $p900-clr;
			}
			.list {
				right: 0;
				background-color: $white-clr;
				li {
					color: $p900-clr;
					border: 1px solid $p200-clr;
				}
			}
		}
	}
	.accordion-single {
		padding: 0 0;
		box-shadow: none;
		background: transparent;
		.header-area {
			button {
				gap: 10px;
				font-size: 14px;
				font-weight: 500;
				line-height: 22px;
				font-family: $heading-font;
				color: $p900-clr;
				transition: all 0.4s;
				&::after {
					display: none;
				}
			}
		}
		&.active {
			.header-area {
				button {
					gap: 10px;
					color: $p1-clr;
					&::after {
						display: none;
					}
				}
			}
		}
		border-bottom: 1px solid $p200-clr;
		border-radius: 0;
		padding-bottom: 10px;
		.content-area {
			padding-top: 0;
		}
		.content-body {
			padding-top: 0;
			padding-left: 24px;
			li {
				a {
					display: flex;
					align-items: center;
					gap: 10px;
					padding: 10px 0;
					font-size: 14px;
					font-weight: 500;
					line-height: 22px;
					font-family: $heading-font;
					color: $p900-clr;
					transition: all 0.4s;
					border-bottom: 1px solid $p200-clr;
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		padding: 20px 20px;
	}
}
.latest-project-wrap {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	background: $white-clr;
	padding: 40px 40px;
	h5 {
		font-size: 20px;
		font-weight: 400;
		font-family: $heading-font;
		color: $p900-clr;
	}
	.recent-postwrap {
		display: grid;
		gap: 32px;
		.recent-items {
			display: flex;
			align-items: center;
			gap: 25px;
			img {
				border-radius: 20px;
				width: 95px;
				height: 78px;
			}
			.recent-content {
				a {
					font-size: 16px;
					display: block;
					font-size: 16px;
					font-weight: 500;
					line-height: 150%;
					letter-spacing: -0.176px;
					color: $p900-clr;
					margin-bottom: 4px;
				}
				span {
					font-size: 14px;
					font-weight: 400;
					font-family: $heading-font;
					color: $p900-clr;
				}
				P {
					font-size: 14px;
					color: $p800-clr;
					font-family: $heading-font;
					span {
						color: $p1-clr;
					}
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		padding: 20px 20px;
		.recent-postwrap {
			gap: 18px;
			.recent-items {
				gap: 10px;
			}
		}
	}
}
.product-infowrap {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	padding: 40px 40px;
	.title {
		color: $p900-clr;
		margin-bottom: 18px;
	}
	p {
		color: $p800-clr;
		font-size: 14px;
		margin-bottom: 20px;
	}
	.ratting {
		display: flex;
		gap: 5px;
		margin-bottom: 10px;
		i {
			&:not(:last-child) {
				color: $p2-clr;
			}
		}
	}
	.creview {
		margin-bottom: 15px;
		display: block;
	}
	span {
		color: $p800-clr;
	}
	.prices {
		color: $p800-clr;
		font-weight: 400;
		margin-bottom: 20px;
		span {
			color: $p1-clr;
			font-size: 32px;
		}
	}
	.quantity-wrap {
		margin-top: 20px;
		margin-bottom: 30px;
		.counter {
			border: 2px solid $p200-clr;
			border-radius: 100px;
			display: inline-flex;
			align-items: center;
			padding: 1px 20px;
			width: 110px;
		}
		.counter input {
			height: 40px;
			width: 50px;
			border: 0;
			color: #fff;
			appearance: none;
			outline: 0;
			text-align: center;
			font-size: 14px;
			color: $p800-clr;
		}
		.counter span {
			display: block;
			font-size: 18px;
			cursor: pointer;
			color: $p900-clr;
			user-select: none;
		}
	}
	@include breakpoint(max-xxl) {
		padding: 30px 30px;
	}
	@include breakpoint(max-xl) {
		padding: 20px 20px;
	}
}
.review-description {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	padding: 40px 40px;
	.tablinks {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px 30px;
		flex-wrap: wrap;
		margin-bottom: 80px;
		.nav-links {
			button {
				font-size: 20px;
				font-weight: 400;
				color: $p900-clr;
				font-family: $heading-font;
			}
			&.active {
				button {
					color: $p1-clr;
				}
			}
		}
	}
	@include breakpoint(max-xxl) {
		padding: 30px 30px;
		.tablinks{
			margin-bottom: 40px;
		}
	}
	@include breakpoint(max-xl) {
		padding: 20px 20px;
		.tablinks{
			margin-bottom: 30px;
			gap: 7px 20px;
			.nav-links {
				button {
					font-size: 16px;
					font-weight: 400;
				}
			}
		}
	}
}
.review-main-body{
	display: flex;
	gap: 74px;
	justify-content: space-between;
	form{
		display: grid;
		gap: 20px;
		.cmn-name{
			width: 60px;
			font-family: $heading-font;
			font-size: 14px;
		}
		input,
		textarea{
			width: 100px;
			border-radius: 20px;
			outline: none;
			border: unset;
			border: 1px solid $p200-clr;
			font-size: 14px;
			padding: 4px 16px;
			width: 155px;
			color: $p900-clr;
		}
		.cmn-btn{
			padding: 14px 16px;
		}
	}
	.review-start-inner{
		display: grid;
		gap: 16px;
	}
	.review-star-item{
		display: flex;
		align-items: center; 
		gap: 16px;
		.progress_bar{
			width: 280px;
			.item_bar{
				background: $p200-clr !important;
				height: 9px;
				.progress{
					height: 9px;
					background: $p1-clr !important;
				}
			}
		}
	}
	@include breakpoint(max-xl){
		gap: 20px 35px;
		.review-star-item{
			display: flex;
			align-items: center; 
			gap: 16px;
			.progress_bar{
				width: 180px;
			}
		}
	}
	@include breakpoint(max-lg){
		display: grid;
		flex-wrap: wrap;
	}
	@include breakpoint(max-xs){
		gap: 20px 35px;
		.review-star-item{
			display: flex;
			align-items: center; 
			flex-wrap: wrap;
			gap: 4px 16px;
			.progress_bar{
				width: 180px;
			}
		}
	}
}
.dess{
	color: $p800-clr;
	font-size: 14px;
}

.shop-details-wrap {
	.swiper-button-next {
		&::after {
			color: $p900-clr;
			font-size: 24px;
		}
	}
	.swiper-button-prev {
		&::after {
			color: $p900-clr;
			font-size: 24px;
		}
	}
	.mySwiper2{
		margin-bottom: 30px;
	}
}
.shop-details-bigthumb {
	border-radius: 30px;
	width: 100%;
	img {
		width: 100%;
	}
}
.shop-details-samll {
	border-radius: 20px;
	border: 1px solid $p200-clr;
	width: 64px;
	height: 64px;
	transition: all 0.4s;
	&:hover {
		cursor: pointer;
		border-color: $p1-clr;
	}
	img {
		width: 48px;
		height: 53px;
		object-fit: contain;
	}
}
// Product List Style
