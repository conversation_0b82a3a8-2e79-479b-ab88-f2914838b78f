@extends('layouts.admin')

@section('title', 'Add New Contact')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Add New Contact</h3>
                    <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Contacts
                    </a>
                </div>
                <form action="{{ route('admin.contacts.store') }}" method="POST" id="contactForm">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- Contact Information -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Contact Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name">Full Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                           id="name" name="name" value="{{ old('name') }}" required>
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                           id="email" name="email" value="{{ old('email') }}" required>
                                                    @error('email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="phone">Phone Number</label>
                                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                           id="phone" name="phone" value="{{ old('phone') }}">
                                                    @error('phone')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="company">Company</label>
                                                    <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                                           id="company" name="company" value="{{ old('company') }}">
                                                    @error('company')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="subject">Subject <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                                   id="subject" name="subject" value="{{ old('subject') }}" required>
                                            @error('subject')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="message">Message <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                                      id="message" name="message" rows="6" required>{{ old('message') }}</textarea>
                                            <small class="form-text text-muted">
                                                Character count: <span id="messageCount">0</span>
                                            </small>
                                            @error('message')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Settings -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Contact Settings</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="new" {{ old('status') === 'new' ? 'selected' : '' }}>New</option>
                                                <option value="in_progress" {{ old('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                <option value="resolved" {{ old('status') === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                                <option value="closed" {{ old('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="is_read" name="is_read" value="1" {{ old('is_read') ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_read">Mark as Read</label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="priority">Priority</label>
                                            <select class="form-control @error('priority') is-invalid @enderror" id="priority" name="priority">
                                                <option value="low" {{ old('priority') === 'low' ? 'selected' : '' }}>Low</option>
                                                <option value="medium" {{ old('priority') === 'medium' ? 'selected' : '' }}>Medium</option>
                                                <option value="high" {{ old('priority') === 'high' ? 'selected' : '' }}>High</option>
                                                <option value="urgent" {{ old('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                            </select>
                                            @error('priority')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="ip_address">IP Address</label>
                                            <input type="text" class="form-control @error('ip_address') is-invalid @enderror" 
                                                   id="ip_address" name="ip_address" value="{{ old('ip_address', request()->ip()) }}">
                                            @error('ip_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="user_agent">User Agent</label>
                                            <textarea class="form-control @error('user_agent') is-invalid @enderror" 
                                                      id="user_agent" name="user_agent" rows="3">{{ old('user_agent', request()->userAgent()) }}</textarea>
                                            @error('user_agent')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Preview -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h4 class="card-title">Preview</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="contactPreview">
                                            <div class="preview-item">
                                                <strong>Name:</strong> <span id="previewName">-</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Email:</strong> <span id="previewEmail">-</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Subject:</strong> <span id="previewSubject">-</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Message:</strong> <span id="previewMessage">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Contact
                                </button>
                                <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.contacts.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.preview-item {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-item strong {
    color: #495057;
}

.preview-item span {
    color: #6c757d;
    word-wrap: break-word;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Character counting
    $('#message').on('input', function() {
        const count = $(this).val().length;
        $('#messageCount').text(count);
    });

    // Real-time preview
    function updatePreview() {
        $('#previewName').text($('#name').val() || '-');
        $('#previewEmail').text($('#email').val() || '-');
        $('#previewSubject').text($('#subject').val() || '-');
        const message = $('#message').val();
        $('#previewMessage').text(message ? (message.length > 100 ? message.substring(0, 100) + '...' : message) : '-');
    }

    // Update preview on input
    $('#name, #email, #subject, #message').on('input', updatePreview);

    // Initial preview update
    updatePreview();

    // Form validation
    $('#contactForm').on('submit', function(e) {
        let isValid = true;
        const requiredFields = ['name', 'email', 'subject', 'message'];
        
        requiredFields.forEach(function(field) {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid');
            }
        });
        
        // Email validation
        const email = $('#email').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
        }
    });

    // Remove validation errors on input
    $('.form-control').on('input', function() {
        $(this).removeClass('is-invalid');
    });
});

// Reset form function
function resetForm() {
    if (confirm('Are you sure you want to reset the form? All data will be lost.')) {
        document.getElementById('contactForm').reset();
        $('.form-control').removeClass('is-invalid');
        $('#messageCount').text('0');
        
        // Reset preview
        $('#previewName, #previewEmail, #previewSubject, #previewMessage').text('-');
        
        // Reset IP and User Agent
        $('#ip_address').val('{{ request()->ip() }}');
        $('#user_agent').val('{{ request()->userAgent() }}');
    }
}
</script>
@endpush