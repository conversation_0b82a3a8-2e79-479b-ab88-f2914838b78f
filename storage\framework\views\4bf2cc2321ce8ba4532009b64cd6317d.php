<?php $__env->startSection('title', 'Settings Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Settings Management</h3>
                    <div>
                        <button type="button" class="btn btn-info mr-2" onclick="clearCache()">
                            <i class="fas fa-sync"></i> Clear Cache
                        </button>

                        <div class="btn-group">
                            <button type="button" class="btn btn-success" onclick="exportSettings()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#importModal">
                                <i class="fas fa-upload"></i> Import
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="settingsForm" method="POST" action="<?php echo e(route('admin.settings.update')); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <!-- Settings Tabs -->
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupKey => $groupLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link <?php echo e($loop->first ? 'active' : ''); ?>" 
                                       id="<?php echo e($groupKey); ?>-tab" 
                                       data-toggle="tab" 
                                       href="#<?php echo e($groupKey); ?>" 
                                       role="tab">
                                        <i class="fas fa-<?php echo e($groupKey === 'site' ? 'globe' : ($groupKey === 'seo' ? 'search' : ($groupKey === 'social' ? 'share-alt' : ($groupKey === 'email' ? 'envelope' : ($groupKey === 'theme' ? 'palette' : 'cog'))))); ?>"></i>
                                        <?php echo e($groupLabel); ?>

                                        <?php if(isset($settings[$groupKey]) && $settings[$groupKey]->count() > 0): ?>
                                            <span class="badge badge-secondary ml-1"><?php echo e($settings[$groupKey]->count()); ?></span>
                                        <?php endif; ?>
                                    </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>

                        <!-- Settings Tab Content -->
                        <div class="tab-content" id="settingsTabContent">
                            <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupKey => $groupLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="tab-pane fade <?php echo e($loop->first ? 'show active' : ''); ?>" 
                                     id="<?php echo e($groupKey); ?>" 
                                     role="tabpanel">
                                    <div class="row mt-3">
                                        <?php if(isset($settings[$groupKey]) && $settings[$groupKey]->count() > 0): ?>
                                            <?php $__currentLoopData = $settings[$groupKey]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="col-md-6 mb-3">
                                                    <div class="form-group">
                                                        <label for="setting_<?php echo e($setting->key); ?>" class="font-weight-bold">
                                                            <?php echo e(ucwords(str_replace('_', ' ', $setting->key))); ?>

                                                            <?php if($setting->is_public): ?>
                                                                <span class="badge badge-info ml-1" title="Public setting">Public</span>
                                                            <?php endif; ?>
                                                        </label>
                                                        
                                                        <?php if($setting->description): ?>
                                                            <small class="form-text text-muted mb-2"><?php echo e($setting->description); ?></small>
                                                        <?php endif; ?>
                                                        
                                                        <?php switch($setting->type):
                                                            case ('textarea'): ?>
                                                                <textarea name="settings[<?php echo e($setting->key); ?>]" 
                                                                         id="setting_<?php echo e($setting->key); ?>" 
                                                                         class="form-control" 
                                                                         rows="3"
                                                                         placeholder="Enter <?php echo e(strtolower(str_replace('_', ' ', $setting->key))); ?>"><?php echo e(old('settings.' . $setting->key, $setting->value)); ?></textarea>
                                                                <?php break; ?>
                                                            
                                                            <?php case ('boolean'): ?>
                                                                <div class="custom-control custom-switch">
                                                                    <input type="checkbox" 
                                                                           class="custom-control-input" 
                                                                           id="setting_<?php echo e($setting->key); ?>" 
                                                                           name="settings[<?php echo e($setting->key); ?>]" 
                                                                           value="1"
                                                                           <?php echo e(old('settings.' . $setting->key, $setting->value) ? 'checked' : ''); ?>>
                                                                    <label class="custom-control-label" for="setting_<?php echo e($setting->key); ?>">
                                                                        <?php echo e($setting->value ? 'Enabled' : 'Disabled'); ?>

                                                                    </label>
                                                                </div>
                                                                <?php break; ?>
                                                            
                                                            <?php case ('color'): ?>
                                                                <div class="input-group">
                                                                    <input type="color" 
                                                                           name="settings[<?php echo e($setting->key); ?>]" 
                                                                           id="setting_<?php echo e($setting->key); ?>" 
                                                                           class="form-control form-control-color" 
                                                                           value="<?php echo e(old('settings.' . $setting->key, $setting->value ?: '#000000')); ?>"
                                                                           style="width: 60px;">
                                                                    <input type="text" 
                                                                           class="form-control ml-2" 
                                                                           value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                           readonly>
                                                                </div>
                                                                <?php break; ?>
                                                            
                                                            <?php case ('email'): ?>
                                                                <input type="email" 
                                                                       name="settings[<?php echo e($setting->key); ?>]" 
                                                                       id="setting_<?php echo e($setting->key); ?>" 
                                                                       class="form-control" 
                                                                       value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                       placeholder="Enter email address">
                                                                <?php break; ?>
                                                            
                                                            <?php case ('url'): ?>
                                                                <input type="url" 
                                                                       name="settings[<?php echo e($setting->key); ?>]" 
                                                                       id="setting_<?php echo e($setting->key); ?>" 
                                                                       class="form-control" 
                                                                       value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                       placeholder="https://example.com">
                                                                <?php break; ?>
                                                            
                                                            <?php case ('integer'): ?>
                                                                <input type="number" 
                                                                       name="settings[<?php echo e($setting->key); ?>]" 
                                                                       id="setting_<?php echo e($setting->key); ?>" 
                                                                       class="form-control" 
                                                                       value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                       placeholder="Enter number">
                                                                <?php break; ?>
                                                            
                                                            <?php case ('password'): ?>
                                                                <div class="input-group">
                                                                    <input type="password" 
                                                                           name="settings[<?php echo e($setting->key); ?>]" 
                                                                           id="setting_<?php echo e($setting->key); ?>" 
                                                                           class="form-control" 
                                                                           value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                           placeholder="Enter password">
                                                                    <div class="input-group-append">
                                                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('setting_<?php echo e($setting->key); ?>')">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <?php break; ?>
                                                            
                                                            <?php default: ?>
                                                                <input type="text" 
                                                                       name="settings[<?php echo e($setting->key); ?>]" 
                                                                       id="setting_<?php echo e($setting->key); ?>" 
                                                                       class="form-control" 
                                                                       value="<?php echo e(old('settings.' . $setting->key, $setting->value)); ?>"
                                                                       placeholder="Enter <?php echo e(strtolower(str_replace('_', ' ', $setting->key))); ?>">
                                                        <?php endswitch; ?>
                                                        
                                                        <?php $__errorArgs = ['settings.' . $setting->key];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <div class="col-12">
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    <strong>No settings found for <?php echo e($groupLabel); ?>.</strong>
                                                    <br>
                                                    <small>Settings should be automatically initialized. Please refresh the page or contact your system administrator if this persists.</small>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo"></i> Reset
                                        </button>
                                    </div>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save"></i> Save All Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Settings</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="<?php echo e(route('admin.settings.import')); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="settings_file">Select Settings File (JSON)</label>
                        <input type="file" class="form-control-file" id="settings_file" name="settings_file" accept=".json" required>
                        <small class="form-text text-muted">Upload a JSON file containing settings data.</small>
                    </div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> Importing settings will overwrite existing values with the same keys.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Import Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.nav-tabs .nav-link {
    border-radius: 0.25rem 0.25rem 0 0;
}

.nav-tabs .nav-link.active {
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #f8f9fa;
}

.tab-content {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1rem;
    border-radius: 0 0 0.25rem 0.25rem;
}

.form-control-color {
    height: 38px;
    padding: 0.375rem 0.75rem;
}

.custom-control-label::before {
    border-radius: 1rem;
}

.custom-control-label::after {
    border-radius: 1rem;
}

.badge {
    font-size: 0.7rem;
}

.alert {
    border-radius: 0.375rem;
}

.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Color picker sync
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(function(colorInput) {
        const textInput = colorInput.nextElementSibling;
        
        colorInput.addEventListener('change', function() {
            textInput.value = this.value;
        });
        
        textInput.addEventListener('input', function() {
            if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                colorInput.value = this.value;
            }
        });
    });
    
    // Switch label updates
    const switches = document.querySelectorAll('.custom-control-input[type="checkbox"]');
    switches.forEach(function(switchInput) {
        const label = switchInput.nextElementSibling;
        
        switchInput.addEventListener('change', function() {
            label.textContent = this.checked ? 'Enabled' : 'Disabled';
        });
    });
});

// Clear cache
function clearCache() {
    if (confirm('Are you sure you want to clear the settings cache?')) {
        fetch('<?php echo e(route("admin.settings.clear-cache")); ?>', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache cleared successfully!');
            } else {
                alert('Failed to clear cache.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing cache.');
        });
    }
}



// Export settings
function exportSettings() {
    window.open('<?php echo e(route("admin.settings.export")); ?>', '_blank');
}

// Reset form
function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('settingsForm').reset();
        
        // Reset switch labels
        const switches = document.querySelectorAll('.custom-control-input[type="checkbox"]');
        switches.forEach(function(switchInput) {
            const label = switchInput.nextElementSibling;
            label.textContent = switchInput.checked ? 'Enabled' : 'Disabled';
        });
        
        // Reset color inputs
        const colorInputs = document.querySelectorAll('input[type="color"]');
        colorInputs.forEach(function(colorInput) {
            const textInput = colorInput.nextElementSibling;
            textInput.value = colorInput.value;
        });
    }
}

// Form submission with loading state
document.getElementById('settingsForm').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds as fallback
    setTimeout(function() {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>