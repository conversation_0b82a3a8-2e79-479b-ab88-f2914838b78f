<?php $__env->startSection('title', 'Contact Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Contact Messages</h3>
                    <p class="text-muted mb-0">Manage customer inquiries and messages</p>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.contacts.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="<?php echo e(request('search')); ?>" placeholder="Search by name, email, or subject...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="new" <?php echo e(request('status') === 'new' ? 'selected' : ''); ?>>New</option>
                                        <option value="in_progress" <?php echo e(request('status') === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                                        <option value="resolved" <?php echo e(request('status') === 'resolved' ? 'selected' : ''); ?>>Resolved</option>
                                        <option value="closed" <?php echo e(request('status') === 'closed' ? 'selected' : ''); ?>>Closed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="read_status">Read Status</label>
                                    <select class="form-control" id="read_status" name="read_status">
                                        <option value="">All</option>
                                        <option value="read" <?php echo e(request('read_status') === 'read' ? 'selected' : ''); ?>>Read</option>
                                        <option value="unread" <?php echo e(request('read_status') === 'unread' ? 'selected' : ''); ?>>Unread</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_range">Date Range</label>
                                    <select class="form-control" id="date_range" name="date_range">
                                        <option value="">All Time</option>
                                        <option value="today" <?php echo e(request('date_range') === 'today' ? 'selected' : ''); ?>>Today</option>
                                        <option value="week" <?php echo e(request('date_range') === 'week' ? 'selected' : ''); ?>>This Week</option>
                                        <option value="month" <?php echo e(request('date_range') === 'month' ? 'selected' : ''); ?>>This Month</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="sort">Sort By</label>
                                    <select class="form-control" id="sort" name="sort">
                                        <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Date</option>
                                        <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                                        <option value="status" <?php echo e(request('sort') === 'status' ? 'selected' : ''); ?>>Status</option>
                                        <option value="subject" <?php echo e(request('sort') === 'subject' ? 'selected' : ''); ?>>Subject</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-envelope"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Contacts</span>
                                    <span class="info-box-number"><?php echo e($stats['total'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-envelope-open"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Unread</span>
                                    <span class="info-box-number"><?php echo e($stats['unread'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">In Progress</span>
                                    <span class="info-box-number"><?php echo e($stats['in_progress'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Resolved</span>
                                    <span class="info-box-number"><?php echo e($stats['resolved'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <form id="bulkForm" method="POST" action="<?php echo e(route('admin.contacts.bulk-action')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="action" class="form-control" required>
                                        <option value="">Select Bulk Action</option>
                                        <option value="mark_read">Mark as Read</option>
                                        <option value="mark_unread">Mark as Unread</option>
                                        <option value="status_new">Set Status: New</option>
                                        <option value="status_in_progress">Set Status: In Progress</option>
                                        <option value="status_resolved">Set Status: Resolved</option>
                                        <option value="status_closed">Set Status: Closed</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted"><?php echo e($contacts->total()); ?> total contacts</span>
                            </div>
                        </div>

                        <!-- Contacts Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Contact Info</th>
                                        <th>Subject</th>
                                        <th>Message</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="<?php echo e(!$contact->is_read ? 'table-warning' : ''); ?> <?php echo e($contact->status === 'new' ? 'border-left-primary' : ''); ?>">
                                        <td>
                                            <input type="checkbox" name="selected_ids[]" value="<?php echo e($contact->id); ?>" class="contact-checkbox">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if(!$contact->is_read): ?>
                                                    <span class="badge badge-warning badge-sm mr-2">NEW</span>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo e($contact->name); ?></strong>
                                                    <br><small class="text-muted"><?php echo e($contact->email); ?></small>
                                                    <?php if($contact->phone): ?>
                                                        <br><small class="text-info"><?php echo e($contact->phone); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="subject-preview">
                                                <?php echo e(Str::limit($contact->subject, 40)); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <div class="message-preview">
                                                <?php echo e(Str::limit($contact->message, 60)); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <span class="badge badge-<?php echo e($contact->status === 'new' ? 'primary' : ($contact->status === 'in_progress' ? 'warning' : ($contact->status === 'resolved' ? 'success' : 'secondary'))); ?>">
                                                    <?php echo e(ucfirst(str_replace('_', ' ', $contact->status))); ?>

                                                </span>
                                            </div>
                                            <div class="mt-1">
                                                <select class="form-control form-control-sm" onchange="changeStatus(<?php echo e($contact->id); ?>, this.value)">
                                                    <option value="new" <?php echo e($contact->status === 'new' ? 'selected' : ''); ?>>New</option>
                                                    <option value="in_progress" <?php echo e($contact->status === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                                                    <option value="resolved" <?php echo e($contact->status === 'resolved' ? 'selected' : ''); ?>>Resolved</option>
                                                    <option value="closed" <?php echo e($contact->status === 'closed' ? 'selected' : ''); ?>>Closed</option>
                                                </select>
                                            </div>
                                        </td>
                                        <td>
                                            <small><?php echo e($contact->created_at->format('M d, Y')); ?></small>
                                            <br><small class="text-muted"><?php echo e($contact->created_at->format('H:i')); ?></small>
                                            <?php if($contact->ip_address): ?>
                                                <br><small class="text-muted"><?php echo e($contact->ip_address); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.contacts.show', $contact)); ?>" class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.contacts.edit', $contact)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if(!$contact->is_read): ?>
                                                    <button type="button" class="btn btn-sm btn-success" onclick="markAsRead(<?php echo e($contact->id); ?>)" title="Mark as Read">
                                                        <i class="fas fa-envelope-open"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-secondary" onclick="markAsUnread(<?php echo e($contact->id); ?>)" title="Mark as Unread">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteContact(<?php echo e($contact->id); ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-envelope fa-3x mb-3"></i>
                                                <h5>No contacts found</h5>
                                                <p>No contacts match your current filters.</p>
                                                <?php if(request()->hasAny(['search', 'status', 'read_status', 'date_range'])): ?>
                                                    <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-primary">
                                                        <i class="fas fa-refresh"></i> Clear Filters
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    <?php if($contacts->hasPages()): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing <?php echo e($contacts->firstItem()); ?> to <?php echo e($contacts->lastItem()); ?> of <?php echo e($contacts->total()); ?> results
                            </p>
                        </div>
                        <div>
                            <?php echo e($contacts->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this contact?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-box-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
}

.subject-preview, .message-preview {
    max-width: 200px;
    word-wrap: break-word;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.border-left-primary {
    border-left: 3px solid #2678a1db;
}

.badge-sm {
    font-size: 0.7rem;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select All functionality
$('#selectAll').change(function() {
    $('.contact-checkbox').prop('checked', this.checked);
});

$('.contact-checkbox').change(function() {
    if (!this.checked) {
        $('#selectAll').prop('checked', false);
    } else if ($('.contact-checkbox:checked').length === $('.contact-checkbox').length) {
        $('#selectAll').prop('checked', true);
    }
});

// Bulk action confirmation
function confirmBulkAction() {
    const selectedContacts = $('.contact-checkbox:checked').length;
    const action = $('select[name="action"]').val();

    if (selectedContacts === 0) {
        alert('Please select at least one contact.');
        return false;
    }

    if (action === '') {
        alert('Please select an action.');
        return false;
    }

    const actionText = action.replace('_', ' ');
    return confirm(`Are you sure you want to ${actionText} ${selectedContacts} selected contact(s)?`);
}

// Change contact status
function changeStatus(contactId, status) {
    $.ajax({
        url: `/admin/contacts/${contactId}/status`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            status: status
        },
        success: function(response) {
            if (response.success) {
                // Update the badge
                const badge = $(`.contact-checkbox[value="${contactId}"]`).closest('tr').find('.badge');
                badge.removeClass('badge-primary badge-warning badge-success badge-secondary');

                if (status === 'new') badge.addClass('badge-primary');
                else if (status === 'in_progress') badge.addClass('badge-warning');
                else if (status === 'resolved') badge.addClass('badge-success');
                else badge.addClass('badge-secondary');

                badge.text(status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
            } else {
                alert('Error updating contact status');
            }
        },
        error: function() {
            alert('Error updating contact status');
        }
    });
}

// Mark as read
function markAsRead(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-read`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as read');
            }
        },
        error: function() {
            alert('Error marking contact as read');
        }
    });
}

// Mark as unread
function markAsUnread(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-unread`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as unread');
            }
        },
        error: function() {
            alert('Error marking contact as unread');
        }
    });
}

// Delete contact
function deleteContact(contactId) {
    $('#deleteForm').attr('action', `/admin/contacts/${contactId}`);
    $('#deleteModal').modal('show');
}

// Auto-submit form on filter change
$('#status, #read_status, #date_range, #sort').change(function() {
    $(this).closest('form').submit();
});

// Clear search on escape
$('#search').keyup(function(e) {
    if (e.keyCode === 27) { // Escape key
        $(this).val('');
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/contacts/index.blade.php ENDPATH**/ ?>