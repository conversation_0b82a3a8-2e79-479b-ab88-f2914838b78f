@extends('layouts.admin')

@section('title', 'Edit Tag')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Tag: {{ $tag->name }}</h3>
                    <div>
                        <a href="{{ route('admin.tags.show', $tag) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.tags.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tags
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.tags.update', $tag) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="name" class="required">Tag Name</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $tag->name) }}" 
                                                   placeholder="Enter tag name" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                The name of the tag as it will appear on the website.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="slug">Slug</label>
                                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                   id="slug" name="slug" value="{{ old('slug', $tag->slug) }}" 
                                                   placeholder="Auto-generated from name">
                                            @error('slug')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                URL-friendly version of the name. Leave blank to auto-generate.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="description">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" 
                                                      placeholder="Enter tag description (optional)">{{ old('description', $tag->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Optional description for the tag.
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO Settings -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">SEO Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="meta_title">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title', $tag->meta_title) }}" 
                                                   placeholder="Enter meta title (optional)" maxlength="60">
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <span id="meta_title_count">0</span>/60 characters. Leave blank to use tag name.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="meta_description">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                      id="meta_description" name="meta_description" rows="3" 
                                                      placeholder="Enter meta description (optional)" maxlength="160">{{ old('meta_description', $tag->meta_description) }}</textarea>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <span id="meta_description_count">0</span>/160 characters. Leave blank to use tag description.
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Associated Posts -->
                                @if($tag->posts->count() > 0)
                                <div class="card mt-3">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0">Associated Posts ({{ $tag->posts->count() }})</h5>
                                        <a href="{{ route('admin.posts.index', ['tag' => $tag->id]) }}" class="btn btn-sm btn-primary">
                                            View All Posts
                                        </a>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Status</th>
                                                        <th>Published</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($tag->posts()->latest()->take(5)->get() as $post)
                                                    <tr>
                                                        <td>
                                                            <a href="{{ route('admin.posts.show', $post) }}" class="text-decoration-none">
                                                                {{ $post->title }}
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-{{ $post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning') }}">
                                                                {{ ucfirst($post->status) }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            @if($post->published_at)
                                                                {{ $post->published_at->format('M d, Y') }}
                                                            @else
                                                                <span class="text-muted">Not published</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-xs btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        @if($tag->posts->count() > 5)
                                        <div class="text-center mt-2">
                                            <a href="{{ route('admin.posts.index', ['tag' => $tag->id]) }}" class="btn btn-sm btn-outline-primary">
                                                View All {{ $tag->posts->count() }} Posts
                                            </a>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            </div>

                            <div class="col-md-4">
                                <!-- Publishing Options -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Publishing Options</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" 
                                                    id="status" name="status" required>
                                                <option value="active" {{ old('status', $tag->status) === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status', $tag->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Only active tags will be visible on the website.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="sort_order">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $tag->sort_order) }}" 
                                                   min="0" placeholder="0">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Lower numbers appear first. Default is 0.
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tag Statistics -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Statistics</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border-right">
                                                    <h4 class="text-primary">{{ $tag->posts->count() }}</h4>
                                                    <small class="text-muted">Posts</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <h4 class="text-success">{{ $tag->posts()->where('status', 'published')->count() }}</h4>
                                                <small class="text-muted">Published</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tag Information -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Tag Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <th>Created:</th>
                                                <td>{{ $tag->created_at->format('M d, Y H:i') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Updated:</th>
                                                <td>{{ $tag->updated_at->format('M d, Y H:i') }}</td>
                                            </tr>
                                            <tr>
                                                <th>Current Slug:</th>
                                                <td><code>{{ $tag->slug }}</code></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <!-- Tag Preview -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Preview</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="tag-preview">
                                            <h6 id="preview_name" class="text-primary">{{ $tag->name }}</h6>
                                            <p id="preview_description" class="text-muted small mb-2">{{ $tag->description ?: 'Tag description will appear here...' }}</p>
                                            <code id="preview_slug">{{ $tag->slug }}</code>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Quick Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <a href="{{ route('admin.tags.show', $tag) }}" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> View Tag
                                            </a>
                                            
                                            <button type="button" class="btn btn-{{ $tag->status === 'active' ? 'secondary' : 'success' }} btn-sm" 
                                                    onclick="toggleStatus({{ $tag->id }})">
                                                <i class="fas fa-{{ $tag->status === 'active' ? 'eye-slash' : 'eye' }}"></i> 
                                                {{ $tag->status === 'active' ? 'Deactivate' : 'Activate' }}
                                            </button>
                                            
                                            @if($tag->posts->count() === 0)
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteTag({{ $tag->id }})">
                                                <i class="fas fa-trash"></i> Delete Tag
                                            </button>
                                            @else
                                            <button type="button" class="btn btn-danger btn-sm" disabled title="Cannot delete tag with associated posts">
                                                <i class="fas fa-trash"></i> Delete Tag
                                            </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Tag
                                </button>
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-success">
                                    <i class="fas fa-check"></i> Save & Continue Editing
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.tags.show', $tag) }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{{ route('admin.tags.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this tag?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: ' *';
    color: red;
}

.tag-preview {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
}

.tag-preview h6 {
    margin-bottom: 0.5rem;
}

.tag-preview code {
    font-size: 0.875rem;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate slug from name (only if slug is empty or matches current name)
    const originalSlug = '{{ $tag->slug }}';
    const originalName = '{{ $tag->name }}';
    
    $('#name').on('input', function() {
        const name = $(this).val();
        const currentSlug = $('#slug').val();
        
        // Only auto-generate if slug is empty or matches the original pattern
        if (!currentSlug || currentSlug === originalSlug) {
            const slug = name.toLowerCase()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
            
            $('#slug').val(slug);
        }
        updatePreview();
    });

    // Update preview when fields change
    $('#name, #description, #slug').on('input', updatePreview);

    // Character count for meta fields
    $('#meta_title').on('input', function() {
        updateCharCount(this, '#meta_title_count');
    });

    $('#meta_description').on('input', function() {
        updateCharCount(this, '#meta_description_count');
    });

    // Initialize character counts
    updateCharCount('#meta_title', '#meta_title_count');
    updateCharCount('#meta_description', '#meta_description_count');

    function updateCharCount(input, counter) {
        const length = $(input).val().length;
        $(counter).text(length);
        
        // Add warning color if approaching limit
        const maxLength = $(input).attr('maxlength');
        if (length > maxLength * 0.8) {
            $(counter).addClass('text-warning');
        } else {
            $(counter).removeClass('text-warning');
        }
        
        if (length >= maxLength) {
            $(counter).addClass('text-danger').removeClass('text-warning');
        } else {
            $(counter).removeClass('text-danger');
        }
    }

    function updatePreview() {
        const name = $('#name').val() || 'Tag Name';
        const description = $('#description').val() || 'Tag description will appear here...';
        const slug = $('#slug').val() || 'tag-slug';
        
        $('#preview_name').text(name);
        $('#preview_description').text(description);
        $('#preview_slug').text(slug);
    }
});

// Toggle status
function toggleStatus(tagId) {
    $.ajax({
        url: `/admin/tags/${tagId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

// Delete tag
function deleteTag(tagId) {
    $('#deleteForm').attr('action', `/admin/tags/${tagId}`);
    $('#deleteModal').modal('show');
}

// Form validation
$('form').on('submit', function(e) {
    const name = $('#name').val().trim();
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a tag name.');
        $('#name').focus();
        return false;
    }
    
    // Show loading state
    $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');
});
</script>
@endpush