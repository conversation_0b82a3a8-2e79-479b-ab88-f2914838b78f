<?php

namespace Database\Factories;

use App\Models\Testimonial;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Testimonial>
 */
class TestimonialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $testimonials = [
            "The leadership program at PESCOT completely transformed my approach to management. I learned to lead with integrity and purpose, which has made a tremendous impact on my team and organization.",
            "PESCOT helped me develop strong character foundations that guide my daily decisions. The biblical principles combined with practical application have been life-changing.",
            "The family life program strengthened our relationships and gave us tools to communicate better. Our family is closer than ever thanks to what we learned.",
            "As a young person, the Youth Leadership Academy gave me confidence and direction. I now have the skills and character to make a positive difference in my community.",
            "The community outreach training equipped me with practical skills to serve others effectively. I've been able to start several successful community initiatives.",
            "PESCOT programs are exceptional in their depth and practical application. The instructors are knowledgeable and truly care about each participant's growth.",
            "I was skeptical at first, but the character development workshop exceeded my expectations. The content was relevant and the impact has been lasting.",
            "The online format made it possible for me to participate despite my busy schedule. The quality of instruction was excellent and the community support was amazing.",
            "PESCOT doesn't just teach concepts - they help you apply them in real life. The mentorship and ongoing support have been invaluable to my personal growth.",
            "The leadership principles I learned have helped me in every area of life - work, family, and community involvement. I highly recommend PESCOT programs to anyone seeking growth."
        ];

        $positions = [
            'CEO', 'Manager', 'Director', 'Team Lead', 'Supervisor', 'Coordinator',
            'Pastor', 'Teacher', 'Consultant', 'Entrepreneur', 'Student', 'Volunteer',
            'Executive', 'Administrator', 'Specialist', 'Analyst', 'Officer', 'Representative'
        ];

        $companies = [
            'Tech Solutions Inc', 'Global Enterprises', 'Community Health Center', 'Local Church',
            'Educational Institute', 'Non-Profit Organization', 'Business Consulting Group',
            'Healthcare Services', 'Financial Advisory', 'Marketing Agency', 'Construction Company',
            'Retail Corporation', 'Government Agency', 'Social Services', 'Media Company',
            'Transportation Services', 'Real Estate Group', 'Manufacturing Corp'
        ];

        return [
            'name' => $this->faker->name(),
            'position' => $this->faker->randomElement($positions),
            'company' => $this->faker->randomElement($companies),
            'testimonial' => $this->faker->randomElement($testimonials),
            'image' => null, // Will be handled separately if needed
            'rating' => $this->faker->numberBetween(4, 5), // High ratings for quality testimonials
            'status' => $this->faker->boolean(90), // 90% chance of being active
            'featured' => $this->faker->boolean(30), // 30% chance of being featured
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }

    /**
     * Indicate that the testimonial is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the testimonial is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the testimonial is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
            'status' => true, // Featured testimonials should be active
        ]);
    }

    /**
     * Indicate that the testimonial is not featured.
     */
    public function notFeatured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => false,
        ]);
    }

    /**
     * Set a specific rating for the testimonial.
     */
    public function rating(int $rating): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => max(1, min(5, $rating)), // Ensure rating is between 1-5
        ]);
    }

    /**
     * Set high rating (4-5 stars).
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
        ]);
    }

    /**
     * Set perfect rating (5 stars).
     */
    public function perfectRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => 5,
        ]);
    }
}