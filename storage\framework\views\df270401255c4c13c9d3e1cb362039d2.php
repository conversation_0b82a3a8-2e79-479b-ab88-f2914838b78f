<?php $__env->startSection('title', 'Comments Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Comments Management</h3>
                    <a href="<?php echo e(route('admin.comments.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Comment
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.comments.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?php echo e(request('search')); ?>" placeholder="Search by name, email, or comment...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Approved</option>
                                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                                        <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="post">Post</label>
                                    <select class="form-control" id="post" name="post">
                                        <option value="">All Posts</option>
                                        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($post->id); ?>" <?php echo e(request('post') == $post->id ? 'selected' : ''); ?>>
                                                <?php echo e(Str::limit($post->title, 40)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="sort">Sort By</label>
                                    <select class="form-control" id="sort" name="sort">
                                        <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Date</option>
                                        <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                                        <option value="status" <?php echo e(request('sort') === 'status' ? 'selected' : ''); ?>>Status</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-search"></i> Filter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-comments"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Comments</span>
                                    <span class="info-box-number"><?php echo e($stats['total'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Approved</span>
                                    <span class="info-box-number"><?php echo e($stats['approved'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Pending</span>
                                    <span class="info-box-number"><?php echo e($stats['pending'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Rejected</span>
                                    <span class="info-box-number"><?php echo e($stats['rejected'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <form id="bulkForm" method="POST" action="<?php echo e(route('admin.comments.bulk-action')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="action" class="form-control" required>
                                        <option value="">Select Bulk Action</option>
                                        <option value="approve">Approve</option>
                                        <option value="reject">Reject</option>
                                        <option value="pending">Mark as Pending</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted"><?php echo e($comments->total()); ?> total comments</span>
                            </div>
                        </div>

                        <!-- Comments Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Author</th>
                                        <th>Comment</th>
                                        <th>Post</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="<?php echo e($comment->status === 'pending' ? 'table-warning' : ($comment->status === 'rejected' ? 'table-danger' : '')); ?>">
                                        <td>
                                            <input type="checkbox" name="selected_ids[]" value="<?php echo e($comment->id); ?>" class="comment-checkbox">
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e($comment->name); ?></strong>
                                                <?php if($comment->user_id): ?>
                                                    <span class="badge badge-info">Registered</span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted"><?php echo e($comment->email); ?></small>
                                            <?php if($comment->website): ?>
                                                <br><small><a href="<?php echo e($comment->website); ?>" target="_blank" class="text-info"><?php echo e($comment->website); ?></a></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="comment-preview">
                                                <?php echo e(Str::limit($comment->comment, 100)); ?>

                                            </div>
                                            <?php if($comment->parent_id): ?>
                                                <small class="text-muted">
                                                    <i class="fas fa-reply"></i> Reply to: <?php echo e(Str::limit($comment->parent->comment ?? '', 30)); ?>

                                                </small>
                                            <?php endif; ?>
                                            <?php if($comment->replies->count() > 0): ?>
                                                <br><small class="text-info">
                                                    <i class="fas fa-comments"></i> <?php echo e($comment->replies->count()); ?> <?php echo e(Str::plural('reply', $comment->replies->count())); ?>

                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($comment->post): ?>
                                                <a href="<?php echo e(route('admin.posts.show', $comment->post)); ?>" class="text-decoration-none">
                                                    <?php echo e(Str::limit($comment->post->title, 30)); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">Post deleted</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if($comment->status === 'approved'): ?>
                                                    <button type="button" class="btn btn-xs btn-success" onclick="changeStatus(<?php echo e($comment->id); ?>, 'pending')" title="Mark as Pending">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php elseif($comment->status === 'pending'): ?>
                                                    <button type="button" class="btn btn-xs btn-warning" onclick="changeStatus(<?php echo e($comment->id); ?>, 'approved')" title="Approve">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-xs btn-danger" onclick="changeStatus(<?php echo e($comment->id); ?>, 'approved')" title="Approve">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <small><?php echo e($comment->created_at->format('M d, Y')); ?></small>
                                            <br><small class="text-muted"><?php echo e($comment->created_at->format('H:i')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.comments.show', $comment)); ?>" class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.comments.edit', $comment)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if($comment->status === 'pending'): ?>
                                                    <button type="button" class="btn btn-sm btn-success" onclick="changeStatus(<?php echo e($comment->id); ?>, 'approved')" title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="changeStatus(<?php echo e($comment->id); ?>, 'rejected')" title="Reject">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteComment(<?php echo e($comment->id); ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-comments fa-3x mb-3"></i>
                                                <h5>No comments found</h5>
                                                <p>No comments match your current filters.</p>
                                                <?php if(request()->hasAny(['search', 'status', 'post'])): ?>
                                                    <a href="<?php echo e(route('admin.comments.index')); ?>" class="btn btn-primary">
                                                        <i class="fas fa-refresh"></i> Clear Filters
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    <?php if($comments->hasPages()): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing <?php echo e($comments->firstItem()); ?> to <?php echo e($comments->lastItem()); ?> of <?php echo e($comments->total()); ?> results
                            </p>
                        </div>
                        <div>
                            <?php echo e($comments->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this comment?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This will also delete all replies to this comment.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-box-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
}

.comment-preview {
    max-width: 300px;
    word-wrap: break-word;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select All functionality
$('#selectAll').change(function() {
    $('.comment-checkbox').prop('checked', this.checked);
});

$('.comment-checkbox').change(function() {
    if (!this.checked) {
        $('#selectAll').prop('checked', false);
    } else if ($('.comment-checkbox:checked').length === $('.comment-checkbox').length) {
        $('#selectAll').prop('checked', true);
    }
});

// Bulk action confirmation
function confirmBulkAction() {
    const selectedComments = $('.comment-checkbox:checked').length;
    const action = $('select[name="action"]').val();
    
    if (selectedComments === 0) {
        alert('Please select at least one comment.');
        return false;
    }
    
    if (action === '') {
        alert('Please select an action.');
        return false;
    }
    
    const actionText = action === 'delete' ? 'delete' : action;
    return confirm(`Are you sure you want to ${actionText} ${selectedComments} selected comment(s)?`);
}

// Change comment status
function changeStatus(commentId, status) {
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);
    
    if (!confirm(`Are you sure you want to mark this comment as ${statusText}?`)) {
        return;
    }
    
    $.ajax({
        url: `/admin/comments/${commentId}/${status}`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating comment status');
            }
        },
        error: function() {
            alert('Error updating comment status');
        }
    });
}

// Delete comment
function deleteComment(commentId) {
    $('#deleteForm').attr('action', `/admin/comments/${commentId}`);
    $('#deleteModal').modal('show');
}

// Auto-submit form on filter change
$('#status, #post, #sort').change(function() {
    $(this).closest('form').submit();
});

// Clear search on escape
$('#search').keyup(function(e) {
    if (e.keyCode === 27) { // Escape key
        $(this).val('');
    }
});

// Initialize Select2 for post filter
$('#post').select2({
    placeholder: 'Select a post...',
    allowClear: true,
    width: '100%'
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/comments/index.blade.php ENDPATH**/ ?>