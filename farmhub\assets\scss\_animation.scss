//>>>>> Video Animation Start <<<<<//
@-webkit-keyframes rippleOne {
	70% {
		-webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
		box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
	}
	100% {
		-webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
		box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
	}
}

@keyframes rippleOne {
	70% {
		-webkit-box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
		box-shadow: 0 0 0 40px rgba(244, 68, 56, 0);
	}
	100% {
		-webkit-box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
		box-shadow: 0 0 0 0 rgba(244, 68, 56, 0);
	}
}
//>>>>> Video Animation End <<<<<//

//>>>>> Circle Animation Start <<<<<//
@keyframes cir36 {
	100% {
		transform: rotate(360deg);
	}
}
@keyframes palyzom {
	50%{
		transform: scale(1.1);
	}
}

@keyframes rounded {
	50% {
		transform: rotate(15deg);
	}
}

//>>>>> Circle Animation End <<<<<//

//>>>>> Updown End <<<<<//
@keyframes updown {
	50% {
		transform: translateY(-10px);
	}
}
@keyframes lf {
	50% {
		transform: translateX(14px);
	}
}
@keyframes zin1 {
	50% {
		transform: scale(1.08);
	}
}
@keyframes rots1 {
	50% {
		transform: rotate(10deg);
	}
}
//>>>>> Updown End <<<<<//

//>>>>> Preloader Animation Start <<<<<//
@-webkit-keyframes spinner {
	to {
		-webkit-transform: rotateZ(360deg);
		transform: rotateZ(360deg);
	}
}

@keyframes spinner {
	to {
		-webkit-transform: rotateZ(360deg);
		transform: rotateZ(360deg);
	}
}

@-webkit-keyframes letters-loading {
	0%,
	75%,
	100% {
		opacity: 0;
		transform: rotateY(-90deg);
	}
	25%,
	50% {
		opacity: 1;
		transform: rotateY(0deg);
	}
}

@keyframes letters-loading {
	0%,
	75%,
	100% {
		opacity: 0;
		transform: rotateY(-90deg);
	}
	25%,
	50% {
		opacity: 1;
		transform: rotateY(0deg);
	}
}

//>>>>> Preloader Animation Start <<<<<//
@keyframes loaderspin {
	0% {
		transform: translate(-50%, -50%) rotate(0deg);
	}
	100% {
		transform: translate(-50%, -50%) rotate(360deg);
	}
}

@keyframes tpswing {
	0% {
		-webkit-transform: rotate(20deg);
		-ms-transform: rotate(20deg);
		transform: rotate(20deg);
	}
	100% {
		-webkit-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		transform: rotate(0deg);
	}
}

@keyframes width {
	0% {
		width: 0%;
	}
	100% {
		width: 100%;
	}
}

@-webkit-keyframes width {
	0% {
		width: 0%;
	}
	100% {
		width: 100%;
	}
}

@-webkit-keyframes loaderspin {
	0% {
		transform: translate(-50%, -50%) rotate(0deg);
	}
	100% {
		transform: translate(-50%, -50%) rotate(360deg);
	}
}

@keyframes loaderpulse {
	0% {
		transform: scale(1);
	}
	100% {
		transform: scale(1.2);
	}
}
//>>>>> Preloader Animation End <<<<<//

//animation
@keyframes rounded {
	50% {
		transform: rotate(20deg);
	}
}

@keyframes cir36 {
	100% {
		transform: rotate(360deg);
	}
}

.float-bob-y {
	-webkit-animation-name: float-bob-y;
	animation-name: float-bob-y;
	-webkit-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	animation-timing-function: linear;
}

@-webkit-keyframes float-bob-y {
	0% {
		-webkit-transform: translateY(-30px);
		transform: translateY(-30px);
	}
	50% {
		-webkit-transform: translateY(-10px);
		transform: translateY(-10px);
	}
	100% {
		-webkit-transform: translateY(-30px);
		transform: translateY(-30px);
	}
}

@keyframes float-bob-y {
	0% {
		-webkit-transform: translateY(-30px);
		transform: translateY(-30px);
	}
	50% {
		-webkit-transform: translateY(-10px);
		transform: translateY(-10px);
	}
	100% {
		-webkit-transform: translateY(-30px);
		transform: translateY(-30px);
	}
}

.float-bob-x {
	-webkit-animation-name: float-bob-x;
	animation-name: float-bob-x;
	-webkit-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	animation-timing-function: linear;
}

@-webkit-keyframes float-bob-x {
	0% {
		-webkit-transform: translateX(-0px);
		transform: translateX(30px);
	}
	50% {
		-webkit-transform: translateX(10px);
		transform: translateX(10px);
	}
	100% {
		-webkit-transform: translateX(30px);
		transform: translateX(30px);
	}
}

@keyframes float-bob-x {
	0% {
		-webkit-transform: translateX(30px);
		transform: translateX(30px);
	}
	50% {
		-webkit-transform: translateX(10px);
		transform: translateX(10px);
	}
	100% {
		-webkit-transform: translateX(30px);
		transform: translateX(30px);
	}
}

@keyframes bounce-x {
	0% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
	}
	50% {
		-webkit-transform: translateX(30px);
		transform: translateX(30px);
	}
	100% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
	}
}

.bounce-x {
	-webkit-animation: bounce-x 7s infinite linear;
	animation: bounce-x 7s infinite linear;
}

@keyframes criss-cross-left {
	0% {
		left: -20px;
	}
	50% {
		left: 50%;
		width: 20px;
		height: 20px;
	}
	100% {
		left: 50%;
		width: 375px;
		height: 375px;
	}
}
@keyframes criss-cross-right {
	0% {
		right: -20px;
	}
	50% {
		right: 50%;
		width: 20px;
		height: 20px;
	}
	100% {
		right: 50%;
		width: 375px;
		height: 375px;
	}
}

@keyframes rotated2 {
	0% {
		transform: rotate(0);
	}
	100% {
		transform: rotate(-360deg);
	}
}

@keyframes wave {
	0% {
		transform: translateX(0);
	}
	50% {
		transform: translateX(-25%);
	}
	100% {
		transform: translateX(-50%);
	}
}

@keyframes zints1 {
	100% {
		width: 65%;
		height: 65%;
	}
}
@keyframes zints2 {
	100% {
		width: 65%;
		height: 65%;
	}
}
