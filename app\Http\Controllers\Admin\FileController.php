<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class FileController extends Controller
{
    /**
     * Display the file manager interface.
     */
    public function index(Request $request)
    {
        $path = $request->get('path', '');
        $disk = 'public';
        
        // Ensure path is safe
        $path = $this->sanitizePath($path);
        
        $directories = [];
        $files = [];
        
        try {
            // Get directories
            $allDirectories = Storage::disk($disk)->directories($path);
            foreach ($allDirectories as $directory) {
                $directories[] = [
                    'name' => basename($directory),
                    'path' => $directory,
                    'type' => 'directory',
                    'size' => $this->getDirectorySize($directory),
                    'modified' => Storage::disk($disk)->lastModified($directory),
                ];
            }
            
            // Get files
            $allFiles = Storage::disk($disk)->files($path);
            foreach ($allFiles as $file) {
                $files[] = [
                    'name' => basename($file),
                    'path' => $file,
                    'type' => $this->getFileType($file),
                    'size' => Storage::disk($disk)->size($file),
                    'modified' => Storage::disk($disk)->lastModified($file),
                    'url' => Storage::disk($disk)->url($file),
                    'extension' => pathinfo($file, PATHINFO_EXTENSION),
                    'mime_type' => Storage::disk($disk)->mimeType($file),
                ];
            }
            
        } catch (\Exception $e) {
            return back()->with('error', 'Error accessing directory: ' . $e->getMessage());
        }
        
        // Breadcrumb navigation
        $breadcrumbs = $this->generateBreadcrumbs($path);
        
        // Format data for view
        $folders = array_map(function($dir) {
            return [
                'name' => $dir['name'],
                'path' => $dir['path'],
                'size' => $this->formatFileSize($dir['size']),
                'modified' => date('Y-m-d H:i:s', $dir['modified'])
            ];
        }, $directories);

        $files = array_map(function($file) {
            return [
                'name' => $file['name'],
                'path' => $file['path'],
                'size' => $this->formatFileSize($file['size']),
                'extension' => $file['extension'],
                'url' => $file['url'],
                'modified' => date('Y-m-d H:i:s', $file['modified'])
            ];
        }, $files);

        return view('admin.files.index', compact('folders', 'files', 'path', 'breadcrumbs'));
    }

    /**
     * Upload files to the current directory.
     */
    public function upload(Request $request)
    {
        $request->validate([
            'files.*' => 'required|file|max:10240', // 10MB max
            'path' => 'nullable|string',
        ]);

        $path = $this->sanitizePath($request->get('path', ''));
        $uploadedFiles = [];
        $errors = [];

        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                try {
                    $filename = $file->getClientOriginalName();
                    $filePath = $path ? $path . '/' . $filename : $filename;
                    
                    // Check if file already exists
                    if (Storage::disk('public')->exists($filePath)) {
                        $filename = $this->generateUniqueFilename($path, $filename);
                        $filePath = $path ? $path . '/' . $filename : $filename;
                    }
                    
                    Storage::disk('public')->putFileAs($path, $file, $filename);
                    $uploadedFiles[] = $filename;
                    
                } catch (\Exception $e) {
                    $errors[] = "Failed to upload {$file->getClientOriginalName()}: " . $e->getMessage();
                }
            }
        }

        $message = count($uploadedFiles) . ' file(s) uploaded successfully.';
        if (!empty($errors)) {
            $message .= ' Errors: ' . implode(', ', $errors);
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'uploaded' => $uploadedFiles
        ]);
    }

    /**
     * Create a new directory.
     */
    public function createDirectory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'path' => 'nullable|string',
        ]);

        $path = $this->sanitizePath($request->get('path', ''));
        $directoryName = Str::slug($request->name, '_');
        $fullPath = $path ? $path . '/' . $directoryName : $directoryName;

        try {
            if (Storage::disk('public')->exists($fullPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Directory already exists.'
                ], 400);
            }

            Storage::disk('public')->makeDirectory($fullPath);

            return response()->json([
                'success' => true,
                'message' => 'Directory created successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create directory: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rename a file or directory.
     */
    public function rename(Request $request)
    {
        $request->validate([
            'old_path' => 'required|string',
            'new_name' => 'required|string|max:255',
        ]);

        $oldPath = $this->sanitizePath($request->old_path);
        $newName = $request->new_name;
        $directory = dirname($oldPath);
        $newPath = $directory === '.' ? $newName : $directory . '/' . $newName;

        try {
            if (Storage::disk('public')->exists($newPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'A file or directory with that name already exists.'
                ], 400);
            }

            Storage::disk('public')->move($oldPath, $newPath);

            return response()->json([
                'success' => true,
                'message' => 'Renamed successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to rename: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete files or directories.
     */
    public function delete(Request $request)
    {
        $request->validate([
            'paths' => 'required|array',
            'paths.*' => 'string',
        ]);

        $deletedCount = 0;
        $errors = [];

        foreach ($request->paths as $path) {
            $path = $this->sanitizePath($path);
            
            try {
                if (Storage::disk('public')->exists($path)) {
                    Storage::disk('public')->delete($path);
                    $deletedCount++;
                } elseif (Storage::disk('public')->directoryExists($path)) {
                    Storage::disk('public')->deleteDirectory($path);
                    $deletedCount++;
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to delete {$path}: " . $e->getMessage();
            }
        }

        $message = "{$deletedCount} item(s) deleted successfully.";
        if (!empty($errors)) {
            $message .= ' Errors: ' . implode(', ', $errors);
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Download a file.
     */
    public function download(Request $request)
    {
        $path = $this->sanitizePath($request->get('path'));

        if (!Storage::disk('public')->exists($path)) {
            abort(404, 'File not found.');
        }

        return Storage::disk('public')->download($path);
    }

    /**
     * Get file information.
     */
    public function getFileInfo(Request $request)
    {
        $path = $this->sanitizePath($request->get('path'));

        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found.'
            ], 404);
        }

        $fileInfo = [
            'name' => basename($path),
            'path' => $path,
            'size' => Storage::disk('public')->size($path),
            'modified' => Storage::disk('public')->lastModified($path),
            'url' => Storage::disk('public')->url($path),
            'mime_type' => Storage::disk('public')->mimeType($path),
            'extension' => pathinfo($path, PATHINFO_EXTENSION),
        ];

        return response()->json([
            'success' => true,
            'file' => $fileInfo
        ]);
    }

    /**
     * Sanitize file path to prevent directory traversal.
     */
    private function sanitizePath($path)
    {
        // Remove any ../ or .\ patterns
        $path = preg_replace('/\.\.+[\/\\\\]/', '', $path);
        // Remove leading slashes
        $path = ltrim($path, '/\\');
        // Normalize slashes
        $path = str_replace('\\', '/', $path);
        
        return $path;
    }

    /**
     * Generate breadcrumbs for navigation.
     */
    private function generateBreadcrumbs($path)
    {
        $breadcrumbs = [['name' => 'Root', 'path' => '']];
        
        if (!empty($path)) {
            $parts = explode('/', $path);
            $currentPath = '';
            
            foreach ($parts as $part) {
                $currentPath .= ($currentPath ? '/' : '') . $part;
                $breadcrumbs[] = ['name' => $part, 'path' => $currentPath];
            }
        }
        
        return $breadcrumbs;
    }

    /**
     * Get file type based on extension.
     */
    private function getFileType($file)
    {
        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        $audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

        if (in_array($extension, $imageExtensions)) {
            return 'image';
        } elseif (in_array($extension, $videoExtensions)) {
            return 'video';
        } elseif (in_array($extension, $audioExtensions)) {
            return 'audio';
        } elseif (in_array($extension, $documentExtensions)) {
            return 'document';
        }

        return 'file';
    }

    /**
     * Format file size in human readable format.
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Generate a public shareable link for a file.
     */
    public function generatePublicLink(Request $request)
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->input('file_path');
        $fullPath = storage_path('app/public/' . $filePath);

        if (!file_exists($fullPath)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        // Generate a unique token for the file
        $token = Str::random(32);

        // Store the token in cache with file path (expires in 24 hours)
        Cache::put("file_share_{$token}", $filePath, now()->addHours(24));

        $publicUrl = route('public.file.download', ['token' => $token]);

        return response()->json([
            'success' => true,
            'public_url' => $publicUrl,
            'expires_at' => now()->addHours(24)->format('Y-m-d H:i:s'),
            'message' => 'Public link generated successfully'
        ]);
    }

    /**
     * Get directory size (approximate).
     */
    private function getDirectorySize($directory)
    {
        try {
            $files = Storage::disk('public')->allFiles($directory);
            $size = 0;
            
            foreach ($files as $file) {
                $size += Storage::disk('public')->size($file);
            }
            
            return $size;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Generate unique filename if file already exists.
     */
    private function generateUniqueFilename($path, $filename)
    {
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $counter = 1;
        
        do {
            $newFilename = $name . '_' . $counter . '.' . $extension;
            $fullPath = $path ? $path . '/' . $newFilename : $newFilename;
            $counter++;
        } while (Storage::disk('public')->exists($fullPath));
        
        return $newFilename;
    }
}
