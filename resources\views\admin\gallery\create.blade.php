@extends('layouts.admin')

@section('title', 'Add New Gallery Item')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Add New Gallery Item</h3>
                    <a href="{{ route('admin.gallery.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Gallery
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.gallery.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Gallery Item Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title *</label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="alt_text" class="form-label">Alt Text</label>
                                            <input type="text" class="form-control @error('alt_text') is-invalid @enderror" 
                                                   id="alt_text" name="alt_text" value="{{ old('alt_text') }}">
                                            @error('alt_text')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="type" class="form-label">Type *</label>
                                                    <select class="form-control @error('type') is-invalid @enderror" 
                                                            id="type" name="type" required>
                                                        <option value="image" {{ old('type', 'image') == 'image' ? 'selected' : '' }}>Image</option>
                                                        <option value="video" {{ old('type') == 'video' ? 'selected' : '' }}>Video</option>
                                                    </select>
                                                    @error('type')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category" class="form-label">Category *</label>
                                                    <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                                           id="category" name="category" value="{{ old('category') }}" required>
                                                    @error('category')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <!-- File Upload for Images -->
                                        <div class="mb-3" id="file-upload">
                                            <label for="file" class="form-label">Upload File *</label>
                                            <input type="file" class="form-control @error('file') is-invalid @enderror" 
                                                   id="file" name="file" accept="image/*,video/*">
                                            @error('file')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Video URL for Videos -->
                                        <div class="mb-3" id="video-url" style="display: none;">
                                            <label for="file_path" class="form-label">Video URL</label>
                                            <input type="url" class="form-control @error('file_path') is-invalid @enderror" 
                                                   id="file_path" name="file_path" value="{{ old('file_path') }}" 
                                                   placeholder="https://www.youtube.com/watch?v=...">
                                            @error('file_path')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" 
                                                       name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Item
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('admin.gallery.index') }}" class="btn btn-secondary me-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Gallery Item
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle between file upload and video URL based on type
document.getElementById('type').addEventListener('change', function() {
    const fileUpload = document.getElementById('file-upload');
    const videoUrl = document.getElementById('video-url');
    const fileInput = document.getElementById('file');
    
    if (this.value === 'video') {
        fileUpload.style.display = 'none';
        videoUrl.style.display = 'block';
        fileInput.removeAttribute('required');
    } else {
        fileUpload.style.display = 'block';
        videoUrl.style.display = 'none';
        fileInput.setAttribute('required', 'required');
    }
});
</script>
@endpush
@endsection
