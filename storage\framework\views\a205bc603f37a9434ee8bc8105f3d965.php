<?php $__env->startSection('title', 'File Manager'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">File Manager</h3>
                    <div>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-2"></i>Upload Files
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                            <i class="fas fa-folder-plus me-2"></i>New Folder
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.files.index')); ?>">
                                    <i class="fas fa-home"></i> Root
                                </a>
                            </li>
                            <?php if(request('path')): ?>
                                <?php
                                    $pathParts = explode('/', request('path'));
                                    $currentPath = '';
                                ?>
                                <?php $__currentLoopData = $pathParts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php $currentPath .= ($currentPath ? '/' : '') . $part; ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?php echo e(route('admin.files.index', ['path' => $currentPath])); ?>"><?php echo e($part); ?></a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </ol>
                    </nav>

                    <!-- File Actions -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="deselectAll()">
                                <i class="fas fa-square"></i> Deselect All
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()" disabled id="deleteBtn">
                                <i class="fas fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>

                    <!-- Files and Folders -->
                    <div class="row" id="filesList">
                        <!-- Folders -->
                        <?php if(isset($folders)): ?>
                            <?php $__currentLoopData = $folders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-2 col-sm-3 col-4 mb-3">
                                    <div class="card h-100 file-item" data-type="folder">
                                        <div class="card-body text-center p-2">
                                            <div class="position-relative">
                                                <input type="checkbox" class="file-checkbox position-absolute top-0 start-0" 
                                                       value="<?php echo e($folder['name']); ?>" data-type="folder">
                                                <a href="<?php echo e(route('admin.files.index', ['path' => $folder['path']])); ?>" 
                                                   class="text-decoration-none">
                                                    <i class="fas fa-folder fa-3x text-warning mb-2"></i>
                                                    <h6 class="card-title small"><?php echo e(Str::limit($folder['name'], 15)); ?></h6>
                                                </a>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="renameItem('<?php echo e($folder['name']); ?>', 'folder')">
                                                        <i class="fas fa-edit me-2"></i>Rename
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem('<?php echo e($folder['name']); ?>', 'folder')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <!-- Files -->
                        <?php if(isset($files)): ?>
                            <?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-2 col-sm-3 col-4 mb-3">
                                    <div class="card h-100 file-item" data-type="file">
                                        <div class="card-body text-center p-2">
                                            <div class="position-relative">
                                                <input type="checkbox" class="file-checkbox position-absolute top-0 start-0" 
                                                       value="<?php echo e($file['name']); ?>" data-type="file">
                                                
                                                <?php if(in_array($file['extension'], ['jpg', 'jpeg', 'png', 'gif', 'webp'])): ?>
                                                    <img src="<?php echo e($file['url']); ?>" 
                                                         class="img-fluid mb-2" 
                                                         style="max-height: 60px; object-fit: cover;"
                                                         alt="<?php echo e($file['name']); ?>">
                                                <?php else: ?>
                                                    <i class="fas fa-file fa-3x text-secondary mb-2"></i>
                                                <?php endif; ?>
                                                
                                                <h6 class="card-title small"><?php echo e(Str::limit($file['name'], 15)); ?></h6>
                                                <small class="text-muted"><?php echo e($file['size']); ?></small>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="<?php echo e($file['url']); ?>" target="_blank">
                                                        <i class="fas fa-eye me-2"></i>View
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="<?php echo e(route('admin.files.download', ['file' => $file['path']])); ?>">
                                                        <i class="fas fa-download me-2"></i>Download
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="copyShareableLink('<?php echo e($file['url']); ?>', '<?php echo e($file['name']); ?>')">
                                                        <i class="fas fa-share me-2"></i>Copy Shareable Link
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="generateDownloadLink('<?php echo e($file['path']); ?>', '<?php echo e($file['name']); ?>')">
                                                        <i class="fas fa-link me-2"></i>Generate Download Link
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item" href="#" onclick="renameItem('<?php echo e($file['name']); ?>', 'file')">
                                                        <i class="fas fa-edit me-2"></i>Rename
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem('<?php echo e($file['name']); ?>', 'file')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <?php if((!isset($folders) || empty($folders)) && (!isset($files) || empty($files))): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                    <h5>This folder is empty</h5>
                                    <p class="text-muted">Upload files or create folders to get started.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" action="<?php echo e(route('admin.files.upload')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="path" value="<?php echo e(request('path', '')); ?>">
                    <div class="mb-3">
                        <label for="files" class="form-label">Select Files</label>
                        <input type="file" class="form-control" id="files" name="files[]" multiple>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="uploadForm" class="btn btn-primary">Upload</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createFolderForm" action="<?php echo e(route('admin.files.create-directory')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="path" value="<?php echo e(request('path', '')); ?>">
                    <div class="mb-3">
                        <label for="folder_name" class="form-label">Folder Name</label>
                        <input type="text" class="form-control" id="folder_name" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="createFolderForm" class="btn btn-primary">Create</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// File selection
function selectAll() {
    document.querySelectorAll('.file-checkbox').forEach(cb => cb.checked = true);
    updateDeleteButton();
}

function deselectAll() {
    document.querySelectorAll('.file-checkbox').forEach(cb => cb.checked = false);
    updateDeleteButton();
}

function updateDeleteButton() {
    const selected = document.querySelectorAll('.file-checkbox:checked').length;
    document.getElementById('deleteBtn').disabled = selected === 0;
}

// Listen for checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('file-checkbox')) {
        updateDeleteButton();
    }
});

// Delete selected items
function deleteSelected() {
    const selected = Array.from(document.querySelectorAll('.file-checkbox:checked'))
        .map(cb => ({ name: cb.value, type: cb.dataset.type }));
    
    if (selected.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selected.length} item(s)?`)) {
        // Implementation for bulk delete
        console.log('Delete selected:', selected);
    }
}

// Rename item
function renameItem(name, type) {
    const newName = prompt(`Rename ${type}:`, name);
    if (newName && newName !== name) {
        // Implementation for rename
        console.log('Rename:', { name, newName, type });
    }
}

// Delete single item
function deleteItem(name, type) {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
        // Implementation for single delete
        console.log('Delete:', { name, type });
    }
}

// Copy shareable link to clipboard
function copyShareableLink(url, fileName) {
    navigator.clipboard.writeText(url).then(function() {
        showToast('success', `Shareable link for "${fileName}" copied to clipboard!`);
    }, function(err) {
        showToast('error', 'Failed to copy link to clipboard');
        console.error('Could not copy text: ', err);
    });
}

// Generate download link
function generateDownloadLink(filePath, fileName) {
    // Generate a public shareable link
    fetch('<?php echo e(route('admin.files.generate-link')); ?>', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            file_path: filePath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showDownloadLinkModal(data.public_url, fileName, data.expires_at);
        } else {
            showToast('error', data.message || 'Failed to generate public link');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('error', 'Failed to generate public link');
    });
}

// Show download link modal
function showDownloadLinkModal(downloadUrl, fileName, expiresAt = null) {
    const expirationInfo = expiresAt ? `
        <div class="alert alert-warning">
            <i class="fas fa-clock me-2"></i>
            <strong>Link expires:</strong> ${new Date(expiresAt).toLocaleString()}
        </div>
    ` : '';

    const modalHtml = `
        <div class="modal fade" id="downloadLinkModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-share-alt me-2"></i>Shareable Link for "${fileName}"
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Public Download Link:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="downloadLinkInput" value="${downloadUrl}" readonly>
                                <button class="btn btn-outline-success" type="button" onclick="copyDownloadLink()">
                                    <i class="fas fa-copy"></i> Copy Link
                                </button>
                            </div>
                        </div>
                        ${expirationInfo}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>How to use:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Share this link with anyone to allow direct file download</li>
                                <li>No login required for recipients</li>
                                <li>Link is secure and expires automatically</li>
                                <li>Perfect for sharing resources with clients or partners</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                        <a href="${downloadUrl}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download me-2"></i>Test Download
                        </a>
                        <button class="btn btn-success" onclick="copyDownloadLink()">
                            <i class="fas fa-share me-2"></i>Copy & Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('downloadLinkModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('downloadLinkModal'));
    modal.show();

    // Remove modal from DOM when hidden
    document.getElementById('downloadLinkModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Copy download link to clipboard
function copyDownloadLink() {
    const input = document.getElementById('downloadLinkInput');
    input.select();
    navigator.clipboard.writeText(input.value).then(function() {
        showToast('success', 'Download link copied to clipboard!');
    }, function(err) {
        showToast('error', 'Failed to copy link to clipboard');
        console.error('Could not copy text: ', err);
    });
}

// Show toast notification
function showToast(type, message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Remove toast from DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/files/index.blade.php ENDPATH**/ ?>