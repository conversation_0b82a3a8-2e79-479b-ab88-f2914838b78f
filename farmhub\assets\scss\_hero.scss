//Hero Section style
.banner-section {
	//Style one
	&.style-v1 {
		background: url(../img/banner/hero1-bg.png) no-repeat center center;
		background-size: cover;
		padding: 185px 0 110px;
		position: relative;
		&::before {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			content: "";
			background: linear-gradient(
				90deg,
				#1f4e3d 33.96%,
				rgba(72, 180, 141, 0.38) 100%
			);
		}
		.hero-thumbv01 {
			width: 100%;
			.mimg {
				width: 100%;
			}
		}
		.hero-contentv01 {
			h1 {
				font-size: 80px;
				font-weight: 700;
				line-height: 85px;
				letter-spacing: -2.5px;
				color: $white-clr;
				margin-bottom: 32px;
				span {
					font-size: 80px;
					font-weight: 300;
					line-height: 85px;
					letter-spacing: -2.5px;
					color: $white-clr;
					position: relative;
					display: inline-block;
					&::before {
						position: absolute;
						left: 0;
						top: 109%;
						width: 65%;
						border-radius: 10px;
						height: 5px;
						background: var(--p1-clr);
						content: "";
					}
				}
			}
			p {
				margin-bottom: 18px;
				color: $white-clr;
			}
			.hero-list {
				display: grid;
				gap: 14px;
				margin-bottom: 30px;
				li {
					display: flex;
					align-items: center;
					font-size: 18px;
					font-family: $heading-font;
					gap: 14px;
				}
			}
		}
		@include breakpoint(xl) {
			.container {
				max-width: 1450px;
				margin: 0 auto;
			}
		}
		@include breakpoint(max-xxl) {
			padding: 150px 0 80px;
		}
		@include breakpoint(max-xl) {
			.hero-contentv01 {
				h1 {
					font-size: 60px;
					line-height: 85px;
					letter-spacing: -2.5px;
					margin-bottom: 32px;
					span {
						font-size: 60px;
						line-height: 85px;
						letter-spacing: -2.5px;
						&::before {
							height: 5px;
							content: "";
						}
					}
				}
			}
		}
		@include breakpoint(max-lg) {
			padding: 140px 0 80px;
			.hero-contentv01 {
				h1 {
					font-size: 44px;
					line-height: 65px;
					letter-spacing: -2.5px;
					margin-bottom: 32px;
					span {
						font-size: 44px;
						line-height: 65px;
						letter-spacing: -2.5px;
						&::before {
							height: 3px;
							content: "";
						}
					}
				}
				p {
					margin-bottom: 14px;
					font-size: 14px;
				}
				.hero-list {
					gap: 4px;
					margin-bottom: 30px;
					li {
						font-size: 14px;
						gap: 8px;
					}
				}
			}
		}
		@include breakpoint(max-xs) {
			padding: 130px 0 80px;
			.hero-contentv01 {
				h1 {
					font-size: 35px;
					line-height: 42px;
					font-weight: 500;
					letter-spacing: -2.5px;
					margin-bottom: 32px;
					span {
						font-size: 38px;
						line-height: 55px;
						font-weight: 500;
						letter-spacing: -2.5px;
						&::before {
							height: 3px;
							content: "";
						}
					}
				}
				p {
					margin-bottom: 14px;
					font-size: 14px;
				}
				.hero-list {
					gap: 4px;
					margin-bottom: 30px;
					li {
						font-size: 14px;
						gap: 8px;
					}
				}
			}
		}
	}
	//Style Two
	&.style-v2 {
		background: $p100-clr;
		z-index: 1;
		.banner-wrapperv2 {
			.banner-contentv02 {
				padding: 130px 0 165px;
				h5 {
					color: $p1-clr;
					font-weight: 400;
					font-size: 20px;
					font-family: $sub-font;
					line-height: 36px;
					margin-bottom: 10px;
				}
				h1 {
					font-size: 80px;
					line-height: 85px;
					color: $p900-clr;
					margin-bottom: 20px;
					font-weight: 700;
					letter-spacing: -2.5px;
					span {
						font-weight: 200;
						font-size: 80px;
						line-height: 85px;
						letter-spacing: -2.5px;
					}
				}
				p {
					color: $p900-clr;
					margin-bottom: 40px;
					max-width: 578px;
				}
				.banner-buttonv2 {
					display: flex;
					align-items: center;
					gap: 40px;
					.header-help {
						display: flex;
						align-items: center;
						gap: 15px;
						.icon {
							width: 55px;
							height: 55px;
							background: $p1-clr;
							border-radius: 50%;
							i {
								color: $white-clr;
							}
						}
						span {
							.need {
								font-size: 16px;
								font-weight: 400;
								color: $p800-clr;
								line-height: 30px;
								font-family: $body-font;
							}
							.call {
								font-size: 20px;
								font-weight: 400;
								font-family: $heading-font !important;
								color: $p900-clr;
								display: block;
							}
						}
					}
				}
			}
		}
		.hero-v02-thumb {
			position: absolute;
			right: 0;
			bottom: 0;
			height: 100%;
			width: 55%;
			z-index: -1;
		}
		@include breakpoint(max-xxl) {
			.banner-wrapperv2 {
				.banner-contentv02 {
					padding: 110px 0 125px;
					h1 {
						font-size: 66px;
						line-height: 78px;
						margin-bottom: 20px;
						span {
							font-size: 66px;
							line-height: 78px;
						}
					}
					p {
						margin-bottom: 34px;
						max-width: 500px;
					}
					.banner-buttonv2 {
						display: flex;
						align-items: center;
						gap: 30px;
						.header-help {
							gap: 15px;
							.icon {
								width: 55px;
								height: 55px;
								background: $p1-clr;
								border-radius: 50%;
								i {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
		}
		@include breakpoint(max-lg) {
			.banner-wrapperv2 {
				.banner-contentv02 {
					padding: 80px 0 95px;
					h1 {
						font-size: 66px;
						line-height: 78px;
						margin-bottom: 20px;
						span {
							font-size: 66px;
							line-height: 78px;
						}
					}
					p {
						margin-bottom: 34px;
						max-width: 500px;
					}
					.banner-buttonv2 {
						display: flex;
						align-items: center;
						gap: 30px;
						.header-help {
							gap: 15px;
							.icon {
								width: 55px;
								height: 55px;
								background: $p1-clr;
								border-radius: 50%;
								i {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
			.hero-v02-thumb {
				width: 75%;
				opacity: 0.7;
			}
		}
		@include breakpoint(max-md) {
			.banner-wrapperv2 {
				.banner-contentv02 {
					padding: 80px 0 95px;
					h1 {
						font-size: 58px;
						line-height: 78px;
						margin-bottom: 20px;
						span {
							font-size: 58px;
							line-height: 78px;
						}
					}
					p {
						margin-bottom: 34px;
						max-width: 500px;
					}
					.banner-buttonv2 {
						gap: 20px;
						.header-help {
							gap: 10px;
							.icon {
								width: 55px;
								height: 55px;
								background: $p1-clr;
								border-radius: 50%;
								i {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
			.hero-v02-thumb {
				width: 100%;
				opacity: 0.3;
			}
		}
		@include breakpoint(max-sm) {
			.banner-wrapperv2 {
				.banner-contentv02 {
					padding: 70px 0 85px;
					h1 {
						font-size: 38px;
						line-height: 48px;
						margin-bottom: 20px;
						span {
							font-size: 38px;
							line-height: 48px;
						}
					}
					p {
						margin-bottom: 28px;
						max-width: 500px;
					}
					.banner-buttonv2 {
						flex-wrap: wrap;
						gap: 20px;
						.header-help {
							gap: 10px;
							.icon {
								width: 55px;
								height: 55px;
								background: $p1-clr;
								border-radius: 50%;
								i {
									font-size: 16px;
								}
							}
						}
					}
				}
			}
			.hero-v02-thumb {
				display: none;
			}
		}
	}
	//Style Three
	&.style-v3 {
		z-index: 1;
		@include breakpoint(xl) {
			.container {
				max-width: 1314px;
			}
		}
		background: url(../img/banner/hero3-bg.png) no-repeat center center;
		background-size: cover;
		padding-top: 80px;
		.hero-contentv03 {
			padding: 190px 0 177px;
			.sun-star {
				display: flex;
				align-items: center;
				gap: 10px;
				margin-bottom: 10px;
				font-family: $sub-font;
				font-size: 20px;
				font-weight: 400;
				color: $white-clr;
			}
			h1 {
				font-size: 80px;
				font-weight: 700;
				line-height: 85px;
				letter-spacing: -2.5px;
				font-family: $heading-font;
				margin-bottom: 28px;
				color: $white-clr;
				span {
					font-size: 80px;
					font-weight: 300;
					line-height: 85px;
					letter-spacing: -2.5px;
					font-family: $heading-font;
					color: $white-clr;
				}
			}
			p {
				color: $white-clr;
				font-size: 16px;
				line-height: 30px;
				margin-bottom: 40px;
				max-width: 600px;
			}
			.adjust-video {
				display: flex;
				align-items: center;
				gap: 32px;
				.video-area {
					display: flex;
					align-items: center;
					gap: 15px;
					.video-cmn {
						width: 50px;
						height: 50px;
						border-radius: 50%;
						background: $white-clr;
						position: relative;
						z-index: 1;
						&::before {
							position: absolute;
							left: 0px;
							top: 0px;
							border-radius: 50%;
							width: 50px;
							height: 50px;
							background: $white-clr;
							content: "";
							animation: ropple 2s linear infinite;
							z-index: -1;
						}
						i {
							color: $p1-clr;
							font-size: 18px;
						}
					}
					h5 {
						font-size: 20px;
						color: $white-clr;
						font-family: $heading-font;
						font-weight: 400;
					}
				}
			}
		}
		.hero-threthumb {
			position: absolute;
			bottom: 0;
			right: 0;
			border-top-left-radius: 142px;
			height: 84%;
			z-index: -1;
		}
		@media screen and (max-width: 1600px) {
			.hero-threthumb {
				height: 84%;
				width: 50%;
			}
		}
		@include breakpoint(max-xxl) {
			.hero-contentv03 {
				padding: 120px 0 140px;
				h1 {
					font-size: 72px;
					line-height: 75px;
					margin-bottom: 28px;
					span {
						font-size: 72px;
						line-height: 75px;
					}
				}
				p {
					color: $white-clr;
					font-size: 16px;
					line-height: 30px;
					margin-bottom: 40px;
					max-width: 590px;
				}
				.adjust-video {
					gap: 12px 20px;
					flex-wrap: wrap;
					.video-area {
						display: flex;
						align-items: center;
						gap: 15px;
						h5 {
							font-size: 16px;
						}
					}
				}
			}
			.hero-threthumb {
				height: 75%;
				width: initial;
			}
		}
		@include breakpoint(max-xl) {
			padding-top: 80px;
			.hero-contentv03 {
				padding: 110px 0 130px;
				h1 {
					font-size: 60px;
					line-height: 75px;
					margin-bottom: 28px;
					span {
						font-size: 60px;
						line-height: 75px;
					}
				}
				p {
					color: $white-clr;
					font-size: 16px;
					line-height: 30px;
					margin-bottom: 40px;
					max-width: 600px;
				}
				.adjust-video {
					gap: 12px 20px;
					flex-wrap: wrap;
					.video-area {
						display: flex;
						align-items: center;
						gap: 15px;
						h5 {
							font-size: 16px;
						}
					}
				}
			}
			.hero-threthumb {
				height: 84%;
				width: initial;
				opacity: 0.6;
			}
		}
		@include breakpoint(max-lg) {
			padding-top: 60px;
			.hero-contentv03 {
				padding: 110px 0 130px;
				h1 {
					font-size: 60px;
					line-height: 75px;
					margin-bottom: 28px;
					span {
						font-size: 60px;
						line-height: 75px;
					}
				}
				p {
					color: $white-clr;
					font-size: 16px;
					line-height: 30px;
					margin-bottom: 40px;
					max-width: 600px;
				}
				.adjust-video {
					gap: 12px 20px;
					flex-wrap: wrap;
					.video-area {
						display: flex;
						align-items: center;
						gap: 15px;
						h5 {
							font-size: 16px;
						}
					}
				}
			}
			.hero-threthumb {
				height: 84%;
				width: initial;
				opacity: 0.3;
			}
		}
		@include breakpoint(max-md) {
			padding-top: 60px;
			.hero-contentv03 {
				padding: 80px 0 100px;
				h1 {
					font-size: 39px;
					line-height: 48px;
					margin-bottom: 18px;
					span {
						font-size: 39px;
						line-height: 48px;
					}
				}
				p {
					color: $white-clr;
					font-size: 14px;
					line-height: 24px;
					margin-bottom: 24px;
				}
				.adjust-video {
					gap: 12px 20px;
					flex-wrap: wrap;
					.video-area {
						display: flex;
						align-items: center;
						gap: 15px;
						h5 {
							font-size: 16px;
						}
					}
				}
			}
			.hero-threthumb {
				height: 84%;
				width: initial;
				opacity: 0.3;
			}
		}
	}
}
@keyframes ropple {
	30% {
		transform: scale(1.01);
		opacity: 0.3;
	}
	80% {
		transform: scale(1.2);
		opacity: 0.3;
	}
	100% {
		transform: scale(1.3);
		opacity: 0;
	}
}
@keyframes zin {
	90% {
		transform: scale(2.2);
	}
}
//Hero Section style

//BreadCrumnd Banner
.breadcrumnd-banner {
	background: url(../img/about/bread-bg.png) no-repeat center center;
	background-size: cover;
	position: relative;
	padding: 150px 0;
	z-index: 1;
	&::before {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		content: "";
		background: linear-gradient(
			90deg,
			#1f4e3d 0%,
			rgba(45, 114, 89, 0.9) 52.5%,
			rgba(72, 180, 141, 0.58) 100%
		);
		z-index: -1;
	}
	.breadcrumnd-wrapp {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 24px 40px;
		.bread-content {
			h1 {
				font-size: 80px;
				font-weight: 700;
				color: $white-clr;
				line-height: 85px;
				font-family: $heading-font;
				margin-bottom: 20px;
			}
			.bread-listing {
				display: flex;
				align-items: center;
				gap: 18px;
				li,
				a {
					color: $white-clr;
					font-family: $heading-font;
					font-size: 20px;
					font-weight: 400;
				}
			}
		}
	}
	@include breakpoint(max-xl) {
		padding: 80px 0;
	}
	@include breakpoint(max-lg) {
		padding: 80px 0 90px;
		.breadcrumnd-wrapp {
			display: flex;
			flex-wrap: wrap;
			gap: 24px 24px;
			.bread-content {
				h1 {
					font-size: 60px;
					font-weight: 700;
					line-height: 65px;
					margin-bottom: 21px;
				}
				.bread-listing {
					gap: 15px;
					li,
					a {
						font-size: 18px;
					}
				}
			}
		}
		.bread-thumb {
			max-width: 180px;
			img {
				width: 100%;
			}
		}
	}
	@include breakpoint(max-md) {
		padding: 50px 0 60px;
		.breadcrumnd-wrapp {
			display: flex;
			flex-wrap: wrap;
			gap: 24px 24px;
			.bread-content {
				h1 {
					font-size: 48px;
					margin-bottom: 10px;
				}
			}
		}
		.bread-thumb {
			max-width: 120px;
			img {
				width: 100%;
			}
		}
	}
	@include breakpoint(max-sm) {
		.breadcrumnd-wrapp {
			.bread-content {
				h1 {
					font-size: 39px;
					margin-bottom: 10px;
				}
			}
		}
	}
}
//BreadCrumnd Banner
