@extends('layouts.admin')

@section('title', 'File Manager')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">File Manager</h3>
                    <div>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-2"></i>Upload Files
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                            <i class="fas fa-folder-plus me-2"></i>New Folder
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.files.index') }}">
                                    <i class="fas fa-home"></i> Root
                                </a>
                            </li>
                            @if(request('path'))
                                @php
                                    $pathParts = explode('/', request('path'));
                                    $currentPath = '';
                                @endphp
                                @foreach($pathParts as $part)
                                    @php $currentPath .= ($currentPath ? '/' : '') . $part; @endphp
                                    <li class="breadcrumb-item">
                                        <a href="{{ route('admin.files.index', ['path' => $currentPath]) }}">{{ $part }}</a>
                                    </li>
                                @endforeach
                            @endif
                        </ol>
                    </nav>

                    <!-- File Actions -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="deselectAll()">
                                <i class="fas fa-square"></i> Deselect All
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()" disabled id="deleteBtn">
                                <i class="fas fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>

                    <!-- Files and Folders -->
                    <div class="row" id="filesList">
                        <!-- Folders -->
                        @if(isset($folders))
                            @foreach($folders as $folder)
                                <div class="col-md-2 col-sm-3 col-4 mb-3">
                                    <div class="card h-100 file-item" data-type="folder">
                                        <div class="card-body text-center p-2">
                                            <div class="position-relative">
                                                <input type="checkbox" class="file-checkbox position-absolute top-0 start-0" 
                                                       value="{{ $folder['name'] }}" data-type="folder">
                                                <a href="{{ route('admin.files.index', ['path' => $folder['path']]) }}" 
                                                   class="text-decoration-none">
                                                    <i class="fas fa-folder fa-3x text-warning mb-2"></i>
                                                    <h6 class="card-title small">{{ Str::limit($folder['name'], 15) }}</h6>
                                                </a>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="renameItem('{{ $folder['name'] }}', 'folder')">
                                                        <i class="fas fa-edit me-2"></i>Rename
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem('{{ $folder['name'] }}', 'folder')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif

                        <!-- Files -->
                        @if(isset($files))
                            @foreach($files as $file)
                                <div class="col-md-2 col-sm-3 col-4 mb-3">
                                    <div class="card h-100 file-item" data-type="file">
                                        <div class="card-body text-center p-2">
                                            <div class="position-relative">
                                                <input type="checkbox" class="file-checkbox position-absolute top-0 start-0" 
                                                       value="{{ $file['name'] }}" data-type="file">
                                                
                                                @if(in_array($file['extension'], ['jpg', 'jpeg', 'png', 'gif', 'webp']))
                                                    <img src="{{ $file['url'] }}" 
                                                         class="img-fluid mb-2" 
                                                         style="max-height: 60px; object-fit: cover;"
                                                         alt="{{ $file['name'] }}">
                                                @else
                                                    <i class="fas fa-file fa-3x text-secondary mb-2"></i>
                                                @endif
                                                
                                                <h6 class="card-title small">{{ Str::limit($file['name'], 15) }}</h6>
                                                <small class="text-muted">{{ $file['size'] }}</small>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ $file['url'] }}" target="_blank">
                                                        <i class="fas fa-eye me-2"></i>View
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="{{ route('admin.files.download', ['file' => $file['path']]) }}">
                                                        <i class="fas fa-download me-2"></i>Download
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="renameItem('{{ $file['name'] }}', 'file')">
                                                        <i class="fas fa-edit me-2"></i>Rename
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem('{{ $file['name'] }}', 'file')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif

                        @if((!isset($folders) || empty($folders)) && (!isset($files) || empty($files)))
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                    <h5>This folder is empty</h5>
                                    <p class="text-muted">Upload files or create folders to get started.</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" action="{{ route('admin.files.upload') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="path" value="{{ request('path', '') }}">
                    <div class="mb-3">
                        <label for="files" class="form-label">Select Files</label>
                        <input type="file" class="form-control" id="files" name="files[]" multiple>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="uploadForm" class="btn btn-primary">Upload</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createFolderForm" action="{{ route('admin.files.create-directory') }}" method="POST">
                    @csrf
                    <input type="hidden" name="path" value="{{ request('path', '') }}">
                    <div class="mb-3">
                        <label for="folder_name" class="form-label">Folder Name</label>
                        <input type="text" class="form-control" id="folder_name" name="name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="createFolderForm" class="btn btn-primary">Create</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// File selection
function selectAll() {
    document.querySelectorAll('.file-checkbox').forEach(cb => cb.checked = true);
    updateDeleteButton();
}

function deselectAll() {
    document.querySelectorAll('.file-checkbox').forEach(cb => cb.checked = false);
    updateDeleteButton();
}

function updateDeleteButton() {
    const selected = document.querySelectorAll('.file-checkbox:checked').length;
    document.getElementById('deleteBtn').disabled = selected === 0;
}

// Listen for checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('file-checkbox')) {
        updateDeleteButton();
    }
});

// Delete selected items
function deleteSelected() {
    const selected = Array.from(document.querySelectorAll('.file-checkbox:checked'))
        .map(cb => ({ name: cb.value, type: cb.dataset.type }));
    
    if (selected.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selected.length} item(s)?`)) {
        // Implementation for bulk delete
        console.log('Delete selected:', selected);
    }
}

// Rename item
function renameItem(name, type) {
    const newName = prompt(`Rename ${type}:`, name);
    if (newName && newName !== name) {
        // Implementation for rename
        console.log('Rename:', { name, newName, type });
    }
}

// Delete single item
function deleteItem(name, type) {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
        // Implementation for single delete
        console.log('Delete:', { name, type });
    }
}
</script>
@endpush
@endsection
