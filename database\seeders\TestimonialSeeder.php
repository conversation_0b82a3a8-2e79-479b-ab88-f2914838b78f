<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some specific testimonials with predefined data
        $specificTestimonials = [
            [
                'name' => '<PERSON>',
                'position' => 'CEO',
                'company' => 'Tech Innovations Ltd',
                'testimonial' => 'The Leadership Development Program at PESCOT completely transformed my approach to management. I learned to lead with integrity and purpose, which has made a tremendous impact on my team and organization. The biblical principles combined with modern leadership methodologies provided a unique and powerful foundation for ethical leadership.',
                'rating' => 5,
                'status' => true,
                'featured' => true,
                'sort_order' => 1
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Pastor',
                'company' => 'Grace Community Church',
                'testimonial' => 'PESCOT helped me develop strong character foundations that guide my daily decisions. The Character Development Workshop was particularly impactful, providing practical tools for ethical decision-making that I now use in my ministry and personal life.',
                'rating' => 5,
                'status' => true,
                'featured' => true,
                'sort_order' => 2
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Marketing Director',
                'company' => 'Global Marketing Solutions',
                'testimonial' => 'The Family Life and Relationship Building program strengthened our family relationships and gave us tools to communicate better. Our family is closer than ever thanks to what we learned. The online format made it accessible despite our busy schedules.',
                'rating' => 5,
                'status' => true,
                'featured' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'David Thompson',
                'position' => 'Student',
                'company' => 'University of Excellence',
                'testimonial' => 'As a young person, the Youth Leadership Academy gave me confidence and direction. I now have the skills and character to make a positive difference in my community. The mentorship component was especially valuable.',
                'rating' => 5,
                'status' => true,
                'featured' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Lisa Williams',
                'position' => 'Non-Profit Director',
                'company' => 'Community Care Foundation',
                'testimonial' => 'The Community Outreach Training equipped me with practical skills to serve others effectively. I\'ve been able to start several successful community initiatives that are making a real difference in people\'s lives.',
                'rating' => 5,
                'status' => true,
                'featured' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Robert Davis',
                'position' => 'Manager',
                'company' => 'Financial Services Group',
                'testimonial' => 'PESCOT programs are exceptional in their depth and practical application. The instructors are knowledgeable and truly care about each participant\'s growth. The leadership principles I learned have helped me in every area of life.',
                'rating' => 4,
                'status' => true,
                'featured' => false,
                'sort_order' => 6
            ],
            [
                'name' => 'Jennifer Martinez',
                'position' => 'Teacher',
                'company' => 'Lincoln Elementary School',
                'testimonial' => 'I was skeptical at first, but the Character Development Workshop exceeded my expectations. The content was relevant and the impact has been lasting. I now incorporate these principles in my classroom management.',
                'rating' => 4,
                'status' => true,
                'featured' => false,
                'sort_order' => 7
            ],
            [
                'name' => 'James Wilson',
                'position' => 'Entrepreneur',
                'company' => 'Wilson Consulting',
                'testimonial' => 'The online format made it possible for me to participate despite my busy schedule. The quality of instruction was excellent and the community support was amazing. I\'ve applied these leadership principles in growing my business.',
                'rating' => 5,
                'status' => true,
                'featured' => false,
                'sort_order' => 8
            ],
            [
                'name' => 'Amanda Brown',
                'position' => 'Volunteer Coordinator',
                'company' => 'Local Community Center',
                'testimonial' => 'PESCOT doesn\'t just teach concepts - they help you apply them in real life. The mentorship and ongoing support have been invaluable to my personal growth and effectiveness in coordinating community volunteers.',
                'rating' => 4,
                'status' => true,
                'featured' => false,
                'sort_order' => 9
            ],
            [
                'name' => 'Kevin Anderson',
                'position' => 'Team Lead',
                'company' => 'Healthcare Solutions Inc',
                'testimonial' => 'The leadership principles I learned have helped me in every area of life - work, family, and community involvement. I highly recommend PESCOT programs to anyone seeking personal and professional growth.',
                'rating' => 5,
                'status' => true,
                'featured' => false,
                'sort_order' => 10
            ]
        ];

        // Create the specific testimonials
        foreach ($specificTestimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }

        // Create additional random testimonials
        Testimonial::factory(20)->create();
        
        // Create some testimonials with specific states
        Testimonial::factory(8)->featured()->perfectRating()->create();
        Testimonial::factory(12)->active()->create();
        Testimonial::factory(5)->rating(4)->create();
        Testimonial::factory(3)->inactive()->create();
    }
}