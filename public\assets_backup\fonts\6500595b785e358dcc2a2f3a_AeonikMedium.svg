<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20190801 at Wed Aug  1 17:26:50 2018
 By www-data
<PERSON> and <PERSON> (c) 2018
</metadata>
<defs>
<font id="6500595b785e358dcc2a2f3a_AeonikMedium" horiz-adv-x="520" >
  <font-face 
    font-family="6500595b785e358dcc2a2f3a_AeonikMedium"
    font-weight="500"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 1 5 3 3 3 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="512"
    cap-height="700"
    bbox="-388 -206 2155 910"
    underline-thickness="72"
    underline-position="-200"
    unicode-range="U+0020-1F10C"
  />
<missing-glyph horiz-adv-x="620" 
d="M80 700h460v-768h-460v768z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="554" 
d="M400.5 605.5q-18.5 18.5 -18.5 46.5t19 46.5t47 18.5t46.5 -18.5t18.5 -46.5t-18.5 -46.5t-46.5 -18.5q-29 0 -47.5 18.5zM502 0h-109v417h-198v-417h-109v417h-89v95h89v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h307v-512z" />
    <glyph glyph-name="f_f.liga" unicode="ff" horiz-adv-x="640" 
d="M513.5 593q-11.5 -12 -11.5 -38v-43h137v-95h-137v-417h-109v417h-198v-417h-109v417h-89v95h89v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h198v51q0 68 35 102.5t108 34.5h104v-95h-89q-26 0 -37.5 -12z" />
    <glyph glyph-name="f_f_i.liga" unicode="ffi" horiz-adv-x="861" 
d="M707.5 605.5q-18.5 18.5 -18.5 46.5t19 46.5t47 18.5t46.5 -18.5t18.5 -46.5t-18.5 -46.5t-46.5 -18.5q-29 0 -47.5 18.5zM809 0h-109v417h-198v-417h-109v417h-198v-417h-109v417h-89v95h89v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h198v51
q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h307v-512z" />
    <glyph glyph-name="f_f_t.liga" unicode="fft" horiz-adv-x="949" 
d="M943 95v-95h-104q-73 0 -106 34t-33 105v278h-198v-417h-109v417h-198v-417h-109v417h-89v95h89v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h198v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h198v143h108v-143h124
v-95h-124v-267q0 -30 12.5 -42.5t41.5 -12.5h81z" />
    <glyph glyph-name="f_t.liga" unicode="ft" horiz-adv-x="642" 
d="M636 95v-95h-104q-73 0 -106 34t-33 105v278h-198v-417h-109v417h-89v95h89v51q0 68 35 102.5t108 34.5h91v-95h-76q-26 0 -37.5 -12t-11.5 -38v-43h198v143h108v-143h124v-95h-124v-267q0 -30 12.5 -42.5t41.5 -12.5h81z" />
    <glyph glyph-name="t_t.liga" unicode="tt" horiz-adv-x="645" 
d="M639 95v-95h-104q-73 0 -106 34t-33 105v278h-201v-267q0 -30 12 -42.5t41 -12.5h67v-95h-89q-73 0 -106.5 34t-33.5 105v278h-89v95h89v143h109v-143h201v143h108v-143h124v-95h-124v-267q0 -30 12.5 -42.5t41.5 -12.5h81z" />
    <glyph glyph-name=".notdef" horiz-adv-x="620" 
d="M80 700h460v-768h-460v768z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="256" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="267" 
d="M93 213l-17 487h116l-17 -487h-82zM84 111q20 19 50 19t49.5 -19t19.5 -49t-19.5 -49t-49.5 -19t-50 19t-20 49t20 49z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="388" 
d="M42 647q0 25 15.5 41.5t43.5 16.5q29 0 44 -16t15 -41q0 -8 -1 -12l-23 -191h-70l-23 191q-1 4 -1 11zM229 647q0 25 15.5 41.5t43.5 16.5q29 0 44 -16t15 -41q0 -8 -1 -12l-23 -191h-70l-23 191q-1 4 -1 11z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="571" 
d="M181 606h89l-24 -156h121l24 156h89l-24 -156h84v-90h-97l-17 -114h92v-90h-105l-24 -156h-89l24 156h-120l-24 -156h-89l24 156h-84v90h97l16 114h-92v90h105zM233 360l-16 -114h120l17 114h-121z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="615" 
d="M467 371q49 -20 80 -59t31 -103q0 -56 -27.5 -101.5t-77 -75t-113.5 -36.5v-103h-97v105q-102 14 -162.5 77t-62.5 155l113 -2q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105
q0 82 57.5 137.5t152.5 66.5v103h97v-106q91 -15 146.5 -72.5t59.5 -139.5h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="915" 
d="M73 623q23 39 64 61t92 22t92 -22t64 -61t23 -88t-23 -87.5t-64 -60.5t-92 -22t-92 22t-64 60.5t-23 87.5t23 88zM166 0l478 700h107l-479 -700h-106zM292 598.5q-25 25.5 -63 25.5q-37 0 -62 -25.5t-25 -63.5t25 -63.5t62 -25.5q38 0 63 25.5t25 63.5t-25 63.5zM530 252
q23 39 64 61t92 22t92 -22t64 -61t23 -88q0 -48 -23 -87t-64 -61t-92 -22t-92 22t-64 61t-23 87q0 49 23 88zM748 228q-25 26 -62 26t-62.5 -26t-25.5 -64t25 -63t63 -25t62.5 25t24.5 63t-25 64z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="769" 
d="M711 92v-92h-83q-39 0 -68.5 9.5t-51.5 34.5l-27 30q-84 -80 -193 -80q-102 0 -167.5 48t-65.5 136q0 58 35.5 110.5t115.5 87.5q-36 45 -49 78.5t-13 78.5q0 49 27 88.5t74 62t106 22.5q50 0 90.5 -20.5t64 -56.5t23.5 -81q0 -59 -37 -106.5t-127 -84.5l128 -139
l104 160h108l-152 -226l32 -35q13 -15 26 -20t32 -5h68zM278 599q-26 -25 -26 -63q0 -32 12.5 -57t45.5 -62q114 48 114 128q0 35 -21.5 57t-57.5 22q-41 0 -67 -25zM423 137l-164 179l-2 2q-50 -26 -70.5 -55.5t-20.5 -71.5q0 -51 35 -79.5t92 -28.5q70 0 130 54z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="202" 
d="M42 647q0 25 15.5 41.5t43.5 16.5q29 0 44 -16t15 -41q0 -8 -1 -12l-23 -191h-70l-23 191q-1 4 -1 11z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="316" 
d="M325 700q-91 -124 -128 -223.5t-37 -226.5q0 -125 37 -224.5t128 -225.5h-113q-83 113 -121 219t-38 231q0 127 38 232t121 218h113z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="316" 
d="M232 482q38 -105 38 -232q0 -125 -38 -231t-121 -219h-113q91 126 128 225.5t37 224.5q0 127 -37 226.5t-128 223.5h113q83 -113 121 -218z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="429" 
d="M388 560l-119 -44l82 -93l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53z" />
    <glyph glyph-name="plus" unicode="+" 
d="M210 500h99v-169h160v-91h-160v-169h-99v169h-159v91h159v169z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="224" 
d="M168 96q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="434" 
d="M47 240v88h340v-88h-340z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="224" 
d="M62.5 111q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="330" 
d="M-33 -200l293 900h103l-293 -900h-103z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="635" 
d="M42 366q0 100 34.5 177.5t97.5 120t144 42.5q80 0 142.5 -42.5t97.5 -120t35 -177.5v-32q0 -104 -34.5 -181t-97 -118t-143.5 -41q-82 0 -144.5 41t-97 118t-34.5 181v32zM437.5 543.5q-44.5 65.5 -119.5 65.5t-120 -65.5t-45 -181.5v-24q0 -116 45 -181.5t120 -65.5
t119.5 65.5t44.5 181.5v24q0 116 -44.5 181.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="344" 
d="M275 700v-700h-110v473q-15 -9 -44 -9h-115v94h110q25 0 38 18t13 47v77h108z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="570" 
d="M304 318q55 45 82.5 84t27.5 85q0 59 -36 92t-98 33q-60 0 -96.5 -38.5t-36.5 -102.5v-13h-111v19q0 66 30.5 118t85.5 82t128 30q117 0 182 -58.5t65 -156.5q0 -71 -37 -124.5t-111 -115.5l-186 -155h338v-97h-496v89z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="605" 
d="M519.5 306.5q43.5 -42.5 43.5 -111.5q0 -60 -32.5 -105.5t-92 -70.5t-138.5 -25q-120 0 -190.5 61.5t-70.5 168.5h111q1 -64 44 -101t113 -37q62 0 102.5 32t40.5 85q0 50 -39.5 78.5t-112.5 28.5h-64v92h57q69 0 105.5 30t36.5 80q0 47 -36 74.5t-93 27.5
q-60 0 -98.5 -31.5t-39.5 -85.5h-109q1 97 67 153t176 56q76 0 131.5 -23t85 -65.5t29.5 -97.5q0 -63 -36.5 -103.5t-98.5 -54.5q65 -13 108.5 -55.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="617" 
d="M22 256l333 444h134v-442h106v-96h-106v-162h-109v162h-358v94zM133 258h247v328z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="592" 
d="M445.5 442q52.5 -30 82.5 -84t30 -123q0 -76 -34 -130t-92 -82t-128 -28q-77 0 -134.5 30t-89.5 81.5t-36 114.5h110q6 -57 47 -95t103 -38q37 0 69.5 17t52.5 50t20 80q0 64 -38.5 105t-102.5 41q-42 0 -83 -19t-58 -67l-107 2l21 403h436v-97h-334l-13 -195
q30 33 72 48.5t88 15.5q66 0 118.5 -30z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="610" 
d="M57 214q-15 61 -15 131q0 113 33 194t95 124t148 43q97 0 161.5 -53t77.5 -145h-110q-11 50 -46.5 78t-90.5 28q-79 0 -123 -64.5t-44 -174.5v-27q1 2 2.5 6.5t5.5 10.5q24 47 71.5 74t109.5 27q69 0 123 -29.5t83.5 -83t29.5 -123.5q0 -68 -31.5 -122t-89 -84
t-130.5 -30q-71 0 -126 29.5t-90 83.5q-29 46 -44 107zM420 334.5q-38 40.5 -101 40.5q-64 0 -104 -40.5t-40 -105.5q0 -64 39 -104t102 -40q64 0 103 40.5t39 104.5t-38 104.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="539" 
d="M12 700h503v-74l-328 -626h-120l326 603h-381v97z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="603" 
d="M519 306q43 -45 43 -117q0 -57 -31 -101.5t-89.5 -69t-139.5 -24.5t-139.5 24.5t-89.5 69t-31 101.5q0 71 42 116t108 59q-56 13 -93 54.5t-37 102.5q0 55 29 97t83.5 65.5t127.5 23.5t127.5 -23.5t83.5 -65.5t29 -97q0 -59 -38 -102t-95 -55q67 -13 110 -58zM207 593.5
q-37 -29.5 -37 -75.5q0 -47 37 -76t95 -29t94.5 29.5t36.5 75.5t-36.5 75.5t-94.5 29.5t-95 -29.5zM409 110q42 33 42 85q0 51 -42.5 85.5t-106.5 34.5q-65 0 -107.5 -34.5t-42.5 -85.5q0 -52 42.5 -85t107.5 -33t107 33z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="610" 
d="M552 489q16 -62 16 -134q0 -170 -72.5 -265.5t-203.5 -95.5q-100 0 -164 53t-77 145h110q12 -49 46.5 -77.5t90.5 -28.5q80 0 124 65t44 175v27q-9 -18 -11 -21q-24 -45 -70 -70.5t-107 -25.5q-70 0 -123.5 28.5t-83 81.5t-29.5 124q0 68 31.5 121.5t88.5 84t131 30.5
q70 0 124 -28.5t89 -80.5q30 -46 46 -108zM190 367q38 -40 101 -40q64 0 104 40t40 105q0 63 -39 103t-102 40q-64 0 -103 -40t-39 -104t38 -104z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="224" 
d="M62.5 474q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49zM62.5 111q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="224" 
d="M168 96q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33zM58.5 474q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M67 233v102l386 167v-105l-266 -113l266 -113v-105z" />
    <glyph glyph-name="equal" unicode="=" 
d="M61 337v92h398v-92h-398zM61 126v91h398v-91h-398z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M453 335v-102l-386 -167v105l266 113l-266 113v105z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="522" 
d="M57.5 590.5q28.5 53.5 82 84.5t125.5 31q68 0 119 -23.5t78.5 -65t27.5 -93.5q0 -62 -26 -100.5t-74 -72.5q-48 -33 -68 -56t-20 -57v-20h-102v32q0 38 13.5 65t35 45.5t58.5 42.5q36 24 53.5 50t17.5 60q0 43 -31 70t-81 27q-58 0 -92.5 -38.5t-34.5 -100.5h-110
q0 66 28.5 119.5zM204.5 109.5q19.5 18.5 49.5 18.5t49 -18.5t19 -48.5t-19 -48.5t-49 -18.5t-49.5 18.5t-19.5 48.5t19.5 48.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="981" 
d="M110 467q62 100 168 156.5t233 56.5q123 0 219.5 -46.5t150 -129.5t53.5 -188q0 -78 -30 -139t-82 -95t-117 -34q-44 0 -71.5 22t-33.5 55l-2 14q-19 -41 -61.5 -65.5t-94.5 -24.5q-47 0 -86.5 23.5t-62.5 67t-23 100.5q0 63 27.5 116t74.5 83.5t103 30.5q53 0 86.5 -20
t43.5 -50l17 62h85l-30 -282q-1 -5 -1 -14q0 -50 49 -50q31 0 58.5 26.5t44 73.5t16.5 103q0 85 -41 150t-117 100.5t-177 35.5t-186.5 -47.5t-135.5 -130t-50 -181.5q0 -102 43.5 -175.5t125.5 -112t194 -38.5q128 0 225 57l-3 -82q-38 -24 -99 -38t-132 -14
q-129 0 -229.5 49t-157 140t-56.5 212q0 123 62 223zM568 365q-27 28 -71 28q-35 0 -64 -20t-46 -53.5t-17 -73.5q0 -54 28.5 -86t72.5 -32q34 0 62.5 19.5t45 55.5t16.5 83q0 51 -27 79z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="633" 
d="M321 700q115 0 178.5 -48t63.5 -134q0 -63 -33.5 -103.5t-96.5 -54.5q157 -27 157 -165q0 -91 -67.5 -143t-192.5 -52h-266v700h257zM179 403h145q59 0 91.5 27t32.5 74t-32.5 73.5t-91.5 26.5h-145v-201zM179 97h152q68 0 104.5 27.5t36.5 76.5q0 51 -37 79t-104 28
h-152v-211z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="706" 
d="M78.5 536.5q41.5 80.5 118 125t178.5 44.5q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38t65.5 105h115q-11 -74 -50.5 -129t-102.5 -85t-143 -30
q-102 0 -178.5 44t-118 124.5t-41.5 187.5q0 106 41.5 186.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="677" 
d="M64 700h223q107 0 187 -44t123 -123t43 -183q0 -103 -43 -182.5t-123 -123.5t-187 -44h-223v700zM287 100q113 0 174 67.5t61 182.5t-61 182.5t-174 67.5h-108v-500h108z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="535" 
d="M64 700h447v-102h-332v-203h274v-99h-274v-296h-115v700z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="748" 
d="M78.5 536.5q41.5 80.5 118.5 125t180 44.5q121 0 201 -66t98 -178h-114q-17 67 -66.5 105t-121.5 38q-66 0 -115.5 -32.5t-76.5 -90t-27 -132.5t26.5 -132.5t76 -90t115.5 -32.5q84 0 140.5 46t60.5 128v17h-194v97h304v-383h-87l-12 100q-30 -44 -85.5 -75t-131.5 -31
q-97 0 -172 44.5t-117 125.5t-42 186q0 106 41.5 186.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="683" 
d="M64 701h115v-295h325v295h115v-701h-115v304h-325v-304h-115v701z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="371" 
d="M307 700v-526q0 -84 -45.5 -129t-129.5 -45h-108v103h82q45 0 65.5 21t20.5 67v509h115z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="658" 
d="M64 700h115v-361l321 361h140l-257 -292l276 -408h-139l-219 320l-122 -137v-183h-115v700z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="504" 
d="M64 0v700h115v-598h301v-102h-416z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="867" 
d="M64 700h149l224 -551l220 551h146v-700h-111v515l-210 -515h-97l-210 515v-515h-111v700z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="687" 
d="M64 0v700h105l341 -496v496h113v-700h-105l-341 496v-496h-113z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="606" 
d="M340 700q72 0 127 -27.5t85 -78t30 -117.5t-30 -117.5t-85 -78t-127 -27.5h-161v-254h-115v700h276zM179 354h151q62 0 98.5 33.5t36.5 89.5q0 57 -36 90t-98 33h-152v-246z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="759" 
d="M753 101v-101h-68q-41 0 -64.5 11.5t-43.5 37.5l-3 4q-84 -59 -197 -59q-99 0 -176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183q0 -66 -19 -124.5t-54 -105.5q19 -19 48 -19h61zM508 138l-15 20q-13 18 -26 26t-30 8h-62v100h75
q37 0 58.5 -11.5t40.5 -37.5l21 -27q29 58 29 134q0 73 -28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5t-78.5 -91t-28 -131.5t28 -131.5t78.5 -91t115.5 -32.5q75 0 131 43z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="625" 
d="M340 700q73 0 128 -27.5t85.5 -78t30.5 -117.5q0 -70 -37 -123t-99 -75l148 -279h-128l-133 254h-156v-254h-115v700h276zM179 354h152q62 0 98.5 33.5t36.5 89.5t-36.5 89.5t-98.5 33.5h-152v-246z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="615" 
d="M151 228q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5
q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32t80 -59t31 -103q0 -62 -33 -111t-92 -76.5t-134 -27.5q-83 0 -146 30t-98.5 83.5t-36.5 122.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="594" 
d="M24 700h547v-102h-216v-598h-115v598h-216v102z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="639" 
d="M5 700h122l193 -562l195 562h119l-245 -700h-138z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="991" 
d="M19 700l120 1l149 -555l156 554h109l151 -554l151 554h117l-200 -700h-135l-143 515l-140 -515h-135z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="656" 
d="M257 358l-221 342h137l160 -249l162 249h126l-221 -342l234 -358h-139l-173 264l-174 -264h-125z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="647" 
d="M5 701h135l184 -321l189 321h129l-261 -442v-259h-115v259z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="617" 
d="M40 700h537v-79l-392 -520h403v-101h-559v79l391 520h-380v101z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="313" 
d="M58 701h241v-96h-140v-710h140v-95h-241v901z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="330" 
d="M363 -200h-103l-293 900h103z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="313" 
d="M255 -201h-241v96h140v710h-140v95h241v-901z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="519" 
d="M38 388l179 312h85l180 -312h-99l-126 217l-125 -217h-94z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="503" 
d="M55 -109v103h393v-103h-393z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="400" 
d="M37 700h125l77 -139h-83z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="603" 
d="M452 486.5q55 -31.5 86 -90.5t31 -140q0 -77 -31 -136.5t-86 -92.5t-125 -33q-57 0 -99 21.5t-68 58.5l-13 -74h-94v700h108v-264q61 82 166 82q70 0 125 -31.5zM417.5 132.5q41.5 47.5 41.5 123.5q0 75 -41.5 122.5t-108.5 47.5t-108 -47t-41 -122q0 -76 41 -124
t108 -48t108.5 47.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="559" 
d="M448 44.5q-62 -50.5 -159 -50.5q-76 0 -134 32.5t-89.5 92.5t-31.5 138t32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5t43 71.5h110q-15 -89 -77 -139.5z
" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="603" 
d="M550 700v-700h-94l-13 77q-61 -83 -167 -83q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q56 0 98 -20.5t68 -56.5v259h108zM402 132q41 47 41 122q0 77 -41 124.5t-108 47.5t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="333" 
d="M86 512v51q0 68 35 102.5t108 34.5h104v-95h-89q-26 0 -37.5 -12t-11.5 -38v-43h137v-95h-137v-417h-109v417h-89v95h89z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="603" 
d="M550 512v-491q0 -108 -64 -167.5t-198 -59.5q-104 0 -169 46.5t-72 131.5h110q10 -43 46 -66.5t93 -23.5q147 0 147 143v57q-56 -84 -167 -84q-70 0 -125 31.5t-86 90t-31 138.5q0 77 31.5 136t86.5 91.5t124 32.5q58 0 100 -23t68 -63l12 80h94zM402 135.5
q41 46.5 41 120.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -122t41 -122t108 -47t108 46.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-108v276q0 73 -31.5 111t-91.5 38q-64 0 -100.5 -44.5t-36.5 -121.5v-259h-108v700h108v-262q58 80 161 80q92 0 149.5 -56z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="214" 
d="M154 698.5q19 -18.5 19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5t47 18.5t47 -18.5zM53 512h108v-512h-108v512z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="219" 
d="M159.5 698.5q18.5 -18.5 18.5 -46.5t-18.5 -46.5t-46.5 -18.5q-29 0 -47.5 18.5t-18.5 46.5t19 46.5t47 18.5t46.5 -18.5zM5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-80v95h58z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="537" 
d="M53 700h108v-438l234 250h125l-191 -206l203 -306h-128l-152 227l-91 -96v-131h-108v700z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="214" 
d="M53 700h108v-700h-108v700z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="879" 
d="M781 463.5q53 -54.5 53 -174.5v-289h-108v288q0 67 -27 102t-80 35q-56 0 -88.5 -39.5t-32.5 -108.5v-277h-109v288q0 68 -27.5 102.5t-80.5 34.5q-55 0 -87.5 -41t-32.5 -110v-274h-108v512h94l12 -67q55 72 150 73q54 0 95 -23t63 -70q27 44 71 68.5t106 24.5
q84 0 137 -54.5z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="603" 
d="M452 486.5q55 -31.5 86 -91t31 -139.5q0 -78 -31 -137.5t-86 -92t-125 -32.5q-57 0 -99 21t-67 57v-272h-108v712h94l13 -76q62 82 167 82q70 0 125 -31.5zM417.5 133q41.5 48 41.5 123q0 76 -41.5 123t-108.5 47t-108 -46.5t-41 -121.5q0 -77 41 -125t108 -48t108.5 48z
" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="603" 
d="M550 512v-712h-108v276q-61 -82 -166 -82q-70 0 -125 31.5t-86 90.5t-31 140q0 77 31.5 136.5t86.5 92.5t124 33q58 0 100.5 -21.5t67.5 -57.5l12 73h94zM402 132.5q41 47.5 41 122.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -123t41 -123.5t108 -47.5t108 47.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="352" 
d="M336 512v-101h-48q-67 0 -97 -43.5t-30 -110.5v-257h-108v512h96l12 -77q22 36 57 56.5t94 20.5h24z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="517" 
d="M139 169q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5
t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -48 -28 -84.5t-77 -56t-110 -19.5q-103 0 -166.5 46.5t-65.5 128.5h104z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="335" 
d="M86 417h-89v95h89v143h109v-143h123v-95h-123v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106.5 34t-33.5 105v278z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="514" 
d="M2 512h116l141 -396l139 396h114l-189 -512h-132z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="798" 
d="M4 512h114l117 -392l117 392h99l112 -392l121 392h110l-170 -512h-116l-108 348l-112 -348l-117 -1z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="526" 
d="M198 262l-181 250h126l123 -173l125 173h118l-182 -250l193 -262h-126l-135 186l-136 -186h-117z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="516" 
d="M117 512l143 -395l139 395h115l-232 -592q-18 -46 -33 -70.5t-37 -37t-59 -12.5h-116v95h76q31 0 43 9.5t25 42.5l21 49l-200 516h115z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="488" 
d="M34 512h419v-77l-286 -340h299v-95h-444v77l286 340h-274v95z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="335" 
d="M222.5 583.5q-22.5 -21.5 -22.5 -60.5q0 -15 3 -33l20 -88q5 -23 5 -38q0 -42 -26.5 -72t-73.5 -42q47 -8 74 -37t27 -72q0 -19 -6 -43l-20 -88q-3 -18 -3 -34q0 -39 23 -60t65 -21l33 -1v-94h-29q-91 0 -143 41t-52 113q0 23 4 43l18 94q3 13 3 36q0 73 -96 76v94
q47 2 72 21t25 55q0 17 -4 36l-18 94q-4 20 -4 43q0 72 52 113t143 41h29v-94l-33 -1q-43 0 -65.5 -21.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="237" 
d="M64 700h109v-900h-109v900z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="335" 
d="M213 127q0 -23 3 -36l18 -94q4 -20 4 -43q0 -72 -52 -113t-143 -41h-29v94l33 1q42 0 65 21t23 60q0 16 -3 34l-20 88q-6 24 -6 43q0 43 27 72t74 37q-47 12 -73.5 42t-26.5 72q0 15 5 38l20 88q3 18 3 33q0 39 -22.5 60.5t-65.5 21.5l-33 1v94h29q91 0 143 -41t52 -113
q0 -23 -4 -43l-18 -94q-4 -19 -4 -36q0 -36 25 -55t72 -21v-94q-96 -3 -96 -76z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M51 193q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="267" 
d="M196.5 687q19.5 -19 19.5 -49t-19.5 -49t-49.5 -19t-50 19t-20 49t20 49t50 19t49.5 -19zM106 487h82l17 -487h-116z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="559" 
d="M372 204.5q33 26.5 43 71.5h110q-13 -75 -59.5 -123.5t-121.5 -61.5v-91h-97v89q-98 13 -155.5 83t-57.5 177q0 105 57.5 174.5t155.5 83.5v93h97v-94q74 -13 121 -61t60 -123h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5
q50 0 83 26.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="618" 
d="M114 366l-6 23q-8 33 -12 53.5t-4 44.5q0 64 31.5 113.5t85.5 77.5t120 28q112 0 178.5 -61t74.5 -170h-109q-8 63 -42.5 98t-96.5 35q-59 0 -95.5 -35t-36.5 -93q0 -18 5 -41.5t11 -46.5l7 -26h203v-85h-193q1 -7 1 -21q0 -40 -17 -82.5t-47 -74.5l408 -2v-101h-528v96
q37 40 55.5 88t16.5 97h-88v85h78z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="641" 
d="M192 555q57 38 129 38q71 0 128 -38l73 72l76 -75l-71 -71q43 -58 43 -138q0 -70 -37 -130l65 -65l-76 -75l-64 64q-60 -44 -137 -44q-78 0 -138 44l-64 -64l-76 76l65 65q-37 60 -37 129q0 80 43 138l-71 71l76 75zM209.5 231q43.5 -44 111.5 -44q67 0 110.5 44
t43.5 112t-43.5 112t-110.5 44q-68 0 -111.5 -44t-43.5 -112t43.5 -112z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="675" 
d="M643 701l-218 -375h143v-80h-182v-72h182v-80h-182v-94h-115v94h-183v80h183v72h-183v80h143l-223 375h136l184 -321l187 321h128z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="237" 
d="M64 700h109v-404h-109v404zM64 201h109v-401h-109v401z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="609" 
d="M520 220.5q-28 -33.5 -80 -52.5q32 -20 48.5 -47.5t16.5 -68.5q0 -71 -55 -114.5t-146 -43.5q-88 0 -144.5 42t-60.5 113h107q2 -35 28.5 -54.5t69.5 -19.5t69 18t26 51q0 37 -25.5 54t-83.5 33q-77 22 -122.5 40t-75.5 49.5t-30 79.5q0 92 104 134q-30 20 -46.5 47.5
t-16.5 66.5q0 71 56 114.5t148 43.5q87 0 142.5 -41.5t59.5 -113.5h-106q-2 36 -28 55t-68 19q-44 0 -71 -18t-26 -50q0 -37 26 -54.5t83 -33.5q78 -22 123 -40t75.5 -49t30.5 -79q0 -47 -28 -80.5zM357 203q78 25 78 89q0 25 -14.5 41t-41 26t-75.5 23l-22 6l-28 8
q-80 -24 -80 -88q0 -25 14.5 -41t41 -26t75.5 -23z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="400" 
d="M58 680.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM253 680.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="806" 
d="M86 531q48 81 131 128t185 47t186 -47t132 -128.5t48 -180.5t-48 -180.5t-132 -128.5t-186 -47t-185 47t-131 128t-48 181t48 181zM661 501q-39 68 -107 107t-152 39q-83 0 -150.5 -39t-106.5 -107t-39 -151t39 -151t106.5 -107t150.5 -39q84 0 152 39t107 107t39 151
t-39 151zM526 185q-49 -40 -123 -40q-60 0 -104.5 28t-68 75t-23.5 103t23.5 103t68 75t104.5 28q71 0 121.5 -40t60.5 -110h-77q-9 36 -36.5 58.5t-66.5 22.5q-57 0 -88 -39t-31 -98q0 -58 31.5 -97.5t87.5 -39.5q40 0 67 22t37 59h76q-10 -70 -59 -110z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="426" 
d="M90 545.5q37 26.5 107 26.5h74v18q0 26 -18 41t-51 15q-26 0 -43 -13t-19 -34h-79q4 50 43 78.5t101 28.5q67 0 104.5 -31t37.5 -88v-118q0 -24 24 -24h12v-61h-39q-60 0 -60 53v6q-14 -29 -42 -46t-68 -17q-54 0 -87.5 23.5t-33.5 66.5q0 49 37 75.5zM271 523h-76
q-29 0 -45.5 -13t-16.5 -34q0 -19 14 -29.5t39 -10.5q38 0 61.5 24t23.5 58v5zM50 309h333v-68h-333v68z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="594" 
d="M308 512l-154 -256l154 -256h-109l-156 256l156 256h109zM550 511l-154 -256l154 -256h-109l-156 256l156 256h109z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M51 353h379v-255h-96v163h-283v92z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="515" 
d="M71.5 602.5q28.5 48.5 77.5 76.5t108 28q60 0 109.5 -28t78 -76t28.5 -107q0 -58 -28.5 -106t-78 -75.5t-109.5 -27.5q-59 0 -108 27.5t-77.5 75t-28.5 105.5q0 59 28.5 107.5zM400.5 581q-21.5 38 -59 59.5t-84.5 21.5q-46 0 -83.5 -21.5t-59 -60t-21.5 -85.5
q0 -46 21.5 -83.5t59 -59t83.5 -21.5q47 0 84.5 21.5t59 59.5t21.5 84q0 47 -21.5 85zM266 610q41 0 65 -20t24 -54q0 -23 -12.5 -40.5t-35.5 -25.5l50 -88h-55l-44 78h-32v-78h-50v228h90zM226 501h37q18 0 29 9.5t11 25.5t-10.5 24.5t-29.5 8.5h-37v-68z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="400" 
d="M55 599v79h291v-79h-291z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="431" 
d="M65.5 624.5q22.5 37.5 62.5 59t88 21.5t88 -21.5t62.5 -59t22.5 -84.5q0 -46 -22.5 -83.5t-62.5 -59t-88 -21.5t-88 21.5t-62.5 59t-22.5 83.5q0 47 22.5 84.5zM281 605.5q-26 25.5 -65 25.5t-65 -25.5t-26 -65.5q0 -39 26 -64.5t65 -25.5t65 25.5t26 64.5q0 40 -26 65.5
z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M51 0v91h418v-91h-418zM210 524h99v-169h160v-91h-160v-169h-99v169h-159v91h159v169z" />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="320" 
d="M21 675q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="330" 
d="M66 776.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="400" 
d="M161 562l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" horiz-adv-x="577" 
d="M162 -200h-109v711h108v-265q0 -77 31.5 -117.5t91.5 -40.5t96 47t36 128v248h108v-511h-97l-9 74q-26 -38 -62.5 -59t-88.5 -21q-33 0 -63.5 13t-41.5 29v-236z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="705" 
d="M367 227h-89q-76 0 -133.5 29.5t-89 83t-31.5 123.5t31.5 123.5t89 83.5t132.5 30h193v-700h-103v227zM538 700h103v-700h-103v700z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="224" 
d="M62.5 344q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="400" 
d="M235 24l-16 -61h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l27 111h57z" />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="190" 
d="M83 437v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="422" 
d="M328.5 425q-44.5 -45 -118.5 -45q-73 0 -117 44.5t-44 118.5t44 119t117 45t118 -45t45 -119q0 -73 -44.5 -118zM151 472q22 -27 59 -27t60 27t23 71t-23 71.5t-60 27.5t-59 -27.5t-22 -71.5t22 -71zM48 309h326v-68h-326v68z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="594" 
d="M286 512h109l156 -256l-156 -256h-109l154 256zM44 511h109l156 -256l-156 -256h-109l154 256z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="642" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM499 364h92v-212h56v-66h-56v-86h-73v86h-179v66zM518 290l-105 -138h105v138z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="680" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM387 239q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5
t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="782" 
d="M66 676.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5zM160 0l444 700h86l-443 -700h-87zM639 364h92v-212h56v-66h-56v-86h-73v86h-179v66zM658 290l-105 -138h105v138z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="519" 
d="M315.5 590.5q-19.5 -18.5 -49.5 -18.5t-49 18.5t-19 48.5t19 48.5t49 18.5t49.5 -18.5t19.5 -48.5t-19.5 -48.5zM463.5 109.5q-28.5 -53.5 -82.5 -84.5t-126 -31q-68 0 -119 23.5t-78.5 65t-27.5 93.5q0 63 26.5 101t74.5 72q47 33 67 56t20 57v20h102v-32
q0 -38 -13.5 -65t-35 -45.5t-58.5 -42.5q-36 -24 -53.5 -50t-17.5 -60q0 -43 31 -70t81 -27q58 0 92.5 38.5t34.5 100.5h111q0 -66 -28.5 -119.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM176 896h125l77 -139h-83z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM300 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM339 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM264.5 812q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22
q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM197 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM392 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5
q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="676" 
d="M668 0h-123l-66 173h-287l-65 -173h-119l256 684q-22 16 -35 40.5t-13 52.5q0 47 35.5 81t86.5 34t86.5 -34t35.5 -81q0 -28 -12.5 -52t-34.5 -41zM294.5 820.5q-17.5 -17.5 -17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5t-43.5 17.5
t-43.5 -17.5zM224 271h223l-112 305z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="975" 
d="M590 102h354v-102h-469v164h-256l-102 -164h-119l418 700h516v-102h-342v-194h282v-99h-282v-203zM271 260h204v343z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="706" 
d="M490.5 133q48.5 38 65.5 105h115q-16 -104 -87 -169t-179 -74l-8 -32h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l20 82q-95 5 -165.5 50.5t-108.5 124t-38 180.5q0 106 41.5 186.5t118 125t178.5 44.5
q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM128 896h125l77 -139h-83z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM252 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM291 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM149 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM344 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5
t18 43.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM-42 896h125l77 -139h-83z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM82 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM121 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM-21 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM174 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="690" 
d="M490 656q80 -44 123 -123t43 -183q0 -103 -43 -182.5t-123 -123.5t-187 -44h-223v312h-83v87h83v301h223q107 0 187 -44zM477 167.5q61 67.5 61 182.5t-61 182.5t-174 67.5h-108v-201h129v-87h-129v-212h108q113 0 174 67.5z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="687" 
d="M64 0v700h105l341 -496v496h113v-700h-105l-341 496v-496h-113zM261.5 812q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5
q-16 0 -25.5 -13z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM214 896h125l77 -139h-83z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM338 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM377 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM302.5 812q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM235 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM430 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M120 491l143 -142l139 140l64 -64l-139 -140l142 -142l-70 -69l-142 142l-139 -140l-64 65l140 139l-142 142z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="754" 
d="M647 573q34 -46 52 -103t18 -120q0 -102 -43 -183t-120.5 -127t-176.5 -46q-61 0 -115 18.5t-97 53.5l-68 -72l-60 59l70 74q-70 95 -70 223q0 102 43 183t120.5 127t176.5 46q61 0 115 -18.5t97 -53.5l69 72l59 -59zM186 210l330 347q-56 48 -139 48q-65 0 -115.5 -32.5
t-78.5 -91t-28 -131.5q0 -80 31 -140zM568 490l-329 -347q56 -48 138 -48q65 0 115.5 32.5t78.5 91t28 131.5q0 80 -31 140z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM173 896h125l77 -139h-83z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM297 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM336 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM194 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM389 876.5
q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="647" 
d="M5 701h135l184 -321l189 321h129l-261 -442v-259h-115v259zM286 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="607" 
d="M179 700v-115h147q121 0 188.5 -59t67.5 -165t-69 -164.5t-188 -58.5h-146v-138h-115v700h115zM179 239h144q68 0 105.5 31.5t37.5 90.5q0 61 -37.5 92.5t-104.5 31.5h-145v-246z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="604" 
d="M245 11v87q43 -16 95 -16q56 0 91.5 31t35.5 83q0 58 -45 88.5t-120 30.5q-8 0 -30 -2v86q72 1 108 32.5t36 80.5q0 47 -32 75t-86 28q-57 0 -91 -35t-34 -94v-486h-109v479q0 74 31 125t84 76.5t119 25.5q67 0 119 -22.5t80.5 -64t28.5 -95.5q-1 -56 -31 -96.5
t-77 -56.5q76 -16 117.5 -59.5t41.5 -117.5q0 -61 -29 -106.5t-80.5 -69.5t-115.5 -24q-54 0 -107 17z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM107 708h125l77 -139h-83z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM231 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM270 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM195.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22
q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM128 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM323 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5
t-18 43.5t18 43.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM183.5 753q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM313.5 715.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45
t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="906" 
d="M484 233v-6q3 -66 41.5 -105.5t101.5 -39.5q49 0 83.5 24t45.5 66h108q-14 -78 -76 -128t-153 -50q-68 0 -120.5 27t-83.5 77q-32 -49 -83 -76.5t-119 -27.5q-87 0 -141.5 40t-54.5 110q0 78 56 119.5t163 41.5h129v34q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5
t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q61 0 107 -20.5t70 -59.5q31 39 75 59.5t97 20.5q73 0 129 -30t88 -85t33 -129q0 -20 -3 -41h-385zM532.5 398.5q-38.5 -32.5 -44.5 -86.5h274q-6 56 -43.5 87.5t-95.5 31.5q-52 0 -90.5 -32.5zM381 226h-131q-52 0 -79.5 -18.5
t-27.5 -56.5q0 -33 25.5 -53t69.5 -20q66 0 104.5 35.5t38.5 93.5v19z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="559" 
d="M372 112.5q33 26.5 43 71.5h110q-14 -82 -67.5 -131.5t-139.5 -57.5l-8 -32h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l20 82q-104 9 -166 79.5t-62 182.5q0 78 32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5
t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM119 708h125l77 -139h-83z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM243 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM282 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM140 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM335 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM-55 708h125l77 -139h-83z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM69 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM108 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM-34 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM161 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="597" 
d="M510 449q43 -82 43 -181q0 -78 -30 -140t-89 -98t-143 -36q-77 0 -135 30t-90 83.5t-32 121.5q0 71 33 123t87 79t116 27q47 0 84 -13.5t72 -44.5q-32 78 -115 145l-117 -70l-38 64l88 53q-37 24 -99 54l61 60q64 -26 122 -63l114 69l37 -65l-87 -52q75 -64 118 -146z
M400.5 126.5q41.5 42.5 41.5 110.5q0 64 -43 102.5t-108 38.5t-105.5 -42.5t-40.5 -106.5q0 -65 40.5 -105t105.5 -40q68 0 109.5 42.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56zM234.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5
q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM132 708h125l77 -139h-83z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM256 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM295 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM220.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM153 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM348 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M213 521.5q19 18.5 48 18.5t48.5 -18.5t19.5 -47.5t-19.5 -47.5t-48.5 -18.5t-48 18.5t-19 47.5t19 47.5zM51 240v91h418v-91h-418zM213 156.5q19 18.5 48 18.5t48.5 -18.5t19.5 -47.5t-19.5 -47.5t-48.5 -18.5t-48 18.5t-19 47.5t19 47.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="590" 
d="M508 417q48 -67 48 -161q0 -78 -32.5 -137.5t-92 -92t-137.5 -32.5q-92 0 -159 47l-47 -47l-54 52l48 48q-48 67 -48 162q0 77 32.5 136.5t91.5 92.5t136 33q94 0 161 -48l47 48l54 -52zM158 171l227 229q-37 28 -91 28q-68 0 -110 -48t-42 -124q0 -45 16 -85zM432 340
l-227 -228q37 -28 89 -28q69 0 111.5 48t42.5 124q0 46 -16 84z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM116 708h125l77 -139h-83z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM240 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM279 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM137 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM332 688.5q18 17.5 44 17.5
q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="516" 
d="M117 512l143 -395l139 395h115l-232 -592q-18 -46 -33 -70.5t-37 -37t-59 -12.5h-116v95h76q31 0 43 9.5t25 42.5l21 49l-200 516h115zM218 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="603" 
d="M159 700v-266q27 38 69 61t99 23q70 0 125 -31.5t86 -91t31 -139.5q0 -78 -31 -137.5t-86 -92t-125 -32.5q-58 0 -99.5 21t-66.5 58v-273h-108v900h106zM417.5 379q-41.5 47 -108.5 47t-108 -46.5t-41 -121.5q0 -77 41 -125t108 -48t108.5 48t41.5 123q0 76 -41.5 123z
" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="516" 
d="M117 512l143 -395l139 395h115l-232 -592q-18 -46 -33 -70.5t-37 -37t-59 -12.5h-116v95h76q31 0 43 9.5t25 42.5l21 49l-200 516h115zM115 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM310 688.5q18 17.5 44 17.5
q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM194 795v79h291v-79h-291z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM125 607v79h291v-79h-291z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="676" 
d="M8 0l262 700h137l261 -700h-123l-66 173h-287l-65 -173h-119zM224 271h223l-112 305zM274 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="549" 
d="M543 94v-94h-57q-50 0 -72 21t-22 61q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48t56 -138v-202q0 -36 34 -36h21zM381 228h-134
q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18zM205 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="676" 
d="M672 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 32 18 58.5t50 51.5h-3l-66 173h-287l-65 -173h-119l262 700h137l261 -700q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64zM224 271h223l-112 305z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="549" 
d="M547 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 35 20.5 63t58.5 56q-42 17 -42 73q-58 -88 -169 -88q-86 0 -138 40t-52 110q0 79 56 121.5t163 42.5h129v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q103 0 159 -48
t56 -138v-202q0 -36 34 -36h21v-94q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64zM381 228h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36t38.5 96v18z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="706" 
d="M78.5 536.5q41.5 80.5 118 125t178.5 44.5q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38t65.5 105h115q-11 -74 -50.5 -129t-102.5 -85t-143 -30
q-102 0 -178.5 44t-118 124.5t-41.5 187.5q0 106 41.5 186.5zM336 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="559" 
d="M448 44.5q-62 -50.5 -159 -50.5q-76 0 -134 32.5t-89.5 92.5t-31.5 138t32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5t43 71.5h110q-15 -89 -77 -139.5z
M254 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="706" 
d="M78.5 536.5q41.5 80.5 118 125t178.5 44.5q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38t65.5 105h115q-11 -74 -50.5 -129t-102.5 -85t-143 -30
q-102 0 -178.5 44t-118 124.5t-41.5 187.5q0 106 41.5 186.5zM375 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="559" 
d="M448 44.5q-62 -50.5 -159 -50.5q-76 0 -134 32.5t-89.5 92.5t-31.5 138t32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5t43 71.5h110q-15 -89 -77 -139.5z
M293 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="706" 
d="M78.5 536.5q41.5 80.5 118 125t178.5 44.5q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38t65.5 105h115q-11 -74 -50.5 -129t-102.5 -85t-143 -30
q-102 0 -178.5 44t-118 124.5t-41.5 187.5q0 106 41.5 186.5zM328.5 892q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="559" 
d="M448 44.5q-62 -50.5 -159 -50.5q-76 0 -134 32.5t-89.5 92.5t-31.5 138t32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5t43 71.5h110q-15 -89 -77 -139.5z
M246.5 704q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="706" 
d="M78.5 536.5q41.5 80.5 118 125t178.5 44.5q79 0 142.5 -30t103.5 -85.5t51 -128.5h-115q-17 67 -66 105t-121 38q-65 0 -114 -32.5t-75 -90t-26 -132.5q0 -74 26 -132t75 -90.5t114 -32.5q72 0 120.5 38t65.5 105h115q-11 -74 -50.5 -129t-102.5 -85t-143 -30
q-102 0 -178.5 44t-118 124.5t-41.5 187.5q0 106 41.5 186.5zM375 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="559" 
d="M448 44.5q-62 -50.5 -159 -50.5q-76 0 -134 32.5t-89.5 92.5t-31.5 138t32 137t90.5 91.5t135.5 32.5q96 0 157 -50.5t76 -137.5h-111q-11 44 -44 70t-81 26q-65 0 -104.5 -47t-39.5 -122q0 -76 39.5 -123.5t104.5 -47.5q50 0 83 26.5t43 71.5h110q-15 -89 -77 -139.5z
M293 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="677" 
d="M64 700h223q107 0 187 -44t123 -123t43 -183q0 -103 -43 -182.5t-123 -123.5t-187 -44h-223v700zM287 100q113 0 174 67.5t61 182.5t-61 182.5t-174 67.5h-108v-500h108zM317 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="666" 
d="M550 700v-700h-94l-13 77q-61 -83 -167 -83q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q56 0 98 -20.5t68 -56.5v259h108zM402 132q41 47 41 122q0 77 -41 124.5t-108 47.5t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47zM626 700
h102l-63 -147h-77z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="693" 
d="M490 656q80 -44 123 -123t43 -183q0 -103 -43 -182.5t-123 -123.5t-187 -44h-223v312h-83v87h83v301h223q107 0 187 -44zM477 167.5q61 67.5 61 182.5t-61 182.5t-174 67.5h-108v-201h129v-87h-129v-212h108q113 0 174 67.5z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="613" 
d="M631 558h-81v-558h-94l-13 77q-61 -83 -167 -83q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q56 0 98 -20.5t68 -56.5v117h-142v85h142v57h108v-57h81v-85zM402 132q41 47 41 122q0 77 -41 124.5t-108 47.5t-108 -47.5t-41 -122.5
q0 -76 41 -123.5t108 -47.5t108 47z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM146 795v79h291v-79h-291z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM137 607v79h291v-79h-291z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM226 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM217 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM244.5 892q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM235.5 704q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="563" 
d="M537 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 32 18 58.5t50 51.5h-349v700h457v-102h-342v-194h282v-99h-282v-203h354v-102q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="570" 
d="M147 233v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-11 -65 -56.5 -111.5t-114.5 -60.5q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 57 67 110q-94 17 -147.5 85t-53.5 170q0 79 31.5 138.5t88 92t129.5 32.5
q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386zM195.5 398.5q-38.5 -32.5 -44.5 -86.5h275q-7 56 -44.5 87.5t-94.5 31.5q-53 0 -91.5 -32.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="563" 
d="M64 700h457v-102h-342v-194h282v-99h-282v-203h354v-102h-469v700zM291 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="570" 
d="M65.5 393.5q31.5 59.5 88 92t129.5 32.5q74 0 130.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386v-6q4 -67 42 -106t101 -39q50 0 84 23.5t45 66.5h108q-14 -78 -75.5 -128t-153.5 -50q-80 0 -139.5 32.5t-92 91.5t-32.5 137q0 79 31.5 138.5zM426 312q-7 56 -44.5 87.5
t-94.5 31.5q-53 0 -91.5 -32.5t-44.5 -86.5h275zM282 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="748" 
d="M78.5 536.5q41.5 80.5 118.5 125t180 44.5q121 0 201 -66t98 -178h-114q-17 67 -66.5 105t-121.5 38q-66 0 -115.5 -32.5t-76.5 -90t-27 -132.5t26.5 -132.5t76 -90t115.5 -32.5q84 0 140.5 46t60.5 128v17h-194v97h304v-383h-87l-12 100q-30 -44 -85.5 -75t-131.5 -31
q-97 0 -172 44.5t-117 125.5t-42 186q0 106 41.5 186.5zM377 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="603" 
d="M550 512v-491q0 -108 -64 -167.5t-198 -59.5q-104 0 -169 46.5t-72 131.5h110q10 -43 46 -66.5t93 -23.5q147 0 147 143v57q-56 -84 -167 -84q-70 0 -125 31.5t-86 90t-31 138.5q0 77 31.5 136t86.5 91.5t124 32.5q58 0 100 -23t68 -63l12 80h94zM402 135.5
q41 46.5 41 120.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -122t41 -122t108 -47t108 46.5zM293 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="748" 
d="M78.5 536.5q41.5 80.5 118.5 125t180 44.5q121 0 201 -66t98 -178h-114q-17 67 -66.5 105t-121.5 38q-66 0 -115.5 -32.5t-76.5 -90t-27 -132.5t26.5 -132.5t76 -90t115.5 -32.5q84 0 140.5 46t60.5 128v17h-194v97h304v-383h-87l-12 100q-30 -44 -85.5 -75t-131.5 -31
q-97 0 -172 44.5t-117 125.5t-42 186q0 106 41.5 186.5zM312 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="603" 
d="M550 512v-491q0 -108 -64 -167.5t-198 -59.5q-104 0 -169 46.5t-72 131.5h110q10 -43 46 -66.5t93 -23.5q147 0 147 143v57q-56 -84 -167 -84q-70 0 -125 31.5t-86 90t-31 138.5q0 77 31.5 136t86.5 91.5t124 32.5q58 0 100 -23t68 -63l12 80h94zM402 135.5
q41 46.5 41 120.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -122t41 -122t108 -47t108 46.5zM228 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="748" 
d="M78.5 536.5q41.5 80.5 118.5 125t180 44.5q121 0 201 -66t98 -178h-114q-17 67 -66.5 105t-121.5 38q-66 0 -115.5 -32.5t-76.5 -90t-27 -132.5t26.5 -132.5t76 -90t115.5 -32.5q84 0 140.5 46t60.5 128v17h-194v97h304v-383h-87l-12 100q-30 -44 -85.5 -75t-131.5 -31
q-97 0 -172 44.5t-117 125.5t-42 186q0 106 41.5 186.5zM330.5 892q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="603" 
d="M550 512v-491q0 -108 -64 -167.5t-198 -59.5q-104 0 -169 46.5t-72 131.5h110q10 -43 46 -66.5t93 -23.5q147 0 147 143v57q-56 -84 -167 -84q-70 0 -125 31.5t-86 90t-31 138.5q0 77 31.5 136t86.5 91.5t124 32.5q58 0 100 -23t68 -63l12 80h94zM402 135.5
q41 46.5 41 120.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -122t41 -122t108 -47t108 46.5zM246.5 704q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="748" 
d="M78.5 536.5q41.5 80.5 118.5 125t180 44.5q121 0 201 -66t98 -178h-114q-17 67 -66.5 105t-121.5 38q-66 0 -115.5 -32.5t-76.5 -90t-27 -132.5t26.5 -132.5t76 -90t115.5 -32.5q84 0 140.5 46t60.5 128v17h-194v97h304v-383h-87l-12 100q-30 -44 -85.5 -75t-131.5 -31
q-97 0 -172 44.5t-117 125.5t-42 186q0 106 41.5 186.5zM314 -50h105l-77 -144h-79z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="603" 
d="M550 512v-491q0 -108 -64 -167.5t-198 -59.5q-104 0 -169 46.5t-72 131.5h110q10 -43 46 -66.5t93 -23.5q147 0 147 143v57q-56 -84 -167 -84q-70 0 -125 31.5t-86 90t-31 138.5q0 77 31.5 136t86.5 91.5t124 32.5q58 0 100 -23t68 -63l12 80h94zM402 135.5
q41 46.5 41 120.5q0 76 -41 123.5t-108 47.5t-108 -47t-41 -122t41 -122t108 -47t108 46.5zM338 564h-105l73 144h83z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="683" 
d="M64 701h115v-295h325v295h115v-701h-115v304h-325v-304h-115v701zM342 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-108v276q0 73 -31.5 111t-91.5 38q-64 0 -100.5 -44.5t-36.5 -121.5v-259h-108v700h108v-262q58 80 161 80q92 0 149.5 -56zM107 820l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="698" 
d="M701 587v-88h-75v-499h-115v288h-324v-288h-115v499h-75v88h75v114h115v-114h324v114h115v-114h75zM511 390v109h-324v-109h324z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="584" 
d="M480.5 462q57.5 -56 57.5 -180v-282h-108v276q0 73 -31.5 111t-91.5 38q-64 0 -100.5 -44.5t-36.5 -121.5v-259h-108v558h-80v85h80v57h108v-57h142v-85h-142v-120q58 80 161 80q92 0 149.5 -56z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM46.5 812q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM33.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM-24 795v79h291v-79h-291z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM-37 607v79h291v-79h-291z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM56 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM43 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="243" 
d="M183 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 33 19 60.5t54 53.5v696h115v-700q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="214" 
d="M60 605.5q-19 18.5 -19 46.5t19 46.5t47 18.5t47 -18.5t19 -46.5t-19 -46.5t-47 -18.5t-47 18.5zM101 -125h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 35 21 63.5t59 55.5v503h108v-512q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="243" 
d="M64 700h115v-700h-115v700zM74.5 892q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="633" 
d="M175 265q0 -80 35.5 -125t104.5 -45q71 0 106.5 41.5t35.5 129.5v434h115v-432q0 -138 -65.5 -206t-191.5 -68q-124 0 -190 69.5t-65 201.5h115zM61 700h115v-321h-115v321z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="433" 
d="M154 698.5q19 -18.5 19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5t47 18.5t47 -18.5zM53 512h108v-512h-108v512zM373.5 698.5q18.5 -18.5 18.5 -46.5t-18.5 -46.5t-46.5 -18.5q-29 0 -47.5 18.5t-18.5 46.5t19 46.5t47 18.5t46.5 -18.5zM219 -105
q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-80v95h58z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="371" 
d="M307 700v-526q0 -84 -45.5 -129t-129.5 -45h-108v103h82q45 0 65.5 21t20.5 67v509h115zM249 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="220" 
d="M5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-80v95h58zM113 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="658" 
d="M64 700h115v-361l321 361h140l-257 -292l276 -408h-139l-219 320l-122 -137v-183h-115v700zM276 -50h105l-77 -144h-79z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="537" 
d="M53 700h108v-438l234 250h125l-191 -206l203 -306h-128l-152 227l-91 -96v-131h-108v700zM229 -50h105l-77 -144h-79z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="504" 
d="M64 0v700h115v-598h301v-102h-416zM82 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="214" 
d="M53 700h108v-700h-108v700zM68 749l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="504" 
d="M64 0v700h115v-598h301v-102h-416zM213 -50h105l-77 -144h-79z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="214" 
d="M53 700h108v-700h-108v700zM52 -50h105l-77 -144h-79z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="504" 
d="M64 0v700h115v-598h301v-102h-416zM269 700h102l-63 -147h-77z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="277" 
d="M53 700h108v-700h-108v700zM237 700h102l-63 -147h-77z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="504" 
d="M64 0v700h115v-598h301v-102h-416zM323.5 420q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="331" 
d="M53 700h108v-700h-108v700zM226.5 396q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="518" 
d="M193 102h301v-102h-416v280l-81 -43v95l81 43v325h115v-264l149 79v-94l-149 -79v-240z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="294" 
d="M289 492v-97l-88 -41v-354h-108v304l-88 -41v97l88 41v299h108v-249z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="687" 
d="M64 0v700h105l341 -496v496h113v-700h-105l-341 496v-496h-113zM297 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56zM270 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="687" 
d="M64 0v700h105l341 -496v496h113v-700h-105l-341 496v-496h-113zM286 -50h105l-77 -144h-79z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56zM237 -50h105l-77 -144h-79z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="687" 
d="M64 0v700h105l341 -496v496h113v-700h-105l-341 496v-496h-113zM336 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56zM309 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="768" 
d="M168 673q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33zM665.5 462q57.5 -56 57.5 -180v-282h-109v276q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5
v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="687" 
d="M623 700v-751q0 -149 -145 -149h-78v98h58q28 0 41 13.5t13 42.5v55l-335 487v-496h-113v700h105l341 -496v496h113z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="574" 
d="M471.5 462q57.5 -56 57.5 -180v-336q0 -146 -143 -146h-80v95h60q29 0 41.5 13.5t12.5 43.5v324q0 73 -31 111t-91 38q-64 0 -100.5 -44.5t-36.5 -122.5v-258h-108v512h94l12 -67q58 73 164 73q91 0 148.5 -56z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM232 795v79h291v-79h-291z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM150 607v79h291v-79h-291z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM312 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM230 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="754" 
d="M674 167q-43 -81 -120.5 -127t-176.5 -46t-176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183t-43 -183zM183 218.5q28 -58.5 78.5 -91t115.5 -32.5t115.5 32.5t78.5 91t28 131.5t-28 131.5t-78.5 91t-115.5 32.5t-115.5 -32.5
t-78.5 -91t-28 -131.5t28 -131.5zM235 756l80 140h120l-122 -140h-78zM421 756l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="590" 
d="M158.5 26.5q-59.5 32.5 -92 92t-32.5 137.5q0 77 32.5 136.5t92 92.5t136.5 33t136.5 -33t92 -92.5t32.5 -136.5q0 -78 -32.5 -137.5t-92 -92t-136.5 -32.5t-136.5 32.5zM404.5 133.5q41.5 47.5 41.5 122.5t-41.5 122.5t-109.5 47.5t-109 -47.5t-41 -122.5t41 -122.5
t109 -47.5t109.5 47.5zM153 568l80 140h120l-122 -140h-78zM339 568l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1121" 
d="M1091 102v-102h-469v102q-38 -52 -96.5 -80t-137.5 -28q-102 0 -182 46t-124.5 127t-44.5 183t44.5 183t124.5 127t182 46q79 0 137.5 -28t96.5 -80v102h457v-102h-342v-194h282v-99h-282v-203h354zM511 127.5q53 32.5 82 90.5t29 132t-29 132t-82 90.5t-122 32.5
q-70 0 -123 -32.5t-82 -90.5t-29 -132t29 -132t82 -90.5t123 -32.5q69 0 122 32.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="970" 
d="M547 233v-6q4 -67 42 -106t102 -39q49 0 83.5 24t45.5 66h108q-15 -78 -76.5 -128t-152.5 -50q-68 0 -121 25t-86 71q-32 -46 -83.5 -71t-114.5 -25q-77 0 -136 33t-91.5 92.5t-32.5 137.5q0 77 32.5 136.5t91.5 92t136 32.5q66 0 118 -25t84 -71q32 46 80 71t108 25
q73 0 129.5 -30t89 -85t33.5 -129q0 -20 -3 -41h-386zM596 398.5q-38 -32.5 -44 -86.5h274q-7 56 -44 87.5t-95 31.5q-53 0 -91 -32.5zM403.5 133q41.5 47 41.5 121q0 76 -41.5 124t-109.5 48q-67 0 -108 -47.5t-41 -121.5q0 -76 40.5 -123.5t108.5 -47.5t109.5 47z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="625" 
d="M340 700q73 0 128 -27.5t85.5 -78t30.5 -117.5q0 -70 -37 -123t-99 -75l148 -279h-128l-133 254h-156v-254h-115v700h276zM179 354h152q62 0 98.5 33.5t36.5 89.5t-36.5 89.5t-98.5 33.5h-152v-246zM252 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="352" 
d="M336 512v-101h-48q-67 0 -97 -43.5t-30 -110.5v-257h-108v512h96l12 -77q22 36 57 56.5t94 20.5h24zM156 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="625" 
d="M340 700q73 0 128 -27.5t85.5 -78t30.5 -117.5q0 -70 -37 -123t-99 -75l148 -279h-128l-133 254h-156v-254h-115v700h276zM179 354h152q62 0 98.5 33.5t36.5 89.5t-36.5 89.5t-98.5 33.5h-152v-246zM256 -50h105l-77 -144h-79z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="352" 
d="M336 512v-101h-48q-67 0 -97 -43.5t-30 -110.5v-257h-108v512h96l12 -77q22 36 57 56.5t94 20.5h24zM52 -50h105l-77 -144h-79z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="625" 
d="M340 700q73 0 128 -27.5t85.5 -78t30.5 -117.5q0 -70 -37 -123t-99 -75l148 -279h-128l-133 254h-156v-254h-115v700h276zM179 354h152q62 0 98.5 33.5t36.5 89.5t-36.5 89.5t-98.5 33.5h-152v-246zM291 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="352" 
d="M336 512v-101h-48q-67 0 -97 -43.5t-30 -110.5v-257h-108v512h96l12 -77q22 36 57 56.5t94 20.5h24zM195 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="615" 
d="M151 228q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5
q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32t80 -59t31 -103q0 -62 -33 -111t-92 -76.5t-134 -27.5q-83 0 -146 30t-98.5 83.5t-36.5 122.5zM269 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="517" 
d="M139 169q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5
t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -48 -28 -84.5t-77 -56t-110 -19.5q-103 0 -166.5 46.5t-65.5 128.5h104zM214 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="615" 
d="M151 228q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5
q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32t80 -59t31 -103q0 -62 -33 -111t-92 -76.5t-134 -27.5q-83 0 -146 30t-98.5 83.5t-36.5 122.5zM308 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="517" 
d="M139 169q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5
t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -48 -28 -84.5t-77 -56t-110 -19.5q-103 0 -166.5 46.5t-65.5 128.5h104zM253 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="615" 
d="M467 371q49 -20 80 -59t31 -103q0 -59 -30 -106t-84 -75.5t-124 -32.5l-8 -32h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l20 82q-73 6 -128.5 37.5t-86 82.5t-31.5 115l113 -2q5 -62 50.5 -99.5t118.5 -37.5
q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5q0 -34 21.5 -54t57.5 -30t103 -22
q68 -12 117 -32z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="517" 
d="M430 44.5q-52 -42.5 -136 -49.5l-8 -32h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l20 82q-92 7 -146.5 52.5t-56.5 121.5h104q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5
t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -67 -52 -109.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="615" 
d="M151 228q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5
q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32t80 -59t31 -103q0 -62 -33 -111t-92 -76.5t-134 -27.5q-83 0 -146 30t-98.5 83.5t-36.5 122.5zM308 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="517" 
d="M139 169q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5
t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -48 -28 -84.5t-77 -56t-110 -19.5q-103 0 -166.5 46.5t-65.5 128.5h104zM253 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="594" 
d="M24 700h547v-102h-216v-598h-115v598h-216v102zM242 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="335" 
d="M86 417h-89v95h89v143h109v-143h123v-95h-123v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106.5 34t-33.5 105v278zM154 -50h105l-77 -144h-79z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="594" 
d="M24 700h547v-102h-216v-598h-115v598h-216v102zM300 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="341" 
d="M86 417h-89v95h89v143h109v-143h123v-95h-123v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106.5 34t-33.5 105v278zM275 709h102l-63 -147h-77z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="594" 
d="M571 598h-216v-216h114v-87h-114v-295h-115v295h-115v87h115v216h-216v102h547v-102z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="357" 
d="M335 95v-95h-104q-73 0 -106 34t-33 105v95h-89v84h89v99h-89v95h89v143h108v-143h124v-95h-124v-99h124v-84h-124v-84q0 -30 12 -42.5t41 -12.5h82z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM261.5 812q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5
q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM204.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66
v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM191 795v79h291v-79h-291z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM134 607v79h291v-79h-291z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM271 896q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z
" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM214 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM249.5 867q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81z
M379.5 829.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM192.5 753q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM322.5 715.5
q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="672" 
d="M175 700v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115v-400q0 -150 -70.5 -228t-206.5 -78q-135 0 -205 78t-70 228v400h115zM194 756l80 140h120l-122 -140h-78zM380 756l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="565" 
d="M512 512v-512h-95l-12 65q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108zM137 568l80 140h120l-122 -140h-78zM323 568l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="672" 
d="M612 700v-400q0 -131 -54 -207.5t-159 -93.5q-48 -25 -73 -46.5t-25 -45.5q0 -32 40 -32h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 31 17 57t48 51q-108 16 -163 92.5t-55 209.5v400h115v-406q0 -99 39 -148.5t121 -49.5q83 0 122.5 49.5t39.5 148.5v406h115z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="565" 
d="M516 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 37 23.5 66.5t66.5 60.5l-9 48q-58 -71 -158 -71q-90 0 -146 56t-56 180v282h108v-271q0 -75 30 -114t89 -39q62 0 97 45.5t35 124.5v254h108v-512q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="991" 
d="M19 700l120 1l149 -555l156 554h109l151 -554l151 554h117l-200 -700h-135l-143 515l-140 -515h-135zM495 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="798" 
d="M4 512h114l117 -392l117 392h99l112 -392l121 392h110l-170 -512h-116l-108 348l-112 -348l-117 -1zM401 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="647" 
d="M5 701h135l184 -321l189 321h129l-261 -442v-259h-115v259zM325 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="516" 
d="M117 512l143 -395l139 395h115l-232 -592q-18 -46 -33 -70.5t-37 -37t-59 -12.5h-116v95h76q31 0 43 9.5t25 42.5l21 49l-200 516h115zM257 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="647" 
d="M5 701h135l184 -321l189 321h129l-261 -442v-259h-115v259zM183 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM378 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z
" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="617" 
d="M40 700h537v-79l-392 -520h403v-101h-559v79l391 520h-380v101zM273 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="488" 
d="M34 512h419v-77l-286 -340h299v-95h-444v77l286 340h-274v95zM206 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="617" 
d="M40 700h537v-79l-392 -520h403v-101h-559v79l391 520h-380v101zM265.5 892q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="488" 
d="M34 512h419v-77l-286 -340h299v-95h-444v77l286 340h-274v95zM198.5 704q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="617" 
d="M40 700h537v-79l-392 -520h403v-101h-559v79l391 520h-380v101zM312 824l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="488" 
d="M34 512h419v-77l-286 -340h299v-95h-444v77l286 340h-274v95zM245 636l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="482" 
d="M333.5 591.5q-14.5 -13.5 -16.5 -42.5l-15 -172h116v-88h-124l-23 -268q-5 -62 -42.5 -94.5t-104.5 -32.5h-106v95h87q29 0 43.5 13.5t16.5 42.5l21 244h-110v88h118l17 196q5 62 42.5 94.5t103.5 32.5h107v-95h-87q-29 0 -43.5 -13.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="975" 
d="M868 896l-123 -138h-82l80 138h125zM590 102h354v-102h-469v164h-256l-102 -164h-119l418 700h516v-102h-342v-194h282v-99h-282v-203zM271 260h204v343z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="906" 
d="M618 700l-123 -138h-82l80 138h125zM484 233v-6q3 -66 41.5 -105.5t101.5 -39.5q49 0 83.5 24t45.5 66h108q-14 -78 -76 -128t-153 -50q-68 0 -120.5 27t-83.5 77q-32 -49 -83 -76.5t-119 -27.5q-87 0 -141.5 40t-54.5 110q0 78 56 119.5t163 41.5h129v34q0 43 -30 67.5
t-83 24.5q-47 0 -77.5 -20.5t-36.5 -54.5h-106q8 77 68 119.5t157 42.5q61 0 107 -20.5t70 -59.5q31 39 75 59.5t97 20.5q73 0 129 -30t88 -85t33 -129q0 -20 -3 -41h-385zM532.5 398.5q-38.5 -32.5 -44.5 -86.5h274q-6 56 -43.5 87.5t-95.5 31.5q-52 0 -90.5 -32.5z
M381 226h-131q-52 0 -79.5 -18.5t-27.5 -56.5q0 -33 25.5 -53t69.5 -20q66 0 104.5 35.5t38.5 93.5v19z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="615" 
d="M151 228q5 -62 50.5 -99.5t118.5 -37.5q64 0 103.5 27.5t39.5 76.5q0 35 -21.5 55.5t-58.5 31.5t-104 23q-68 12 -116.5 31.5t-79 59.5t-30.5 105q0 60 31.5 107t88 73t129.5 26q75 0 134 -28t93 -77t37 -112h-113q-6 54 -47 87t-104 33q-61 0 -97.5 -26.5t-36.5 -74.5
q0 -34 21.5 -54t57.5 -30t103 -22q68 -12 117 -32t80 -59t31 -103q0 -62 -33 -111t-92 -76.5t-134 -27.5q-83 0 -146 30t-98.5 83.5t-36.5 122.5zM258 -50h105l-77 -144h-79z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="517" 
d="M139 169q3 -40 37.5 -65.5t89.5 -25.5q49 0 79.5 18.5t30.5 49.5q0 26 -14 39t-37.5 18.5t-72.5 11.5q-67 8 -110.5 21.5t-70 43t-26.5 79.5q0 47 26.5 83.5t73 56.5t105.5 20q97 0 157.5 -43t65.5 -121h-105q-4 35 -35 57.5t-78 22.5t-76 -18t-29 -49q0 -23 14.5 -34.5
t36.5 -16t71 -10.5q66 -7 111 -21.5t72 -46t27 -85.5q0 -48 -28 -84.5t-77 -56t-110 -19.5q-103 0 -166.5 46.5t-65.5 128.5h104zM212 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="594" 
d="M24 700h547v-102h-216v-598h-115v598h-216v102zM242 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="335" 
d="M86 417h-89v95h89v143h109v-143h123v-95h-123v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106.5 34t-33.5 105v278zM154 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="220" 
d="M5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-80v95h58z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="356" 
d="M148 700h102l-63 -147h-77z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="400" 
d="M200 633l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="400" 
d="M200 628l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="400" 
d="M135 700q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="400" 
d="M153.5 696q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="400" 
d="M113.5 745q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM243.5 707.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="400" 
d="M37 -41.5q25 30.5 70 61.5l93 -20q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 38 25 68.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="400" 
d="M125.5 616q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="403" 
d="M58 560l80 140h120l-122 -140h-78zM244 560l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" horiz-adv-x="0" 
d="M-363 700h125l77 -139h-83z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" horiz-adv-x="0" 
d="M-239 562l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" horiz-adv-x="0" 
d="M-200 633l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" horiz-adv-x="0" 
d="M-274.5 616q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" horiz-adv-x="0" 
d="M-345 599v79h291v-79h-291z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" horiz-adv-x="0" 
d="M-265 700q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" horiz-adv-x="0" 
d="M-246.5 696q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" horiz-adv-x="0" 
d="M-342 680.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM-147 680.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" horiz-adv-x="0" 
d="M-286.5 745q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM-156.5 707.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" horiz-adv-x="0" 
d="M-342 560l80 140h120l-122 -140h-78zM-156 560l80 140h120l-122 -140h-78z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" horiz-adv-x="0" 
d="M-200 628l76 72h101l-127 -139h-100l-127 139h102z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" horiz-adv-x="0" 
d="M-155 556h-105l73 144h83z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-255 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" horiz-adv-x="0" 
d="M-165 24l-16 -61h14q41 0 65.5 -20.5t24.5 -54.5q0 -40 -27 -64t-72 -24h-103v58h83q37 0 37 29q0 12 -10 19t-26 7h-54l27 111h57z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" horiz-adv-x="0" 
d="M-363 -41.5q25 30.5 70 61.5l93 -20q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 38 25 68.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="615" 
d="M601 511v-95h-75v-416h-109v416h-220v-416h-109v416h-74v95h587z" />
    <glyph glyph-name="uni0E3F" unicode="&#xe3f;" horiz-adv-x="647" 
d="M604 195q0 -91 -68 -143t-193 -52h-3v-98h-67v98h-209v700h209v103h67v-103q114 -1 175.5 -48.5t61.5 -133.5q0 -63 -34 -103.5t-97 -54.5q158 -27 158 -165zM179 604v-201h94v201h-94zM340 604v-201h1q59 0 90.5 26.5t31.5 74.5q0 47 -31.5 73.5t-90.5 26.5h-1zM179 97
h94v211h-94v-211zM449 124.5q37 27.5 37 76.5q0 51 -37.5 79t-105.5 28h-3v-211h3q69 0 106 27.5z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="991" 
d="M19 700l120 1l149 -555l156 554h109l151 -554l151 554h117l-200 -700h-135l-143 515l-140 -515h-135zM332 896h125l77 -139h-83z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="798" 
d="M4 512h114l117 -392l117 392h99l112 -392l121 392h110l-170 -512h-116l-108 348l-112 -348l-117 -1zM238 708h125l77 -139h-83z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="991" 
d="M19 700l120 1l149 -555l156 554h109l151 -554l151 554h117l-200 -700h-135l-143 515l-140 -515h-135zM456 758l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="798" 
d="M4 512h114l117 -392l117 392h99l112 -392l121 392h110l-170 -512h-116l-108 348l-112 -348l-117 -1zM362 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="991" 
d="M19 700l120 1l149 -555l156 554h109l151 -554l151 554h117l-200 -700h-135l-143 515l-140 -515h-135zM353 876.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM548 876.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5
t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="798" 
d="M4 512h114l117 -392l117 392h99l112 -392l121 392h110l-170 -512h-116l-108 348l-112 -348l-117 -1zM259 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM454 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5
t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="647" 
d="M5 701h135l184 -321l189 321h129l-261 -442v-259h-115v259zM162 896h125l77 -139h-83z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="516" 
d="M117 512l143 -395l139 395h115l-232 -592q-18 -46 -33 -70.5t-37 -37t-59 -12.5h-116v95h76q31 0 43 9.5t25 42.5l21 49l-200 516h115zM94 708h125l77 -139h-83z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="594" 
d="M47 240v88h500v-88h-500z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="923" 
d="M47 329h829v-88h-829v88z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="224" 
d="M56 471q-25 33 -25 82q0 72 43 112t118 41v-58q-44 -1 -66 -23t-22 -52q0 -9 1 -13q13 10 33 10q26 0 42.5 -16.5t16.5 -47.5q0 -29 -21 -48.5t-55 -19.5q-40 0 -65 33z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="224" 
d="M168 673q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="224" 
d="M168 96q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="417" 
d="M56 471q-25 33 -25 82q0 72 43 112t118 41v-58q-44 -1 -66 -23t-22 -52q0 -9 1 -13q13 10 33 10q26 0 42.5 -16.5t16.5 -47.5q0 -29 -21 -48.5t-55 -19.5q-40 0 -65 33zM248 471q-25 33 -25 82q0 72 43 112t118 41v-58q-44 -1 -66 -23t-22 -52q0 -9 1 -13q13 10 33 10
q26 0 42.5 -16.5t16.5 -47.5q0 -29 -21 -48.5t-55 -19.5q-40 0 -65 33z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="417" 
d="M168 673q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33zM360 673q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10
q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="417" 
d="M168 96q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33zM360 96q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10
q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="518" 
d="M488 497v-90h-181v-407h-96v407h-181v90h181v203h96v-203h181z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="554" 
d="M512 444h-186v-188h186v-90h-186v-166h-97v166h-187v90h187v188h-187v90h187v166h97v-166h186v-90z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="429" 
d="M105.5 445.5q42.5 40.5 108.5 40.5q67 0 110 -40.5t43 -105.5t-43 -106t-110 -41q-66 0 -108.5 41t-42.5 106t42.5 105.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="648" 
d="M62.5 111q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49zM274.5 111q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49zM485.5 111q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z
" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1313" 
d="M73 623q23 39 64 61t92 22t92 -22t64 -61t23 -88t-23 -87.5t-64 -60.5t-92 -22t-92 22t-64 60.5t-23 87.5t23 88zM166 0l478 700h107l-479 -700h-106zM292 598.5q-25 25.5 -63 25.5q-37 0 -62 -25.5t-25 -63.5t25 -63.5t62 -25.5q38 0 63 25.5t25 63.5t-25 63.5zM530 252
q23 39 64 61t92 22t92 -22t64 -61t23 -88q0 -48 -23 -87t-64 -61t-92 -22t-92 22t-64 61t-23 87q0 49 23 88zM927.5 252q23.5 39 64.5 61t92 22t92 -22t64 -61t23 -88q0 -48 -23 -87t-64 -61t-92 -22t-92 22t-64.5 61t-23.5 87q0 49 23.5 88zM748 228q-25 26 -62 26
t-62.5 -26t-25.5 -64t25 -63t63 -25t62.5 25t24.5 63t-25 64zM1146 228q-25 26 -62 26q-38 0 -63 -26t-25 -64t25 -63t63 -25t62.5 25t24.5 63t-25 64z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="352" 
d="M308 512l-154 -256l154 -256h-109l-156 256l156 256h109z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="352" 
d="M44 512h109l156 -256l-156 -256h-109l154 256z" />
    <glyph glyph-name="uni2042" unicode="&#x2042;" horiz-adv-x="969" 
d="M654 600l-119 -44l82 -93l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53zM412 139l-119 -44l82 -93l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53zM904 139l-119 -44l82 -93
l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="190" 
d="M-170 0l444 700h86l-443 -700h-87z" />
    <glyph glyph-name="uni2051" unicode="&#x2051;" horiz-adv-x="476" 
d="M412 600l-119 -44l82 -93l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53zM412 138l-119 -44l82 -93l-58 -50l-80 105l-79 -104l-57 50l82 93l-118 44l30 70l109 -52l-3 122h74l-3 -123l110 53z" />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="344" 
d="M62 756q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM227 707q-20 33 -55 33t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="346" 
d="M179 800h92v-212h56v-66h-56v-86h-73v86h-179v66zM198 726l-105 -138h105v138z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="316" 
d="M274 735h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="326" 
d="M61.5 754q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM216 598.5q-18 17.5 -46 17.5
q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="300" 
d="M18 800h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="332" 
d="M39 593q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM211.5 736q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5
t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM217 581.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="326" 
d="M265 484q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141zM110.5 639.5q17.5 -17.5 46.5 -17.5
t46.5 17t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="343" 
d="M61 219q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM226 170q-20 33 -55 33t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="190" 
d="M83 -100v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="320" 
d="M21 139q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="330" 
d="M66 238.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="346" 
d="M179 264h92v-212h56v-66h-56v-86h-73v86h-179v66zM198 190l-105 -138h105v138z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="316" 
d="M274 199h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="326" 
d="M61.5 217q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM216 61.5q-18 17.5 -46 17.5
q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="300" 
d="M18 265h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="332" 
d="M39 56q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM211.5 199q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5
t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM217 44.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="326" 
d="M265 -53q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141zM110.5 102.5q17.5 -17.5 46.5 -17.5
t46.5 17t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="556" 
d="M532 598h-332v-203h274v-99h-274v-105h136v-84h-136v-107h-115v107h-86v84h86v509h447v-102z" />
    <glyph glyph-name="uni20A8" unicode="&#x20a8;" horiz-adv-x="613" 
d="M584 700l-13 -74h-153q42 -47 51 -112h103l-13 -74h-90q-9 -57 -44 -99.5t-89 -61.5l148 -279h-128l-133 254h-159v99h155q50 0 84 23.5t45 63.5h-284v74h284q-10 40 -44 63.5t-85 23.5h-155v99h520z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="667" 
d="M22 242v75h64q-1 8 -1 33q0 16 1 27h-64v74h69q16 120 89.5 187.5t184.5 67.5q117 0 188 -66t80 -178h-113q-9 68 -48.5 107t-102.5 39q-65 0 -108 -41t-54 -116h178l-13 -74h-172q-1 -9 -1 -26l1 -34h158l-13 -75h-138q14 -72 56.5 -111t106.5 -39q62 0 100.5 39
t49.5 107h112q-11 -113 -81 -178.5t-186 -65.5q-110 0 -182.5 65.5t-91.5 182.5h-69z" />
    <glyph glyph-name="uni20BF" unicode="&#x20bf;" horiz-adv-x="636" 
d="M175 700v105h80v-105h67v105h80v-111q78 -14 121 -59.5t43 -116.5q0 -63 -34 -103.5t-97 -54.5q158 -27 158 -165q0 -78 -50 -127.5t-141 -61.5v-102h-80v96h-67v-96h-80v96h-111v700h111zM179 403h150q58 0 90 26.5t32 74.5q0 47 -31.5 73.5t-90.5 26.5h-150v-201z
M179 97h154q68 0 105 27.5t37 76.5q0 51 -37.5 79t-104.5 28h-154v-211z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="711" 
d="M42 700h238v-60h-86v-209h-66v209h-86v60zM311 700h91l81 -187l80 187h90v-269h-63v190l-83 -190h-52l-81 190v-190h-63v269z" />
    <glyph glyph-name="uni2126" unicode="&#x2126;" horiz-adv-x="737" 
d="M428 161q78 46 113 102.5t35 132.5q0 61 -27 109t-75 75t-107 27t-106 -27t-73.5 -75t-26.5 -109q0 -76 34.5 -132.5t112.5 -102.5v-161h-255v102h167v27q-176 72 -176 273q0 92 41.5 161t115 106t166.5 37q95 0 169 -37t115.5 -105.5t41.5 -160.5q0 -99 -43.5 -169.5
t-133.5 -104.5v-27h169v-102h-257v161z" />
    <glyph glyph-name="uni2153" unicode="&#x2153;" horiz-adv-x="698" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM441 338.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5
h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="uni2155" unicode="&#x2155;" horiz-adv-x="683" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM648 299h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5
q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2156" unicode="&#x2156;" horiz-adv-x="813" 
d="M21 575q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7zM150 0l444 700h86l-443 -700h-87zM778 299h-177l-6 -87
q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2157" unicode="&#x2157;" horiz-adv-x="822" 
d="M66 676.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5zM160 0l444 700h86l-443 -700h-87zM788 299h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17
t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2158" unicode="&#x2158;" horiz-adv-x="838" 
d="M179 700h92v-212h56v-66h-56v-86h-73v86h-179v66zM198 626l-105 -138h105v138zM176 0l444 700h86l-443 -700h-87zM804 299h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5
q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2159" unicode="&#x2159;" horiz-adv-x="676" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM415.5 317q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33
t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM570 161.5q-18 17.5 -46 17.5q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="690" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73zM20 0l444 700h86l-443 -700h-87zM402 156q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5
t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM574.5 299q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM580 144.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="830" 
d="M66 676.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5zM160 0l444 700h86l-443 -700h-87zM542 156q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5
q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM714.5 299q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM720 144.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="816" 
d="M274 635h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65zM146 0
l444 700h86l-443 -700h-87zM528 156q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM700.5 299q-17.5 14 -44.5 14t-44.5 -14
t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM706 144.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="800" 
d="M18 700h267v-61l-153 -304h-80l152 300l-186 -1v66zM130 0l444 700h86l-443 -700h-87zM512 156q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5
t-40.5 75.5q0 33 19 57zM684.5 299q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM690 144.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="597" 
d="M8 268l275 276l67 -67l-165 -159h361v-100h-361l165 -159l-67 -67z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="649" 
d="M600 243l-67 -66l-159 164v-341h-99v341l-159 -164l-67 66l275 275z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="597" 
d="M589 269l-275 -276l-67 67l165 159h-361v100h361l-165 159l67 67z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="649" 
d="M600 267l-276 -275l-275 275l67 66l159 -164v341h99v-341l159 164z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="860" 
d="M565 521l252 -253l-252 -253l-66 67l141 136h-420l142 -136l-67 -67l-252 253l252 253l67 -67l-142 -136h420l-141 136z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="591" 
d="M43 472l253 252l253 -252l-67 -67l-136 141v-399l136 141l67 -67l-254 -252l-252 252l67 67l136 -141v399l-136 -141z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="510" 
d="M49 512h390v-90l-238 2l260 -254l-70 -71l-255 261l2 -238h-89v390z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="510" 
d="M461 512v-390h-89l2 238l-255 -261l-70 71l260 254l-238 -2v90h390z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="510" 
d="M71 89l238 -2l-260 254l70 71l255 -261l-2 238h89v-390h-390v90z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="510" 
d="M138 390l-2 -239l255 261l70 -71l-260 -254l238 2v-90h-390v391h89z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="598" 
d="M70.5 354q32.5 52 86 79.5t115.5 27.5q50 0 92 -14t67 -41q-26 84 -100 138.5t-217 67.5v94q150 -5 249.5 -67.5t146.5 -160t47 -210.5q0 -79 -30 -141t-89 -97.5t-143 -35.5q-76 0 -134.5 31t-90.5 85t-32 122q0 70 32.5 122zM404 126.5q41 42.5 41 111.5
q0 64 -43 102.5t-107 38.5q-65 0 -106 -41t-41 -106t41 -106.5t106 -41.5q68 0 109 42.5z" />
    <glyph glyph-name="emptyset" unicode="&#x2205;" horiz-adv-x="619" 
d="M583 356q0 -75 -35 -134.5t-97.5 -93.5t-140.5 -34q-97 0 -167 51l-45 -46l-54 52l45 46q-51 69 -51 159q0 75 35 134.5t97.5 93.5t139.5 34q89 0 155 -43l47 48l54 -52l-44 -44q61 -70 61 -171zM167 275l226 228q-38 22 -83 22q-46 0 -83 -22t-58 -61t-21 -86
q0 -43 19 -81zM393 209q37 22 58.5 61t21.5 86q0 51 -26 95l-232 -234q41 -30 95 -30q46 0 83 22z" />
    <glyph glyph-name="uni2206" unicode="&#x2206;" horiz-adv-x="685" 
d="M25 0v92l247 608h141l247 -608v-92h-635zM147 104h383l-192 479z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="739" 
d="M711 700v-102h-84v-704h-115v704h-284v-704h-115v704h-84v102h682z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="622" 
d="M36 -13l279 322l-259 298v93h518v-101h-368l219 -252v-73l-245 -281h402v-99h-546v93z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M51 240v91h418v-91h-418z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="601" 
d="M100 349l-51 -26l-37 82l145 69l143 -341l175 567h107l-219 -700h-111z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="821" 
d="M501.5 114.5q-42.5 26.5 -92.5 97.5q-50 -71 -92.5 -97.5t-96.5 -26.5q-59 0 -101.5 28t-65 74.5t-22.5 101.5t22.5 101t65 74t101.5 28q54 0 97 -26.5t93 -96.5q50 70 92 96.5t96 26.5q59 0 101.5 -28t65 -74t22.5 -101t-22.5 -101.5t-65 -74.5t-101.5 -28
q-54 0 -96.5 26.5zM286.5 207.5q27.5 22.5 67.5 83.5q-40 61 -67.5 84t-61.5 23q-44 0 -69.5 -29.5t-25.5 -76.5q0 -50 25 -78.5t70 -28.5q34 0 61.5 22.5zM531 208q28 -23 62 -23q45 0 70 28.5t25 78.5q0 47 -25.5 76.5t-69.5 29.5q-34 0 -62 -23t-67 -83q39 -61 67 -84z
" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="398" 
d="M91 -11q28 0 41 13.5t13 42.5v509q0 73 34.5 109.5t108.5 36.5h83v-95h-64q-28 0 -41 -13.5t-13 -42.5v-509q0 -73 -34.5 -109.5t-108.5 -36.5h-83v95h64z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M51 64q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88zM51 288q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28
q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M459 337h-130l-40 -120h170v-91h-201l-31 -94h-102l31 94h-95v91h126l40 120h-166v92h196l30 89h102l-30 -89h100v-92z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M67 307v102l386 167v-105l-266 -113l266 -113v-105zM67 0v91h386v-91h-386z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M453 409v-102l-386 -167v105l266 113l-266 113v105zM67 0v91h386v-91h-386z" />
    <glyph glyph-name="dotmath" unicode="&#x22c5;" horiz-adv-x="224" 
d="M62.5 344q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="filledbox" unicode="&#x25a0;" horiz-adv-x="832" 
d="M64 700h704v-700h-704v700z" />
    <glyph glyph-name="uni25A1" unicode="&#x25a1;" horiz-adv-x="830" 
d="M64 700h702v-700h-702v700zM174 600v-500h482v500h-482z" />
    <glyph glyph-name="uni25AF" unicode="&#x25af;" horiz-adv-x="612" 
d="M64 700h484v-700h-484v700zM174 600v-500h264v500h-264z" />
    <glyph glyph-name="triagup" unicode="&#x25b2;" horiz-adv-x="865" 
d="M434 712l409 -712h-820z" />
    <glyph glyph-name="uni25B3" unicode="&#x25b3;" horiz-adv-x="865" 
d="M434 712l409 -712h-820zM433 510l-238 -411h475z" />
    <glyph glyph-name="uni25B6" unicode="&#x25b6;" horiz-adv-x="507" 
d="M53 506l446 -256l-446 -256v512z" />
    <glyph glyph-name="uni25B7" unicode="&#x25b7;" horiz-adv-x="516" 
d="M53 518l455 -262l-455 -261v523zM135 132l219 124l-219 126v-250z" />
    <glyph glyph-name="uni25C0" unicode="&#x25c0;" horiz-adv-x="507" 
d="M454 506v-512l-446 256z" />
    <glyph glyph-name="uni25C1" unicode="&#x25c1;" horiz-adv-x="516" 
d="M463 518v-523l-455 261zM381 132v250l-219 -126z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="621" 
d="M378 700l200 -350l-200 -351h-135l-200 351l200 350h135zM311 88l142 262l-142 262l-143 -262z" />
    <glyph glyph-name="circle" unicode="&#x25cb;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37z" />
    <glyph glyph-name="uni25CF" unicode="&#x25cf;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46z" />
    <glyph glyph-name="uni25FD" unicode="&#x25fd;" horiz-adv-x="618" 
d="M53 511h512v-512h-512v512zM138 430v-349h340v349h-340z" />
    <glyph glyph-name="uni25FE" unicode="&#x25fe;" horiz-adv-x="614" 
d="M53 508h508v-508h-508v508z" />
    <glyph glyph-name="uni262E" unicode="&#x262e;" horiz-adv-x="791" 
d="M83 533q46 81 128 127t184 46q103 0 184.5 -46t127.5 -127t46 -183t-46 -183t-127.5 -127t-184.5 -46q-102 0 -184 46t-128 127t-46 183t46 183zM353 609q-65 -8 -115 -43t-78.5 -91.5t-28.5 -124.5q0 -70 40 -123l182 186v196zM437 412l181 -185q42 52 42 123
q0 68 -28.5 124.5t-79 91.5t-115.5 43v-197zM437 91q88 11 146 66l-146 150v-216zM208 157q58 -55 145 -66v215z" />
    <glyph glyph-name="uni26AA" unicode="&#x26aa;" horiz-adv-x="584" 
d="M423 476.5q59 -33.5 93 -92.5t34 -130t-34 -130t-93 -92.5t-131 -33.5t-131 33.5t-93 92.5t-34 130t34 130t93 92.5t131 33.5t131 -33.5zM204 406.5q-40 -23.5 -63 -63.5t-23 -89q0 -48 23 -88.5t63 -64t88 -23.5t88 23.5t63 64t23 88.5q0 49 -23 89t-63 63.5t-88 23.5
t-88 -23.5z" />
    <glyph glyph-name="uni26AB" unicode="&#x26ab;" horiz-adv-x="584" 
d="M423 476.5q59 -33.5 93 -92.5t34 -130t-34 -130t-93 -92.5t-131 -33.5t-131 33.5t-93 92.5t-34 130t34 130t93 92.5t131 33.5t131 -33.5z" />
    <glyph glyph-name="uni2713" unicode="&#x2713;" horiz-adv-x="1018" 
d="M23 349l76 77l273 -272l545 546l78 -77l-623 -623z" />
    <glyph glyph-name="uni2715" unicode="&#x2715;" horiz-adv-x="775" 
d="M115 700l272 -272l273 272l77 -77l-273 -272l273 -274l-77 -76l-273 272l-272 -272l-77 76l273 274l-273 272z" />
    <glyph glyph-name="uni2780" unicode="&#x2780;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM369 168v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="uni2781" unicode="&#x2781;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM256 407q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="uni2782" unicode="&#x2782;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM297 506.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21
v50h22q29 0 46.5 12.5t17.5 34.5q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="uni2783" unicode="&#x2783;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM388 532h92v-212h56v-66h-56v-86h-73v86h-179v66zM407 458l-105 -138h105v138z" />
    <glyph glyph-name="uni2784" unicode="&#x2784;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM514 467h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5
t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni2785" unicode="&#x2785;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM288.5 485q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5
q0 88 42.5 141zM443 329.5q-18 17.5 -46 17.5q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="uni2786" unicode="&#x2786;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM268 533h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="uni2787" unicode="&#x2787;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM270 324q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM442.5 467q-17.5 14 -44.5 14
t-44.5 -14t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM448 312.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="uni2788" unicode="&#x2788;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM504 215q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141z
M349.5 370.5q17.5 -17.5 46.5 -17.5t46.5 17t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="uni278A" unicode="&#x278a;" horiz-adv-x="793" 
d="M212 659q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM367 167v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="uni278B" unicode="&#x278b;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM254 407q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65
l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="uni278C" unicode="&#x278c;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM295 506.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55
q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="uni278D" unicode="&#x278d;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM386 532h92v-212h56v-66h-56v-86h-73v86h-179v66zM405 458l-105 -138h105v138z" />
    <glyph glyph-name="uni278E" unicode="&#x278e;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM512 467h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35
q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="uni278F" unicode="&#x278f;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM286.5 485q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32
t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM441 329.5q-18 17.5 -46 17.5q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="uni2790" unicode="&#x2790;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM266 533h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="uni2791" unicode="&#x2791;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM268 324q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82
q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM440.5 467q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM446 312.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38
t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="uni2792" unicode="&#x2792;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM502 215q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24
q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141zM347.5 370.5q17.5 -17.5 46.5 -17.5t46.5 17t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="fl" unicode="&#xfb02;" horiz-adv-x="547" 
d="M86 512v51q0 68 35 102.5t108 34.5h104v-95h-89q-26 0 -37.5 -12t-11.5 -38v-43h137v-95h-137v-417h-109v417h-89v95h89zM386 700h108v-700h-108v700z" />
    <glyph glyph-name="u1F10B" unicode="&#x1f10b;" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM288 487q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM453 438q-20 33 -55 33t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="u1F10C" unicode="&#x1f10c;" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM286 487q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM451 438q-20 33 -55 33
t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="J.ss06" horiz-adv-x="598" 
d="M141 265q0 -80 35.5 -125t104.5 -45q71 0 106.5 41.5t35.5 129.5v434h115v-432q0 -138 -65.5 -206t-191.5 -68q-124 0 -190 69.5t-65 201.5h115z" />
    <glyph glyph-name="Jcircumflex.ss06" horiz-adv-x="598" 
d="M141 265q0 -80 35.5 -125t104.5 -45q71 0 106.5 41.5t35.5 129.5v434h115v-432q0 -138 -65.5 -206t-191.5 -68q-124 0 -190 69.5t-65 201.5h115zM482 829l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="Q.ss07" horiz-adv-x="754" 
d="M677 173q-40 -79 -112 -125l81 -126h-116l-55 85q-49 -13 -98 -13q-99 0 -176.5 46t-120.5 127t-43 183t43 183t120.5 127t176.5 46t176.5 -46t120.5 -127t43 -183q0 -98 -40 -177zM417 98l-80 125h116l54 -85q44 34 68 89t24 123q0 73 -28 131.5t-78.5 91t-115.5 32.5
t-115.5 -32.5t-78.5 -91t-28 -131.5t28 -131.5t78.5 -91t115.5 -32.5q21 0 40 3z" />
    <glyph glyph-name="a.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z" />
    <glyph glyph-name="a.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36z" />
    <glyph glyph-name="aacute.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M256 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="aacute.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM231 570l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="abreve.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M230 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="abreve.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM205 708q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="acircumflex.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M295 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="acircumflex.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM270 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="adieresis.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M153 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM348 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="adieresis.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM128 688.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM323 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5
t18 43.5z" />
    <glyph glyph-name="agrave.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M132 708h125l77 -139h-83z" />
    <glyph glyph-name="agrave.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM107 708h125l77 -139h-83z" />
    <glyph glyph-name="amacron.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M150 607v79h291v-79h-291z" />
    <glyph glyph-name="amacron.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM125 607v79h291v-79h-291z" />
    <glyph glyph-name="aogonek.ss01" horiz-adv-x="603" 
d="M555 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 37 23.5 66.5t66.5 59.5l-10 62q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94v-512h1q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32
h64zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z" />
    <glyph glyph-name="aogonek.ss02" horiz-adv-x="533" 
d="M492 -125v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 38 24 67.5t68 60.5l-7 55q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48
t56 -137v-333q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32h64zM342.5 114q37.5 36 38.5 96v18h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36z" />
    <glyph glyph-name="aring.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M208.5 753q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM338.5 715.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="aring.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM183.5 753q35.5 34 86.5 34t86.5 -34t35.5 -81q0 -49 -35.5 -83t-86.5 -34t-86.5 34t-35.5 83q0 47 35.5 81zM313.5 715.5q-17.5 17.5 -43.5 17.5t-43.5 -17.5t-17.5 -43.5q0 -27 17.5 -45t43.5 -18
t43.5 18t17.5 45q0 26 -17.5 43.5z" />
    <glyph glyph-name="atilde.ss01" horiz-adv-x="603" 
d="M550 512v-512h-95l-12 78q-27 -38 -69 -61t-98 -23q-70 0 -125 31.5t-86 91t-31 139.5q0 78 31.5 137.5t86.5 92t124 32.5q57 0 99 -21.5t68 -59.5l13 75h94zM402 132q41 47 41 122q0 76 -41 124t-108 48t-108 -47.5t-41 -122.5q0 -76 41 -123.5t108 -47.5t108 47z
M220.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="atilde.ss02" horiz-adv-x="533" 
d="M432 470q56 -48 56 -137v-333h-94l-9 73q-29 -38 -68.5 -58.5t-96.5 -20.5q-85 0 -136 40t-51 110q0 79 56.5 121.5t163.5 42.5h128v31q0 43 -30 67.5t-83 24.5q-47 0 -77.5 -20t-36.5 -55h-106q8 77 68 119.5t157 42.5q103 0 159 -48zM342.5 114q37.5 36 38.5 96v18
h-134q-50 0 -77 -19.5t-27 -57.5q0 -33 25.5 -53t69.5 -20q67 0 104.5 36zM195.5 624q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15
t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="f.calt" horiz-adv-x="299" 
d="M88 665.5q35 34.5 108 34.5h103v-95h-88q-26 0 -38 -12t-12 -38v-43h137v-95h-137v-417h-108v563q0 68 35 102.5z" />
    <glyph glyph-name="i.ss03" horiz-adv-x="247" 
d="M186.5 698.5q18.5 -18.5 18.5 -46.5t-18.5 -46.5t-46.5 -18.5t-47 18.5t-19 46.5t19 46.5t47 18.5t46.5 -18.5zM85 417h-81v95h190v-512h-109v417z" />
    <glyph glyph-name="dotlessi.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95z" />
    <glyph glyph-name="iacute.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM96 569l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="ibreve.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM70 707q0 -29 18 -47.5t46 -18.5q29 0 47.5 18.5t18.5 47.5h81q-2 -64 -43 -102.5t-105 -38.5t-103 38t-41 103h81z" />
    <glyph glyph-name="icircumflex.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM135 640l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="idieresis.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM-7 687.5q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM188 687.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="214" 
d="M53 512h108v-512h-108v512zM61.5 704q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="i.loclTRK.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM88.5 703q18.5 18 46.5 18t46.5 -18t18.5 -45q0 -28 -18.5 -46t-46.5 -18t-46.5 18t-18.5 46q0 27 18.5 45z" />
    <glyph glyph-name="igrave.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM-28 707h125l77 -139h-83z" />
    <glyph glyph-name="ij.ss03" horiz-adv-x="465" 
d="M154 698.5q19 -18.5 19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5t47 18.5t47 -18.5zM53 512h108v-512h-108v512zM311 697.5q19 18.5 47 18.5t47 -18.5t19 -46.5t-19 -46t-47 -18t-47 18t-19 46t19 46.5zM247 -105q30 0 43 13.5t13 42.5v466h-82v95h191v-569
q0 -143 -143 -143h-91v95h69z" />
    <glyph glyph-name="ij.ss04" horiz-adv-x="428" 
d="M154 698.5q19 -18.5 19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5t47 18.5t47 -18.5zM53 512h108v-512h-108v512zM274 698.5q19 18.5 47 18.5t47 -18.5t19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5zM267 512h108v-712h-108v712z" />
    <glyph glyph-name="imacron.ss01" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM-10 606v79h291v-79h-291z" />
    <glyph glyph-name="iogonek.ss03" horiz-adv-x="247" 
d="M93 605.5q-19 18.5 -19 46.5t19 46.5t47 18.5t46.5 -18.5t18.5 -46.5t-18.5 -46.5t-46.5 -18.5t-47 18.5zM135 -125h64v-75h-83q-48 0 -78.5 24.5t-30.5 65.5q0 35 20.5 62.5t57.5 55.5v409h-81v95h190v-512h1q-49 -24 -74.5 -46.5t-25.5 -46.5q0 -32 40 -32z" />
    <glyph glyph-name="itilde.ss03" horiz-adv-x="247" 
d="M4 512h190v-512h-109v417h-81v95zM60.5 623q-9.5 -13 -9.5 -32h-65v16q0 43 25 70.5t65 27.5q23 0 37 -7.5t32 -21.5q11 -10 19.5 -15t19.5 -5q16 0 25 13t9 32h66v-16q0 -43 -25 -71t-65 -28q-24 0 -39 8t-30 22q-11 10 -19.5 15t-19.5 5q-16 0 -25.5 -13z" />
    <glyph glyph-name="j.short" horiz-adv-x="219" 
d="M66 698.5q19 18.5 47 18.5t46.5 -18.5t18.5 -46.5t-18.5 -46.5t-46.5 -18.5q-29 0 -47.5 18.5t-18.5 46.5t19 46.5zM5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-47v95h25z" />
    <glyph glyph-name="j.ss03" horiz-adv-x="251" 
d="M97 697.5q19 18.5 47 18.5t47 -18.5t19 -46.5t-19 -46t-47 -18t-47 18t-19 46t19 46.5zM33 -105q30 0 43 13.5t13 42.5v466h-82v95h191v-569q0 -143 -143 -143h-91v95h69z" />
    <glyph glyph-name="j.ss04" horiz-adv-x="214" 
d="M60 698.5q19 18.5 47 18.5t47 -18.5t19 -46.5t-19 -46.5t-47 -18.5t-47 18.5t-19 46.5t19 46.5zM53 512h108v-712h-108v712z" />
    <glyph glyph-name="uni0237.short" horiz-adv-x="220" 
d="M5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-47v95h25z" />
    <glyph glyph-name="uni0237.ss03" horiz-adv-x="251" 
d="M33 -105q30 0 43 13.5t13 42.5v466h-82v95h191v-569q0 -143 -143 -143h-91v95h69z" />
    <glyph glyph-name="uni0237.ss04" horiz-adv-x="214" 
d="M53 512h108v-712h-108v712z" />
    <glyph glyph-name="jcircumflex.short" horiz-adv-x="220" 
d="M5 -105q28 0 41 13.5t13 42.5v561h108v-569q0 -143 -140 -143h-47v95h25zM112 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="jcircumflex.ss03" horiz-adv-x="251" 
d="M33 -105q30 0 43 13.5t13 42.5v466h-82v95h191v-569q0 -143 -143 -143h-91v95h69zM145 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="jcircumflex.ss04" horiz-adv-x="214" 
d="M53 512h108v-712h-108v712zM108 641l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="t.calt" horiz-adv-x="302" 
d="M53 655h108v-143h124v-95h-124v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106 34t-33 105v516z" />
    <glyph glyph-name="t.ss05" horiz-adv-x="325" 
d="M-3 512h90v143h108v-143h120v-95h-120v-417h-108v417h-90v95z" />
    <glyph glyph-name="tbar.calt" horiz-adv-x="302" 
d="M161 417v-99h124v-84h-124v-84q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106 34t-33 105v516h108v-143h124v-95h-124z" />
    <glyph glyph-name="tbar.ss05" horiz-adv-x="344" 
d="M323 417h-120v-108h120v-84h-120v-225h-109v225h-91v84h91v108h-91v95h91v143h109v-143h120v-95z" />
    <glyph glyph-name="tcaron.calt" horiz-adv-x="308" 
d="M53 655h108v-143h124v-95h-124v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106 34t-33 105v516zM242 709h102l-63 -147h-77z" />
    <glyph glyph-name="tcaron.ss05" horiz-adv-x="338" 
d="M-3 512h90v143h108v-143h120v-95h-120v-417h-108v417h-90v95zM278 706h102l-63 -147h-77z" />
    <glyph glyph-name="uni0163.calt" horiz-adv-x="302" 
d="M53 655h108v-143h124v-95h-124v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106 34t-33 105v516zM120 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni0163.ss05" horiz-adv-x="325" 
d="M-3 512h90v143h108v-143h120v-95h-120v-417h-108v417h-90v95zM87 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni021B.calt" horiz-adv-x="302" 
d="M53 655h108v-143h124v-95h-124v-267q0 -30 12 -42.5t41 -12.5h82v-95h-104q-73 0 -106 34t-33 105v516zM120 -50h105l-77 -144h-79z" />
    <glyph glyph-name="uni021B.ss05" horiz-adv-x="325" 
d="M-3 512h90v143h108v-143h120v-95h-120v-417h-108v417h-90v95zM87 -50h105l-77 -144h-79z" />
    <glyph glyph-name="y.ss01" horiz-adv-x="565" 
d="M512 512v-503q0 -98 -60 -156.5t-183 -58.5q-98 0 -157.5 44.5t-66.5 128.5h110q20 -85 123 -85q60 0 93.5 33t33.5 94v69q-27 -38 -63.5 -59t-91.5 -21q-91 0 -147.5 54.5t-56.5 175.5v284h109v-273q0 -73 30 -110t89 -37q61 0 95.5 44t34.5 122v254h108z" />
    <glyph glyph-name="yacute.ss01" horiz-adv-x="565" 
d="M512 512v-503q0 -98 -60 -156.5t-183 -58.5q-98 0 -157.5 44.5t-66.5 128.5h110q20 -85 123 -85q60 0 93.5 33t33.5 94v69q-27 -38 -63.5 -59t-91.5 -21q-91 0 -147.5 54.5t-56.5 175.5v284h109v-273q0 -73 30 -110t89 -37q61 0 95.5 44t34.5 122v254h108zM240 570
l80 138h125l-123 -138h-82z" />
    <glyph glyph-name="ycircumflex.ss01" horiz-adv-x="565" 
d="M512 512v-503q0 -98 -60 -156.5t-183 -58.5q-98 0 -157.5 44.5t-66.5 128.5h110q20 -85 123 -85q60 0 93.5 33t33.5 94v69q-27 -38 -63.5 -59t-91.5 -21q-91 0 -147.5 54.5t-56.5 175.5v284h109v-273q0 -73 30 -110t89 -37q61 0 95.5 44t34.5 122v254h108zM279 641
l-76 -72h-101l127 139h100l127 -139h-102z" />
    <glyph glyph-name="ydieresis.ss01" horiz-adv-x="565" 
d="M512 512v-503q0 -98 -60 -156.5t-183 -58.5q-98 0 -157.5 44.5t-66.5 128.5h110q20 -85 123 -85q60 0 93.5 33t33.5 94v69q-27 -38 -63.5 -59t-91.5 -21q-91 0 -147.5 54.5t-56.5 175.5v284h109v-273q0 -73 30 -110t89 -37q61 0 95.5 44t34.5 122v254h108zM137 688.5
q18 17.5 45 17.5t45 -17.5t18 -43.5t-18 -43.5t-45 -17.5t-45 17.5t-18 43.5t18 43.5zM332 688.5q18 17.5 44 17.5q27 0 45 -17.5t18 -43.5t-18 -43.5t-45 -17.5q-26 0 -44 17.5t-18 43.5t18 43.5z" />
    <glyph glyph-name="ygrave.ss01" horiz-adv-x="565" 
d="M512 512v-503q0 -98 -60 -156.5t-183 -58.5q-98 0 -157.5 44.5t-66.5 128.5h110q20 -85 123 -85q60 0 93.5 33t33.5 94v69q-27 -38 -63.5 -59t-91.5 -21q-91 0 -147.5 54.5t-56.5 175.5v284h109v-273q0 -73 30 -110t89 -37q61 0 95.5 44t34.5 122v254h108zM116 708h125
l77 -139h-83z" />
    <glyph glyph-name="w_w_w" horiz-adv-x="2159" 
d="M2045 512h110l-170 -512h-116l-108 348l-112 -348l-117 -1l-113 347l-115 -346h-116l-108 348l-112 -348l-117 -1l-113 346l-114 -345h-116l-108 348l-112 -348l-117 -1l-167 513h114l117 -392l117 392h99l112 -392l121 392h114l117 -392l117 392h99l112 -392l121 392
h115l117 -392l117 392h99l112 -392z" />
    <glyph glyph-name="zero.osf" horiz-adv-x="618" 
d="M72 390.5q35 59.5 97.5 93.5t139.5 34q78 0 140 -34t97.5 -93.5t35.5 -134.5t-35 -134.5t-97.5 -93.5t-140.5 -34t-140 34t-97 93.5t-35 134.5t35 134.5zM450.5 342q-21.5 39 -58.5 61t-83 22t-83 -22t-58 -61t-21 -86t21 -86t58 -61t83 -22t83 22t58.5 61t21.5 86
t-21.5 86z" />
    <glyph glyph-name="one.osf" horiz-adv-x="313" 
d="M98 398q25 0 37.5 17.5t12.5 45.5v51h107v-512h-109v311q-20 -7 -49 -7h-91v94h92z" />
    <glyph glyph-name="two.osf" horiz-adv-x="515" 
d="M482 90v-90h-449v85l224 128q56 32 81.5 58.5t25.5 64.5q0 40 -30.5 65t-79.5 25q-51 0 -83 -30t-32 -79v-13h-108v13q0 56 27.5 102t78.5 72.5t119 26.5q65 0 114.5 -23t77 -63.5t27.5 -93.5q0 -96 -123 -162l-156 -86h286z" />
    <glyph glyph-name="three.osf" horiz-adv-x="571" 
d="M494 120q41 -39 41 -108q0 -59 -31.5 -104t-89 -69.5t-133.5 -24.5q-118 0 -186.5 61t-68.5 168h109q1 -67 40.5 -103t105.5 -36q62 0 102.5 31t40.5 83q0 49 -38.5 76t-106.5 27h-57v92h52q62 0 98 29.5t36 76.5q0 50 -35 79t-92 29q-58 0 -94.5 -34t-37.5 -90h-108
q0 64 30.5 112.5t85.5 75t126 26.5q73 0 126 -23.5t81.5 -65t28.5 -96.5q0 -62 -36.5 -103t-99.5 -56q70 -14 111 -53z" />
    <glyph glyph-name="four.osf" horiz-adv-x="590" 
d="M341 512h135v-418h100v-94h-100v-180h-105v180h-350v94zM371 405l-237 -311h237v311z" />
    <glyph glyph-name="five.osf" horiz-adv-x="583" 
d="M438.5 251.5q51.5 -30.5 80.5 -84t29 -120.5q0 -74 -33.5 -126.5t-90.5 -79.5t-126 -27q-73 0 -128.5 29t-86.5 78t-34 110h109q6 -54 43.5 -89.5t96.5 -35.5t99 39t40 103q0 62 -38.5 102t-100.5 40q-46 0 -79.5 -21.5t-52.5 -63.5l-107 1l21 406h425v-97h-329l-13 -199
q56 66 159 66q65 0 116.5 -30.5z" />
    <glyph glyph-name="six.osf" horiz-adv-x="593" 
d="M475 642q62 -52 75 -144h-109q-13 48 -46 76t-87 28q-82 0 -125 -61t-43 -168q0 -16 1 -27q2 6 4.5 11l4.5 9q26 44 73 65.5t107 21.5q67 0 118 -29t79 -80.5t28 -118.5q0 -68 -32 -120.5t-89 -81.5t-128 -29q-133 0 -205 107q-30 43 -46.5 106.5t-16.5 134.5
q0 165 71 258.5t203 93.5q101 0 163 -52zM210 326.5q-39 -37.5 -39 -95.5q0 -65 37.5 -106t99.5 -41t99.5 39t37.5 101t-36.5 101t-97.5 39q-62 0 -101 -37.5z" />
    <glyph glyph-name="seven.osf" horiz-adv-x="522" 
d="M16 512h482v-91l-309 -601h-120l308 597h-361v95z" />
    <glyph glyph-name="eight.osf" horiz-adv-x="590" 
d="M513 300.5q40 -43.5 40 -114.5q0 -57 -30.5 -100.5t-89 -67.5t-138.5 -24t-138.5 24t-89 67.5t-30.5 100.5q0 70 40 113t103 58q-53 16 -88 55.5t-35 100.5q0 53 29 94t82.5 64t126.5 23t126.5 -23t82.5 -64t29 -94q0 -58 -35 -98t-87 -56q62 -15 102 -58.5zM202 585.5
q-36 -29.5 -36 -75.5q0 -47 36 -76.5t93 -29.5t93 29.5t36 76.5q0 46 -36 75.5t-93 29.5t-93 -29.5zM401 108.5q42 33.5 42 85.5t-42 86t-106 34t-106 -34t-42 -86t42 -85.5t106 -33.5t106 33.5z" />
    <glyph glyph-name="nine.osf" horiz-adv-x="593" 
d="M539 303.5q16 -63.5 16 -134.5q0 -166 -71 -260.5t-203 -94.5q-102 0 -163.5 52t-73.5 144h108q13 -48 46.5 -76t86.5 -28q84 0 127 62.5t43 171.5q0 5 -2 27q-4 -10 -13 -25q-25 -43 -71.5 -63.5t-104.5 -20.5q-68 0 -119 29t-79 81t-28 119t32 119.5t89 81.5t129 29
q133 0 205 -107q30 -43 46 -106.5zM184.5 186q36.5 -39 98.5 -39t101 39t39 101t-38 101t-100 39q-61 0 -99 -39t-38 -100q0 -63 36.5 -102z" />
    <glyph glyph-name="one.osf.ss08" horiz-adv-x="600" 
d="M555 90v-90h-471v90h181v225q-21 -9 -49 -9h-119v88h120q25 0 37.5 17.5t12.5 45.5v54h107v-421h181z" />
    <glyph glyph-name="seven.osf.ss08" horiz-adv-x="547" 
d="M33 512h490v-91l-309 -601h-120l308 596h-275v-116h-94v212z" />
    <glyph glyph-name="four.osf.ss09" horiz-adv-x="539" 
d="M344 512h137v-692h-106v180h-354v94zM375 406l-239 -312h239v312z" />
    <glyph glyph-name="zero.osf.zero" horiz-adv-x="618" 
d="M449 484q62 -34 97.5 -93.5t35.5 -134.5t-35 -134.5t-97.5 -93.5t-140.5 -34t-140 34t-97 93.5t-35 134.5t35 134.5t97.5 93.5t139.5 34q78 0 140 -34zM225.5 404q-37.5 -22 -59 -61t-21.5 -87q0 -53 26 -94l231 236q-40 28 -93 28q-46 0 -83.5 -22zM392.5 108
q37.5 22 59 61t21.5 87q0 54 -26 95l-230 -237q40 -28 92 -28q46 0 83.5 22z" />
    <glyph glyph-name="one.ss08" horiz-adv-x="544" 
d="M508 97v-97h-466v96h180v375q-14 -7 -36 -7h-123v94h111q24 0 37 18.5t13 46.5v77h108v-603h176z" />
    <glyph glyph-name="seven.ss08" horiz-adv-x="554" 
d="M532 700v-74l-319 -626h-116l316 603h-282v-115h-93v212h494z" />
    <glyph glyph-name="four.ss09" horiz-adv-x="572" 
d="M358 700h142v-700h-111v161h-366v98zM389 590l-254 -331h254v331z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="600" 
d="M37 367q0 99 32 176.5t91.5 120.5t139.5 43t139.5 -43t91.5 -120.5t32 -176.5v-32q0 -99 -32 -176.5t-91.5 -120.5t-139.5 -43t-139.5 43t-91.5 120.5t-32 176.5v32zM453 363q0 112 -40.5 180.5t-112.5 68.5t-112.5 -68.5t-40.5 -180.5v-24q0 -111 40.5 -180t112.5 -69
t112.5 69t40.5 180v24z" />
    <glyph glyph-name="one.tf" horiz-adv-x="600" 
d="M546 97v-97h-466v96h180v375q-14 -7 -36 -7h-123v94h111q24 0 37 18.5t13 46.5v77h108v-603h176z" />
    <glyph glyph-name="two.tf" horiz-adv-x="595" 
d="M313 318q55 45 82.5 84t27.5 85q0 59 -36 92t-98 33q-60 0 -96.5 -38.5t-36.5 -102.5v-13h-111v19q0 66 30.5 118t85.5 82t128 30q117 0 182 -58.5t65 -156.5q0 -71 -37 -124.5t-111 -115.5l-186 -155h338v-97h-496v89z" />
    <glyph glyph-name="three.tf" horiz-adv-x="605" 
d="M515.5 306.5q43.5 -42.5 43.5 -111.5q0 -60 -32.5 -105.5t-92 -70.5t-138.5 -25q-120 0 -190.5 61.5t-70.5 168.5h111q1 -64 44 -101t113 -37q62 0 102.5 32t40.5 85q0 50 -39.5 78.5t-112.5 28.5h-64v92h57q69 0 105.5 30t36.5 80q0 47 -36 74.5t-93 27.5
q-60 0 -98.5 -31.5t-39.5 -85.5h-109q1 97 67 153t176 56q76 0 131.5 -23t85 -65.5t29.5 -97.5q0 -63 -36.5 -103.5t-98.5 -54.5q65 -13 108.5 -55.5z" />
    <glyph glyph-name="four.tf" horiz-adv-x="610" 
d="M13 256l333 444h134v-442h106v-96h-106v-162h-109v162h-358v94zM124 258h247v328z" />
    <glyph glyph-name="five.tf" horiz-adv-x="601" 
d="M448.5 442q52.5 -30 82.5 -84t30 -123q0 -76 -34 -130t-92 -82t-128 -28q-77 0 -134.5 30t-89.5 81.5t-36 114.5h110q6 -57 47 -95t103 -38q37 0 69.5 17t52.5 50t20 80q0 64 -38.5 105t-102.5 41q-42 0 -83 -19t-58 -67l-107 2l21 403h436v-97h-334l-13 -195
q30 33 72 48.5t88 15.5q66 0 118.5 -30z" />
    <glyph glyph-name="six.tf" horiz-adv-x="607" 
d="M52 214q-15 61 -15 131q0 113 33 194t95 124t148 43q97 0 161.5 -53t77.5 -145h-110q-11 50 -46.5 78t-90.5 28q-79 0 -123 -64.5t-44 -174.5v-27q1 2 2.5 6.5t5.5 10.5q24 47 71.5 74t109.5 27q69 0 123 -29.5t83.5 -83t29.5 -123.5q0 -68 -31.5 -122t-89 -84
t-130.5 -30q-71 0 -126 29.5t-90 83.5q-29 46 -44 107zM415 334.5q-38 40.5 -101 40.5q-64 0 -104 -40.5t-40 -105.5q0 -64 39 -104t102 -40q64 0 103 40.5t39 104.5t-38 104.5z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="587" 
d="M50 700h503v-74l-328 -626h-120l326 603h-381v97z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="603" 
d="M518 306q43 -45 43 -117q0 -57 -31 -101.5t-89.5 -69t-139.5 -24.5t-139.5 24.5t-89.5 69t-31 101.5q0 71 42 116t108 59q-56 13 -93 54.5t-37 102.5q0 55 29 97t83.5 65.5t127.5 23.5t127.5 -23.5t83.5 -65.5t29 -97q0 -59 -38 -102t-95 -55q67 -13 110 -58zM206 593.5
q-37 -29.5 -37 -75.5q0 -47 37 -76t95 -29t94.5 29.5t36.5 75.5t-36.5 75.5t-94.5 29.5t-95 -29.5zM408 110q42 33 42 85q0 51 -42.5 85.5t-106.5 34.5q-65 0 -107.5 -34.5t-42.5 -85.5q0 -52 42.5 -85t107.5 -33t107 33z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="607" 
d="M547 489q16 -62 16 -134q0 -170 -72.5 -265.5t-203.5 -95.5q-100 0 -164 53t-77 145h110q12 -49 46.5 -77.5t90.5 -28.5q80 0 124 65t44 175v27q-9 -18 -11 -21q-24 -45 -70 -70.5t-107 -25.5q-70 0 -123.5 28.5t-83 81.5t-29.5 124q0 68 31.5 121.5t88.5 84t131 30.5
q70 0 124 -28.5t89 -80.5q30 -46 46 -108zM185 367q38 -40 101 -40q64 0 104 40t40 105q0 63 -39 103t-102 40q-64 0 -103 -40t-39 -104t38 -104z" />
    <glyph glyph-name="seven.tf.ss08" horiz-adv-x="587" 
d="M553 700v-74l-319 -626h-116l316 603h-282v-115h-93v212h494z" />
    <glyph glyph-name="four.tf.ss09" horiz-adv-x="600" 
d="M372 700h142v-700h-111v161h-366v98zM403 590l-254 -331h254v331z" />
    <glyph glyph-name="zero.tf.zero" horiz-adv-x="600" 
d="M439.5 664q59.5 -43 91.5 -120.5t32 -176.5v-32q0 -99 -32 -176.5t-91.5 -120.5t-139.5 -43t-139.5 43t-91.5 120.5t-32 176.5v32q0 99 32 176.5t91.5 120.5t139.5 43t139.5 -43zM146 339q0 -46 8 -91l269 279q-42 88 -123 88q-73 0 -113.5 -69t-40.5 -183v-24zM454 363
q0 48 -7 84l-267 -278q20 -39 50.5 -60t69.5 -21q73 0 113.5 69t40.5 182v24z" />
    <glyph glyph-name="zero.tosf" horiz-adv-x="600" 
d="M69.5 389.5q34.5 59.5 94.5 93.5t136 34t136 -34t94.5 -93.5t34.5 -134.5q0 -74 -34.5 -134t-94.5 -93.5t-136 -33.5t-136 33.5t-94.5 93.5t-34.5 134q0 75 34.5 134.5zM435.5 341q-20.5 39 -56 61t-79.5 22t-79.5 -22t-56 -61t-20.5 -86t20.5 -85.5t56 -60.5t79.5 -22
t79.5 22t56 60.5t20.5 85.5t-20.5 86z" />
    <glyph glyph-name="one.tosf" horiz-adv-x="600" 
d="M555 90v-90h-471v90h181v225q-21 -9 -49 -9h-119v88h120q25 0 37.5 17.5t12.5 45.5v54h107v-421h181z" />
    <glyph glyph-name="two.tosf" horiz-adv-x="600" 
d="M531 90v-90h-449v85l224 128q56 32 81.5 58.5t25.5 64.5q0 40 -30.5 65t-79.5 25q-51 0 -83 -30t-32 -79v-13h-108v13q0 56 27.5 102t78.5 72.5t119 26.5q65 0 114.5 -23t77 -63.5t27.5 -93.5q0 -96 -123 -162l-156 -86h286z" />
    <glyph glyph-name="three.tosf" horiz-adv-x="600" 
d="M514.5 118.5q43.5 -42.5 43.5 -111.5q0 -60 -32.5 -105.5t-92 -70.5t-138.5 -25q-120 0 -190.5 61.5t-70.5 168.5h111q1 -64 44 -101t113 -37q62 0 102.5 32t40.5 85q0 50 -39.5 78.5t-112.5 28.5h-64v92h57q69 0 105.5 30t36.5 80q0 47 -36 74.5t-93 27.5
q-60 0 -98.5 -31.5t-39.5 -85.5h-109q1 97 67 153t176 56q76 0 131.5 -23t85 -65.5t29.5 -97.5q0 -63 -36.5 -103.5t-98.5 -54.5q65 -13 108.5 -55.5z" />
    <glyph glyph-name="four.tosf" horiz-adv-x="600" 
d="M341 512h135v-418h100v-94h-100v-180h-105v180h-350v94zM371 405l-237 -311h237v311z" />
    <glyph glyph-name="five.tosf" horiz-adv-x="600" 
d="M444.5 252.5q51.5 -30.5 80.5 -84t29 -120.5q0 -74 -33.5 -126.5t-90.5 -79.5t-126 -27q-73 0 -128.5 29t-86.5 78t-34 110h109q6 -54 43.5 -89.5t96.5 -35.5t99 39t40 103q0 62 -38.5 102t-100.5 40q-46 0 -79.5 -21.5t-52.5 -63.5l-107 1l21 406h425v-97h-329l-13 -199
q56 66 159 66q65 0 116.5 -30.5z" />
    <glyph glyph-name="six.tosf" horiz-adv-x="600" 
d="M478 642q62 -52 75 -144h-109q-13 48 -46 76t-87 28q-82 0 -125 -61t-43 -168q0 -16 1 -27q2 6 4.5 11l4.5 9q26 44 73 65.5t107 21.5q67 0 118 -29t79 -80.5t28 -118.5q0 -68 -32 -120.5t-89 -81.5t-128 -29q-133 0 -205 107q-30 43 -46.5 106.5t-16.5 134.5
q0 165 71 258.5t203 93.5q101 0 163 -52zM213 326.5q-39 -37.5 -39 -95.5q0 -65 37.5 -106t99.5 -41t99.5 39t37.5 101t-36.5 101t-97.5 39q-62 0 -101 -37.5z" />
    <glyph glyph-name="seven.tosf" horiz-adv-x="600" 
d="M50 512h482v-91l-309 -601h-120l308 597h-361v95z" />
    <glyph glyph-name="eight.tosf" horiz-adv-x="600" 
d="M518 300.5q40 -43.5 40 -114.5q0 -57 -30.5 -100.5t-89 -67.5t-138.5 -24t-138.5 24t-89 67.5t-30.5 100.5q0 70 40 113t103 58q-53 16 -88 55.5t-35 100.5q0 53 29 94t82.5 64t126.5 23t126.5 -23t82.5 -64t29 -94q0 -58 -35 -98t-87 -56q62 -15 102 -58.5zM207 585.5
q-36 -29.5 -36 -75.5q0 -47 36 -76.5t93 -29.5t93 29.5t36 76.5q0 46 -36 75.5t-93 29.5t-93 -29.5zM406 108.5q42 33.5 42 85.5t-42 86t-106 34t-106 -34t-42 -86t42 -85.5t106 -33.5t106 33.5z" />
    <glyph glyph-name="nine.tosf" horiz-adv-x="600" 
d="M542 303.5q16 -63.5 16 -134.5q0 -166 -71 -260.5t-203 -94.5q-102 0 -163.5 52t-73.5 144h108q13 -48 46.5 -76t86.5 -28q84 0 127 62.5t43 171.5q0 5 -2 27q-4 -10 -13 -25q-25 -43 -71.5 -63.5t-104.5 -20.5q-68 0 -119 29t-79 81t-28 119t32 119.5t89 81.5t129 29
q133 0 205 -107q30 -43 46 -106.5zM187.5 186q36.5 -39 98.5 -39t101 39t39 101t-38 101t-100 39q-61 0 -99 -39t-38 -100q0 -63 36.5 -102z" />
    <glyph glyph-name="seven.tosf.ss08" horiz-adv-x="590" 
d="M49 512h490v-91l-309 -601h-120l308 596h-275v-116h-94v212z" />
    <glyph glyph-name="four.tosf.ss09" horiz-adv-x="600" 
d="M372 512h142v-700h-111v161h-366v98zM403 402l-254 -331h254v331z" />
    <glyph glyph-name="zero.tosf.zero" horiz-adv-x="600" 
d="M436 483q60 -34 94.5 -93.5t34.5 -134.5q0 -74 -34.5 -134t-94.5 -93.5t-136 -33.5t-136 33.5t-94.5 93.5t-34.5 134q0 75 34.5 134.5t94.5 93.5t136 34t136 -34zM220 403q-36 -22 -56.5 -61t-20.5 -87q0 -50 22 -89l225 230q-40 29 -90 29q-44 0 -80 -22zM380 108
q36 22 56.5 60.5t20.5 86.5q0 52 -23 92l-225 -232q42 -29 91 -29q44 0 80 22z" />
    <glyph glyph-name="zero.zero" horiz-adv-x="635" 
d="M460.5 663.5q62.5 -42.5 97.5 -120t35 -177.5v-32q0 -104 -34.5 -181t-97 -118t-143.5 -41q-82 0 -144.5 41t-97 118t-34.5 181v32q0 100 34.5 177.5t97.5 120t144 42.5q80 0 142.5 -42.5zM152 338q0 -56 10 -97l282 293q-22 38 -54 57.5t-72 19.5q-76 0 -121 -66.5
t-45 -182.5v-24zM483 362q0 50 -10 98l-283 -294q23 -38 55.5 -57.5t72.5 -19.5q75 0 120 66t45 183v24z" />
    <glyph glyph-name="u1F10C.zero" horiz-adv-x="793" 
d="M212 660q-82 -46 -128.5 -127.5t-46.5 -182.5t46.5 -182.5t128.5 -127.5t184 -46t184 46t128.5 127.5t46.5 182.5t-46.5 182.5t-128.5 127.5t-184 46t-184 -46zM506 487q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM340.5 438.5
q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM451.5 262q20.5 33 20.5 88q0 30 -5 47l-125 -138q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="u1F10B.zero" horiz-adv-x="797" 
d="M583 660q82 -46 129 -127.5t47 -182.5t-47 -182.5t-129 -127.5t-185 -46t-185 46t-129 127.5t-47 182.5t47 182.5t129 127.5t185 46t185 -46zM253.5 601q-64.5 -37 -101 -103t-36.5 -148t36.5 -148t101 -103t144.5 -37q81 0 145.5 37t101 103t36.5 148t-36.5 148
t-101 103t-145.5 37q-80 0 -144.5 -37zM508 487q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM342.5 438.5q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM453.5 262q20.5 33 20.5 88q0 30 -5 47l-125 -138
q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="344" 
d="M62 319q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM227 270q-20 33 -55 33t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="190" 
d="M83 0v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="320" 
d="M21 239q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="330" 
d="M66 338.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="346" 
d="M179 364h92v-212h56v-66h-56v-86h-73v86h-179v66zM198 290l-105 -138h105v138z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="316" 
d="M274 299h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="326" 
d="M61.5 317q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM216 161.5q-18 17.5 -46 17.5
q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="300" 
d="M18 365h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="332" 
d="M39 156q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM211.5 299q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5
t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM217 144.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="326" 
d="M265 47q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141zM110.5 202.5q17.5 -17.5 46.5 -17.5t46.5 17
t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="344" 
d="M62 656q41 51 110 51t110 -51t41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137zM227 607q-20 33 -55 33t-55 -33t-20 -88t20 -87.5t55 -32.5t55 32.5t20 87.5t-20 88z" />
    <glyph glyph-name="one.numr" horiz-adv-x="190" 
d="M83 337v232q-6 -8 -19 -8h-64v69h62q10 0 16.5 9.5t6.5 22.5v39h71v-364h-73z" />
    <glyph glyph-name="two.numr" horiz-adv-x="320" 
d="M21 575q0 59 39 95t97 36q63 0 101 -32.5t38 -85.5q0 -36 -18 -61.5t-52 -51.5l-91 -70h164v-69h-276v65l147 109q24 18 36.5 35.5t12.5 40.5q0 24 -17 38.5t-44 14.5q-29 0 -46.5 -17.5t-17.5 -49.5v-4h-73v7z" />
    <glyph glyph-name="three.numr" horiz-adv-x="330" 
d="M66 676.5q37 31.5 99 31.5t98.5 -28t36.5 -74q0 -30 -17.5 -49.5t-43.5 -26.5q32 -6 50.5 -28t18.5 -55q0 -53 -39 -84t-107 -31q-69 0 -106 32.5t-38 92.5h74q1 -28 19.5 -43.5t50.5 -15.5q29 0 48 14t19 37t-19 36.5t-52 13.5h-21v50h22q29 0 46.5 12.5t17.5 34.5
q0 21 -16 33.5t-42 12.5q-28 0 -45 -14t-18 -38h-74q1 55 38 86.5z" />
    <glyph glyph-name="four.numr" horiz-adv-x="346" 
d="M179 700h92v-212h56v-66h-56v-86h-73v86h-179v66zM198 626l-105 -138h105v138z" />
    <glyph glyph-name="five.numr" horiz-adv-x="316" 
d="M274 635h-177l-6 -87q15 16 36 25t46 9q54 0 87.5 -34.5t33.5 -89.5q0 -58 -37.5 -93t-97.5 -35q-56 0 -93.5 31t-41.5 82h73q4 -21 21 -33.5t42 -12.5q26 0 42.5 17t16.5 44q0 28 -16.5 44.5t-43.5 16.5q-20 1 -35.5 -8.5t-24.5 -25.5h-70l10 215h235v-65z" />
    <glyph glyph-name="six.numr" horiz-adv-x="326" 
d="M61.5 654q42.5 53 112.5 53q50 0 86.5 -29t43.5 -80h-71q-4 23 -21.5 37t-41.5 14q-36 0 -58.5 -32t-22.5 -81v-24q8 26 31 43.5t61 17.5q57 0 91.5 -33t34.5 -87q0 -55 -37.5 -88.5t-99.5 -33.5q-73 0 -112 49.5t-39 132.5q0 88 42.5 141zM216 498.5q-18 17.5 -46 17.5
q-29 0 -47 -17t-18 -45t18 -45.5t47 -17.5t46.5 17t17.5 45t-18 45.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="300" 
d="M18 700h267v-61l-153 -304h-80l152 300l-186 -1v66z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="332" 
d="M39 493q19 24 53 34q-60 22 -60 82q0 43 37 70.5t98 27.5q60 0 97.5 -27.5t37.5 -70.5q0 -58 -59 -82q33 -10 51 -33.5t18 -57.5q0 -46 -39 -75.5t-106 -29.5q-66 0 -106.5 29.5t-40.5 75.5q0 33 19 57zM211.5 636q-17.5 14 -44.5 14t-44.5 -14t-17.5 -36t17.5 -35.5
t44.5 -13.5t44.5 13.5t17.5 35.5t-17.5 36zM217 481.5q-20 15.5 -50 15.5t-50.5 -15.5t-20.5 -38.5t20 -38t51 -15q30 0 50 15t20 38t-20 38.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="326" 
d="M265 384q-42 -53 -112 -53q-51 0 -87 29t-44 80h71q5 -23 22 -37t41 -14q37 0 59.5 31t22.5 82v24q-8 -26 -31.5 -43.5t-60.5 -17.5q-58 0 -92.5 33t-34.5 87q0 56 37.5 89t100.5 33q72 0 111 -49.5t39 -132.5q0 -88 -42 -141zM110.5 539.5q17.5 -17.5 46.5 -17.5
t46.5 17t17.5 45t-17.5 45.5t-46.5 17.5t-46.5 -17t-17.5 -45t17.5 -45.5z" />
    <glyph glyph-name="uni2080.zero" horiz-adv-x="344" 
d="M282 219q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM116.5 170.5q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM227.5 -6q20.5 33 20.5 88q0 30 -5 47l-125 -138q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="zero.dnom.zero" horiz-adv-x="344" 
d="M282 319q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM116.5 270.5q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM227.5 94q20.5 33 20.5 88q0 30 -5 47l-125 -138q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="zero.numr.zero" horiz-adv-x="344" 
d="M282 656q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM116.5 607.5q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM227.5 431q20.5 33 20.5 88q0 30 -5 47l-125 -138q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="uni2070.zero" horiz-adv-x="344" 
d="M282 756q41 -51 41 -137t-41 -137t-110 -51t-110 51t-41 137t41 137t110 51t110 -51zM116.5 707.5q-20.5 -33.5 -20.5 -88.5q0 -29 5 -48l126 138q-21 32 -55 32q-35 0 -55.5 -33.5zM227.5 531q20.5 33 20.5 88q0 30 -5 47l-125 -138q21 -30 54 -30q35 0 55.5 33z" />
    <glyph glyph-name="backslash.case" horiz-adv-x="333" 
d="M116 700l232 -700h-109l-231 700h108z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="264" 
d="M82.5 398q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="colon.case" horiz-adv-x="224" 
d="M62.5 555q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49zM62.5 192q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="semicolon.case" horiz-adv-x="224" 
d="M168 177q25 -33 25 -82q0 -72 -43 -112t-118 -41v58q44 1 66 23t22 52q0 9 -1 13q-13 -10 -33 -10q-26 0 -42.5 16.5t-16.5 47.5q0 29 21 48.5t55 19.5q40 0 65 -33zM58.5 555q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="slash.case" horiz-adv-x="333" 
d="M228 700h108l-231 -700h-109z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="362" 
d="M244 583.5q-21 -20.5 -21 -61.5q0 -22 4 -42q1 -6 2 -15t1 -20q0 -38 -23.5 -62.5t-57.5 -32.5q35 -8 58.5 -32t23.5 -62q0 -11 -4 -35q-4 -21 -4 -43q0 -41 21 -61.5t67 -21.5h33v-95h-45q-83 0 -129 32t-46 110q0 9 4 39q3 23 3 39q0 45 -24.5 65t-75.5 20v92
q51 0 75.5 19.5t24.5 64.5q0 16 -3 37q-4 28 -4 40q0 78 46 110t129 32h45v-95h-33q-46 -1 -67 -21.5z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="362" 
d="M255.5 416.5q24.5 -19.5 75.5 -19.5v-92q-51 0 -75.5 -20t-24.5 -65q0 -16 3 -39q4 -30 4 -39q0 -78 -46 -110t-129 -32h-45v95h33q46 1 67 21.5t21 61.5q0 22 -4 43q-4 24 -4 35q0 38 23.5 62t58.5 32q-34 8 -57.5 32.5t-23.5 62.5q0 11 1 20t2 15q4 20 4 42
q0 41 -21 61.5t-67 21.5h-33v95h45q83 0 129 -32t46 -110q0 -12 -4 -40q-3 -21 -3 -37q0 -45 24.5 -64.5z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="317" 
d="M58 700h241v-95h-140v-510h140v-95h-241v700z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="317" 
d="M259 700v-700h-241v95h140v510h-140v95h241z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="280" 
d="M74 539q27 93 70 161h112q-49 -72 -75.5 -163.5t-26.5 -186.5t26.5 -187t75.5 -163h-112q-43 67 -70 160.5t-27 189.5t27 189z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="280" 
d="M24 0q49 71 75.5 163t26.5 187t-26.5 186.5t-75.5 163.5h112q43 -68 70 -161t27 -189t-27 -189.5t-70 -160.5h-112z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="933" 
d="M52 402h829v-94h-829v94z" />
    <glyph glyph-name="endash.case" horiz-adv-x="604" 
d="M52 308v94h500v-94h-500z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="444" 
d="M52 308v94h340v-94h-340z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="604" 
d="M313 607l-154 -256l154 -256h-109l-156 256l156 256h109zM555 606l-154 -256l154 -256h-109l-156 256l156 256h109z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="604" 
d="M291 607h109l156 -256l-156 -256h-109l154 256zM49 606h109l156 -256l-156 -256h-109l154 256z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="360" 
d="M199 605h117l-154 -255l154 -255h-117l-156 255z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="360" 
d="M161 605l156 -255l-156 -255h-117l154 255l-154 255h117z" />
    <glyph glyph-name="dotmath.case" horiz-adv-x="224" 
d="M62.5 407q19.5 19 49.5 19t50 -19t20 -49t-20 -49t-50 -19t-49.5 19t-19.5 49t19.5 49z" />
    <glyph glyph-name="plus.case" 
d="M210 570h99v-169h160v-91h-160v-169h-99v169h-159v91h159v169z" />
    <glyph glyph-name="minus.case" 
d="M51 310v91h418v-91h-418z" />
    <glyph glyph-name="multiply.case" 
d="M120 561l143 -142l139 140l64 -64l-139 -140l142 -142l-70 -69l-142 142l-139 -140l-64 65l140 139l-142 142z" />
    <glyph glyph-name="divide.case" 
d="M213 591.5q19 18.5 48 18.5t48.5 -18.5t19.5 -47.5t-19.5 -47.5t-48.5 -18.5t-48 18.5t-19 47.5t19 47.5zM51 310v91h418v-91h-418zM213 226.5q19 18.5 48 18.5t48.5 -18.5t19.5 -47.5t-19.5 -47.5t-48.5 -18.5t-48 18.5t-19 47.5t19 47.5z" />
    <glyph glyph-name="equal.case" 
d="M61 407v92h398v-92h-398zM61 196v91h398v-91h-398z" />
    <glyph glyph-name="notequal.case" 
d="M459 407h-130l-40 -120h170v-91h-201l-31 -94h-102l31 94h-95v91h126l40 120h-166v92h196l30 89h102l-30 -89h100v-92z" />
    <glyph glyph-name="greater.case" 
d="M453 405v-102l-386 -167v105l266 113l-266 113v105z" />
    <glyph glyph-name="less.case" 
d="M67 303v102l386 167v-105l-266 -113l266 -113v-105z" />
    <glyph glyph-name="greaterequal.case" 
d="M453 425v-102l-386 -167v105l266 113l-266 113v105zM67 16v91h386v-91h-386z" />
    <glyph glyph-name="lessequal.case" 
d="M67 323v102l386 167v-105l-266 -113l266 -113v-105zM67 16v91h386v-91h-386z" />
    <glyph glyph-name="plusminus.case" 
d="M210 594h99v-169h160v-91h-160v-169h-99v169h-159v91h159v169zM51 15v91h418v-91h-418z" />
    <glyph glyph-name="approxequal.case" 
d="M51 134q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88zM51 358q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28
q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88z" />
    <glyph glyph-name="asciitilde.case" 
d="M51 263q0 77 32 118t90 41q34 0 56.5 -10t48.5 -28q17 -13 28.5 -18.5t24.5 -5.5q27 0 38.5 16t11.5 46h88q0 -77 -32 -117.5t-90 -40.5q-34 0 -56.5 9.5t-49.5 28.5q-16 12 -27.5 18t-24.5 6q-50 0 -50 -63h-88z" />
    <glyph glyph-name="logicalnot.case" 
d="M51 423h379v-255h-96v163h-283v92z" />
    <glyph glyph-name="arrowup.case" horiz-adv-x="744" 
d="M43 376l329 331l330 -331l-79 -79l-195 199v-497h-111v497l-196 -199z" />
    <glyph glyph-name="uni2197.case" horiz-adv-x="664" 
d="M154 700l467 1v-467h-111l2 280l-391 -391l-78 78l391 390l-280 -2v111z" />
    <glyph glyph-name="arrowright.case" horiz-adv-x="794" 
d="M420 681l331 -329l-331 -330l-79 79l199 195h-497v111h497l-199 195z" />
    <glyph glyph-name="uni2198.case" horiz-adv-x="664" 
d="M154 1v111l280 -2l-391 390l78 78l391 -391l-2 279h111v-466z" />
    <glyph glyph-name="arrowdown.case" horiz-adv-x="744" 
d="M43 323l78 79l196 -199v497h111v-497l195 199l79 -79l-330 -331z" />
    <glyph glyph-name="uni2199.case" horiz-adv-x="664" 
d="M543 578l78 -79l-391 -390l280 2v-111h-467l1 466h111l-3 -279z" />
    <glyph glyph-name="arrowleft.case" horiz-adv-x="794" 
d="M374 681l79 -79l-199 -195h497v-111h-497l199 -195l-79 -79l-331 330z" />
    <glyph glyph-name="uni2196.case" horiz-adv-x="664" 
d="M510 700v-111l-280 2l391 -390l-78 -78l-391 391l3 -280h-111l-1 467z" />
    <glyph glyph-name="arrowboth.case" horiz-adv-x="993" 
d="M988 349l-311 -308l-77 77l175 176h-556l175 -175l-78 -78l-310 308l79 79l231 232l78 -78l-177 -176h559l-177 176l79 78z" />
    <glyph glyph-name="bar.case" horiz-adv-x="242" 
d="M64 700h114v-700h-114v700z" />
    <glyph glyph-name="brokenbar.case" horiz-adv-x="242" 
d="M64 700h114v-317h-114v317zM64 313h114v-313h-114v313z" />
    <hkern u1="&#x32;" g2="slash.case" k="-2" />
    <hkern u1="&#x32;" u2="&#x3a;" k="10" />
    <hkern u1="&#x32;" u2="&#x38;" k="4" />
    <hkern u1="&#x32;" u2="&#x32;" k="2" />
    <hkern u1="&#x33;" g2="slash.case" k="4" />
    <hkern u1="&#x33;" u2="&#x33;" k="10" />
    <hkern u1="&#x36;" g2="slash.case" k="10" />
    <hkern u1="&#x38;" g2="backslash.case" k="15" />
    <hkern u1="&#x38;" u2="&#x3f;" k="10" />
    <hkern u1="&#x39;" g2="slash.case" k="10" />
    <hkern u1="&#x3a;" u2="&#x31;" k="4" />
    <hkern u1="&#x3f;" u2="&#xbf;" k="60" />
    <hkern u1="B" u2="&#xc6;" k="41" />
    <hkern u1="F" u2="&#xc6;" k="140" />
    <hkern u1="J" u2="&#xc6;" k="34" />
    <hkern u1="P" u2="&#xc6;" k="168" />
    <hkern u1="Q" g2="slash.case" k="-12" />
    <hkern u1="Q" u2="&#x2026;" k="6" />
    <hkern u1="Q" u2="&#x201e;" k="6" />
    <hkern u1="Q" u2="&#x201a;" k="6" />
    <hkern u1="Q" u2="&#x1fc;" k="1" />
    <hkern u1="Q" u2="&#x104;" k="1" />
    <hkern u1="Q" u2="&#x102;" k="1" />
    <hkern u1="Q" u2="&#x100;" k="1" />
    <hkern u1="Q" u2="&#xc6;" k="1" />
    <hkern u1="Q" u2="&#xc5;" k="1" />
    <hkern u1="Q" u2="&#xc4;" k="1" />
    <hkern u1="Q" u2="&#xc3;" k="1" />
    <hkern u1="Q" u2="&#xc2;" k="1" />
    <hkern u1="Q" u2="&#xc1;" k="1" />
    <hkern u1="Q" u2="&#xc0;" k="1" />
    <hkern u1="Q" u2="_" k="14" />
    <hkern u1="Q" u2="X" k="24" />
    <hkern u1="Q" u2="A" k="1" />
    <hkern u1="Q" u2="&#x2f;" k="16" />
    <hkern u1="Q" u2="&#x2e;" k="6" />
    <hkern u1="Q" u2="&#x2c;" k="6" />
    <hkern u1="S" u2="&#xc6;" k="40" />
    <hkern u1="T" u2="&#xc6;" k="146" />
    <hkern u1="U" u2="&#xc6;" k="57" />
    <hkern u1="V" u2="&#xc6;" k="136" />
    <hkern u1="W" u2="&#xc6;" k="120" />
    <hkern u1="Y" u2="&#xc6;" k="156" />
    <hkern u1="\" u2="\" k="98" />
    <hkern u1="\" u2="&#x39;" k="35" />
    <hkern u1="\" u2="&#x38;" k="30" />
    <hkern u1="\" u2="&#x36;" k="30" />
    <hkern u1="\" u2="&#x35;" k="14" />
    <hkern u1="\" u2="&#x31;" k="34" />
    <hkern u1="f" g2="f_t.liga" k="1" />
    <hkern u1="&#xbf;" u2="&#x3f;" k="90" />
    <hkern u1="&#xbf;" u2="&#x32;" k="30" />
    <hkern u1="&#xbf;" u2="&#x31;" k="24" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="57" />
    <hkern u1="&#xda;" u2="&#xc6;" k="57" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="57" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="57" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="156" />
    <hkern u1="&#xde;" g2="slash.case" k="20" />
    <hkern u1="&#xde;" u2="&#xc6;" k="92" />
    <hkern u1="&#x132;" u2="&#xc6;" k="57" />
    <hkern u1="&#x134;" u2="&#xc6;" k="34" />
    <hkern u1="&#x15a;" u2="&#xc6;" k="40" />
    <hkern u1="&#x15c;" u2="&#xc6;" k="40" />
    <hkern u1="&#x15e;" u2="&#xc6;" k="40" />
    <hkern u1="&#x160;" u2="&#xc6;" k="40" />
    <hkern u1="&#x162;" u2="&#xc6;" k="146" />
    <hkern u1="&#x164;" u2="&#xc6;" k="146" />
    <hkern u1="&#x165;" g2="jcircumflex.ss03" k="-27" />
    <hkern u1="&#x165;" g2="jcircumflex.short" k="-27" />
    <hkern u1="&#x165;" g2="uni0237.ss04" k="-27" />
    <hkern u1="&#x165;" g2="uni0237.ss03" k="-27" />
    <hkern u1="&#x165;" g2="uni0237.short" k="-27" />
    <hkern u1="&#x165;" g2="j.ss03" k="-27" />
    <hkern u1="&#x165;" g2="j.short" k="-27" />
    <hkern u1="&#x165;" g2="ij.ss04" k="-24" />
    <hkern u1="&#x165;" g2="ij.ss03" k="-24" />
    <hkern u1="&#x165;" g2="i.loclTRK" k="-24" />
    <hkern u1="&#x165;" g2="f.calt" k="-24" />
    <hkern u1="&#x165;" u2="&#x201d;" k="-35" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-35" />
    <hkern u1="&#x165;" u2="&#x2019;" k="-35" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-35" />
    <hkern u1="&#x165;" u2="&#x237;" k="-27" />
    <hkern u1="&#x165;" u2="&#x142;" k="-24" />
    <hkern u1="&#x165;" u2="&#x140;" k="-24" />
    <hkern u1="&#x165;" u2="&#x13e;" k="-24" />
    <hkern u1="&#x165;" u2="&#x13c;" k="-24" />
    <hkern u1="&#x165;" u2="&#x13a;" k="-24" />
    <hkern u1="&#x165;" u2="&#x137;" k="-24" />
    <hkern u1="&#x165;" u2="&#x135;" k="-27" />
    <hkern u1="&#x165;" u2="&#x133;" k="-24" />
    <hkern u1="&#x165;" u2="&#x131;" k="-24" />
    <hkern u1="&#x165;" u2="&#x12f;" k="-24" />
    <hkern u1="&#x165;" u2="&#x12d;" k="-24" />
    <hkern u1="&#x165;" u2="&#x12b;" k="-24" />
    <hkern u1="&#x165;" u2="&#x129;" k="-24" />
    <hkern u1="&#x165;" u2="&#x127;" k="-24" />
    <hkern u1="&#x165;" u2="&#x125;" k="-24" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-24" />
    <hkern u1="&#x165;" u2="&#xef;" k="-24" />
    <hkern u1="&#x165;" u2="&#xee;" k="-24" />
    <hkern u1="&#x165;" u2="&#xed;" k="-24" />
    <hkern u1="&#x165;" u2="&#xec;" k="-24" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-49" />
    <hkern u1="&#x165;" u2="l" k="-24" />
    <hkern u1="&#x165;" u2="k" k="-24" />
    <hkern u1="&#x165;" u2="j" k="-27" />
    <hkern u1="&#x165;" u2="i" k="-24" />
    <hkern u1="&#x165;" u2="h" k="-24" />
    <hkern u1="&#x165;" u2="b" k="-24" />
    <hkern u1="&#x165;" u2="]" k="-49" />
    <hkern u1="&#x165;" u2="&#x29;" k="-49" />
    <hkern u1="&#x165;" u2="&#x27;" k="-35" />
    <hkern u1="&#x165;" u2="&#x22;" k="-35" />
    <hkern u1="&#x165;" u2="&#x21;" k="-19" />
    <hkern u1="&#x166;" u2="&#xc6;" k="146" />
    <hkern u1="&#x168;" u2="&#xc6;" k="57" />
    <hkern u1="&#x16a;" u2="&#xc6;" k="57" />
    <hkern u1="&#x16c;" u2="&#xc6;" k="57" />
    <hkern u1="&#x16e;" u2="&#xc6;" k="57" />
    <hkern u1="&#x170;" u2="&#xc6;" k="57" />
    <hkern u1="&#x172;" u2="&#xc6;" k="57" />
    <hkern u1="&#x174;" u2="&#xc6;" k="120" />
    <hkern u1="&#x176;" u2="&#xc6;" k="156" />
    <hkern u1="&#x178;" u2="&#xc6;" k="156" />
    <hkern u1="&#x218;" u2="&#xc6;" k="40" />
    <hkern u1="&#x21a;" u2="&#xc6;" k="146" />
    <hkern u1="&#x1e80;" u2="&#xc6;" k="120" />
    <hkern u1="&#x1e82;" u2="&#xc6;" k="120" />
    <hkern u1="&#x1e84;" u2="&#xc6;" k="120" />
    <hkern u1="&#x1ef2;" u2="&#xc6;" k="156" />
    <hkern g1="J.ss06" u2="&#xc6;" k="57" />
    <hkern g1="Jcircumflex.ss06" u2="&#xc6;" k="57" />
    <hkern g1="f.calt" g2="f_t.liga" k="1" />
    <hkern g1="f_f.liga" g2="f_t.liga" k="1" />
    <hkern g1="f_f.liga" g2="f_f_t.liga" k="-17" />
    <hkern g1="two.osf" g2="two.osf" k="10" />
    <hkern g1="two.osf" u2="&#x3f;" k="13" />
    <hkern g1="three.osf" u2="&#x3f;" k="21" />
    <hkern g1="four.osf" g2="eight.osf" k="4" />
    <hkern g1="four.osf" g2="six.osf" k="8" />
    <hkern g1="four.osf" g2="two.osf" k="-3" />
    <hkern g1="four.osf" g2="one.osf" k="21" />
    <hkern g1="four.osf" u2="&#x3f;" k="42" />
    <hkern g1="five.osf" g2="six.osf" k="16" />
    <hkern g1="five.osf" g2="one.osf" k="23" />
    <hkern g1="five.osf" u2="&#x3f;" k="23" />
    <hkern g1="six.osf" g2="three.osf" k="5" />
    <hkern g1="eight.osf" g2="three.osf" k="6" />
    <hkern g1="nine.osf" u2="&#x3f;" k="46" />
    <hkern g1="backslash.case" u2="&#x31;" k="43" />
    <hkern g1="slash.case" u2="&#x39;" k="10" />
    <hkern g1="slash.case" u2="&#x38;" k="15" />
    <hkern g1="slash.case" u2="&#x36;" k="10" />
    <hkern g1="slash.case" u2="&#x35;" k="9" />
    <hkern g1="slash.case" u2="&#x33;" k="20" />
    <hkern g1="five"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="21" />
    <hkern g1="five"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="31" />
    <hkern g1="five"
	g2="asterisk,registered,degree,trademark"
	k="31" />
    <hkern g1="five"
	g2="seven,seven.ss08"
	k="25" />
    <hkern g1="five"
	g2="slash"
	k="20" />
    <hkern g1="five"
	g2="underscore"
	k="44" />
    <hkern g1="five"
	g2="one"
	k="14" />
    <hkern g1="five"
	g2="question"
	k="30" />
    <hkern g1="five"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="8" />
    <hkern g1="five"
	g2="two"
	k="6" />
    <hkern g1="four"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="14" />
    <hkern g1="four"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="50" />
    <hkern g1="four"
	g2="asterisk,registered,degree,trademark"
	k="49" />
    <hkern g1="four"
	g2="seven,seven.ss08"
	k="40" />
    <hkern g1="four"
	g2="underscore"
	k="40" />
    <hkern g1="four"
	g2="one"
	k="19" />
    <hkern g1="four"
	g2="question"
	k="46" />
    <hkern g1="four"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="50" />
    <hkern g1="four"
	g2="two"
	k="11" />
    <hkern g1="four"
	g2="colon"
	k="10" />
    <hkern g1="four"
	g2="slash.case"
	k="8" />
    <hkern g1="one"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="seven,seven.ss08"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="seven,seven.ss08"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-16" />
    <hkern g1="seven,seven.ss08"
	g2="asterisk,registered,degree,trademark"
	k="-7" />
    <hkern g1="seven,seven.ss08"
	g2="seven,seven.ss08"
	k="-13" />
    <hkern g1="seven,seven.ss08"
	g2="slash"
	k="77" />
    <hkern g1="seven,seven.ss08"
	g2="underscore"
	k="128" />
    <hkern g1="seven,seven.ss08"
	g2="one"
	k="-4" />
    <hkern g1="seven,seven.ss08"
	g2="question"
	k="10" />
    <hkern g1="seven,seven.ss08"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="44" />
    <hkern g1="seven,seven.ss08"
	g2="two"
	k="3" />
    <hkern g1="seven,seven.ss08"
	g2="colon"
	k="40" />
    <hkern g1="seven,seven.ss08"
	g2="slash.case"
	k="31" />
    <hkern g1="seven,seven.ss08"
	g2="four,four.ss09"
	k="60" />
    <hkern g1="seven,seven.ss08"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="74" />
    <hkern g1="seven,seven.ss08"
	g2="zero,zero.zero"
	k="10" />
    <hkern g1="seven,seven.ss08"
	g2="eight"
	k="14" />
    <hkern g1="seven,seven.ss08"
	g2="three"
	k="9" />
    <hkern g1="seven,seven.ss08"
	g2="six"
	k="8" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-17" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="asterisk,registered,degree,trademark"
	k="-10" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="slash"
	k="8" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="underscore"
	k="80" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="question"
	k="-6" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="16" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="four.osf,four.osf.ss09"
	k="66" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="seven.osf,seven.osf.ss08"
	k="-10" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="one.osf"
	k="-6" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="three.osf"
	k="3" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="five.osf"
	k="4" />
    <hkern g1="zero,zero.zero"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="zero,zero.zero"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="8" />
    <hkern g1="zero,zero.zero"
	g2="asterisk,registered,degree,trademark"
	k="12" />
    <hkern g1="zero,zero.zero"
	g2="seven,seven.ss08"
	k="16" />
    <hkern g1="zero,zero.zero"
	g2="slash"
	k="60" />
    <hkern g1="zero,zero.zero"
	g2="underscore"
	k="96" />
    <hkern g1="zero,zero.zero"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="-10" />
    <hkern g1="zero,zero.zero"
	g2="slash.case"
	k="10" />
    <hkern g1="zero,zero.zero"
	g2="three"
	k="5" />
    <hkern g1="eight"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="18" />
    <hkern g1="eight"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="9" />
    <hkern g1="eight"
	g2="asterisk,registered,degree,trademark"
	k="10" />
    <hkern g1="eight"
	g2="seven,seven.ss08"
	k="14" />
    <hkern g1="eight"
	g2="slash"
	k="30" />
    <hkern g1="eight"
	g2="underscore"
	k="51" />
    <hkern g1="eight"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="20" />
    <hkern g1="eight.osf"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="eight.osf"
	g2="asterisk,registered,degree,trademark"
	k="4" />
    <hkern g1="eight.osf"
	g2="slash"
	k="6" />
    <hkern g1="eight.osf"
	g2="underscore"
	k="34" />
    <hkern g1="eight.osf"
	g2="four.osf,four.osf.ss09"
	k="4" />
    <hkern g1="five.osf"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="17" />
    <hkern g1="five.osf"
	g2="asterisk,registered,degree,trademark"
	k="22" />
    <hkern g1="five.osf"
	g2="slash"
	k="-15" />
    <hkern g1="five.osf"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="36" />
    <hkern g1="five.osf"
	g2="seven.osf,seven.osf.ss08"
	k="25" />
    <hkern g1="four.osf"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-3" />
    <hkern g1="four.osf"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="36" />
    <hkern g1="four.osf"
	g2="asterisk,registered,degree,trademark"
	k="47" />
    <hkern g1="four.osf"
	g2="slash"
	k="-12" />
    <hkern g1="four.osf"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="31" />
    <hkern g1="four.osf"
	g2="four.osf,four.osf.ss09"
	k="-6" />
    <hkern g1="four.osf"
	g2="seven.osf,seven.osf.ss08"
	k="39" />
    <hkern g1="nine"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="44" />
    <hkern g1="nine"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="6" />
    <hkern g1="nine"
	g2="seven,seven.ss08"
	k="14" />
    <hkern g1="nine"
	g2="slash"
	k="50" />
    <hkern g1="nine"
	g2="underscore"
	k="80" />
    <hkern g1="nine.osf"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="36" />
    <hkern g1="nine.osf"
	g2="asterisk,registered,degree,trademark"
	k="44" />
    <hkern g1="nine.osf"
	g2="slash"
	k="-6" />
    <hkern g1="nine.osf"
	g2="underscore"
	k="4" />
    <hkern g1="nine.osf"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-6" />
    <hkern g1="nine.osf"
	g2="seven.osf,seven.osf.ss08"
	k="15" />
    <hkern g1="six"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="28" />
    <hkern g1="six"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="4" />
    <hkern g1="six"
	g2="asterisk,registered,degree,trademark"
	k="10" />
    <hkern g1="six"
	g2="seven,seven.ss08"
	k="8" />
    <hkern g1="six"
	g2="slash"
	k="40" />
    <hkern g1="six"
	g2="underscore"
	k="64" />
    <hkern g1="six.osf"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="19" />
    <hkern g1="six.osf"
	g2="slash"
	k="10" />
    <hkern g1="six.osf"
	g2="underscore"
	k="40" />
    <hkern g1="six.osf"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-6" />
    <hkern g1="six.osf"
	g2="four.osf,four.osf.ss09"
	k="15" />
    <hkern g1="three"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="21" />
    <hkern g1="three"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="4" />
    <hkern g1="three"
	g2="asterisk,registered,degree,trademark"
	k="6" />
    <hkern g1="three"
	g2="seven,seven.ss08"
	k="14" />
    <hkern g1="three"
	g2="slash"
	k="20" />
    <hkern g1="three"
	g2="underscore"
	k="40" />
    <hkern g1="three"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="25" />
    <hkern g1="three.osf"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="19" />
    <hkern g1="three.osf"
	g2="asterisk,registered,degree,trademark"
	k="28" />
    <hkern g1="three.osf"
	g2="slash"
	k="-21" />
    <hkern g1="three.osf"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="3" />
    <hkern g1="three.osf"
	g2="seven.osf,seven.osf.ss08"
	k="10" />
    <hkern g1="two"
	g2="seven,seven.ss08"
	k="8" />
    <hkern g1="two"
	g2="slash"
	k="10" />
    <hkern g1="two"
	g2="four,four.ss09"
	k="13" />
    <hkern g1="two"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="40" />
    <hkern g1="two.osf"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="14" />
    <hkern g1="two.osf"
	g2="asterisk,registered,degree,trademark"
	k="22" />
    <hkern g1="two.osf"
	g2="seven.osf,seven.osf.ss08"
	k="10" />
    <hkern g1="one.osf"
	g2="asterisk,registered,degree,trademark"
	k="4" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="four.osf,four.osf.ss09"
	k="38" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="28" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="seven,seven.ss08"
	k="54" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="bracketleft"
	k="-14" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="five.osf"
	k="14" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="nine.osf"
	k="-6" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="one"
	k="17" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="one.osf"
	k="-9" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="question"
	k="44" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="six.osf"
	k="-6" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="two"
	k="40" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="two.osf"
	k="-3" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="seven.osf,seven.osf.ss08"
	k="20" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="seven,seven.ss08"
	k="54" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="one"
	k="27" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="two"
	k="20" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="zero,zero.zero"
	k="-10" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="eight"
	k="20" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="three"
	k="60" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="five"
	k="8" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="four.osf,four.osf.ss09"
	k="30" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="four,four.ss09"
	k="30" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="parenright,bracketright,braceright"
	k="-20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="four.osf,four.osf.ss09"
	k="-6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="62" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven,seven.ss08"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="nine.osf"
	k="9" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one"
	k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="one.osf"
	k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="question"
	k="109" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="six.osf"
	k="38" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="seven.osf,seven.osf.ss08"
	k="42" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="zero,zero.zero"
	k="50" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="eight"
	k="18" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="three"
	k="28" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="five"
	k="34" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="four,four.ss09"
	k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="asterisk,registered,degree,trademark"
	k="96" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="eight.osf"
	k="7" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="nine"
	k="33" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="six"
	k="45" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four.osf,four.osf.ss09"
	k="59" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="seven,seven.ss08"
	k="-23" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five.osf"
	k="12" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="nine.osf"
	k="27" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="one"
	k="-15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="two.osf"
	k="14" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="seven.osf,seven.osf.ss08"
	k="-20" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="zero,zero.zero"
	k="8" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="eight"
	k="9" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three"
	k="10" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="five"
	k="6" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="four,four.ss09"
	k="70" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="six"
	k="9" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="28" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="62" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="questiondown"
	k="60" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="three.osf"
	k="17" />
    <hkern g1="asterisk,registered,degree"
	g2="four.osf,four.osf.ss09"
	k="96" />
    <hkern g1="asterisk,registered,degree"
	g2="seven,seven.ss08"
	k="-9" />
    <hkern g1="asterisk,registered,degree"
	g2="five.osf"
	k="18" />
    <hkern g1="asterisk,registered,degree"
	g2="nine.osf"
	k="34" />
    <hkern g1="asterisk,registered,degree"
	g2="one"
	k="-14" />
    <hkern g1="asterisk,registered,degree"
	g2="one.osf"
	k="2" />
    <hkern g1="asterisk,registered,degree"
	g2="two.osf"
	k="20" />
    <hkern g1="asterisk,registered,degree"
	g2="seven.osf,seven.osf.ss08"
	k="-14" />
    <hkern g1="asterisk,registered,degree"
	g2="zero,zero.zero"
	k="12" />
    <hkern g1="asterisk,registered,degree"
	g2="eight"
	k="10" />
    <hkern g1="asterisk,registered,degree"
	g2="three"
	k="4" />
    <hkern g1="asterisk,registered,degree"
	g2="five"
	k="7" />
    <hkern g1="asterisk,registered,degree"
	g2="four,four.ss09"
	k="69" />
    <hkern g1="asterisk,registered,degree"
	g2="eight.osf"
	k="4" />
    <hkern g1="asterisk,registered,degree"
	g2="six"
	k="10" />
    <hkern g1="asterisk,registered,degree"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="96" />
    <hkern g1="asterisk,registered,degree"
	g2="three.osf"
	k="26" />
    <hkern g1="slash"
	g2="four.osf,four.osf.ss09"
	k="70" />
    <hkern g1="slash"
	g2="seven,seven.ss08"
	k="-55" />
    <hkern g1="slash"
	g2="five.osf"
	k="4" />
    <hkern g1="slash"
	g2="nine.osf"
	k="14" />
    <hkern g1="slash"
	g2="one"
	k="-24" />
    <hkern g1="slash"
	g2="six.osf"
	k="-13" />
    <hkern g1="slash"
	g2="two.osf"
	k="6" />
    <hkern g1="slash"
	g2="seven.osf,seven.osf.ss08"
	k="-15" />
    <hkern g1="slash"
	g2="eight"
	k="20" />
    <hkern g1="slash"
	g2="four,four.ss09"
	k="20" />
    <hkern g1="slash"
	g2="eight.osf"
	k="-9" />
    <hkern g1="slash"
	g2="three.osf"
	k="10" />
    <hkern g1="slash"
	g2="slash"
	k="98" />
    <hkern g1="underscore"
	g2="seven,seven.ss08"
	k="29" />
    <hkern g1="underscore"
	g2="five.osf"
	k="8" />
    <hkern g1="underscore"
	g2="nine.osf"
	k="8" />
    <hkern g1="underscore"
	g2="one"
	k="44" />
    <hkern g1="underscore"
	g2="one.osf"
	k="59" />
    <hkern g1="underscore"
	g2="six.osf"
	k="57" />
    <hkern g1="underscore"
	g2="seven.osf,seven.osf.ss08"
	k="40" />
    <hkern g1="underscore"
	g2="zero,zero.zero"
	k="96" />
    <hkern g1="underscore"
	g2="eight"
	k="51" />
    <hkern g1="underscore"
	g2="three"
	k="40" />
    <hkern g1="underscore"
	g2="five"
	k="46" />
    <hkern g1="underscore"
	g2="four,four.ss09"
	k="50" />
    <hkern g1="underscore"
	g2="eight.osf"
	k="34" />
    <hkern g1="underscore"
	g2="nine"
	k="65" />
    <hkern g1="underscore"
	g2="six"
	k="87" />
    <hkern g1="backslash"
	g2="zero,zero.zero"
	k="40" />
    <hkern g1="colon"
	g2="seven,seven.ss08"
	k="22" />
    <hkern g1="colon"
	g2="four,four.ss09"
	k="10" />
    <hkern g1="question"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-6" />
    <hkern g1="question"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="44" />
    <hkern g1="question"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="109" />
    <hkern g1="questiondown"
	g2="four.osf,four.osf.ss09"
	k="8" />
    <hkern g1="questiondown"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="questiondown"
	g2="seven,seven.ss08"
	k="50" />
    <hkern g1="questiondown"
	g2="seven.osf,seven.osf.ss08"
	k="40" />
    <hkern g1="slash.case"
	g2="seven,seven.ss08"
	k="-15" />
    <hkern g1="slash.case"
	g2="zero,zero.zero"
	k="10" />
    <hkern g1="slash.case"
	g2="four,four.ss09"
	k="50" />
    <hkern g1="percent,perthousand"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="56" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="V"
	k="94" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="9" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="60" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="asterisk,registered,degree,trademark"
	k="96" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="seven.osf,seven.osf.ss08"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="24" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="underscore"
	k="-10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="v"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="x"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="z,zacute,zdotaccent,zcaron"
	k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="at"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="question"
	k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	g2="slash.case"
	k="-30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="21" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="V"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="21" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="21" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="asterisk,registered,degree,trademark"
	k="19" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="underscore"
	k="-4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="26" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="26" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="26" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="question"
	k="8" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="slash.case"
	k="-30" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="J.ss06,Jcircumflex.ss06"
	k="34" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="slash"
	k="4" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="23" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="53" />
    <hkern g1="B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="6" />
    <hkern g1="B"
	g2="V"
	k="43" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="64" />
    <hkern g1="B"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="15" />
    <hkern g1="B"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-12" />
    <hkern g1="B"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="20" />
    <hkern g1="B"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="11" />
    <hkern g1="B"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="24" />
    <hkern g1="B"
	g2="asterisk,registered,degree,trademark"
	k="41" />
    <hkern g1="B"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="15" />
    <hkern g1="B"
	g2="underscore"
	k="70" />
    <hkern g1="B"
	g2="v"
	k="21" />
    <hkern g1="B"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="21" />
    <hkern g1="B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="21" />
    <hkern g1="B"
	g2="z,zacute,zdotaccent,zcaron"
	k="15" />
    <hkern g1="B"
	g2="question"
	k="9" />
    <hkern g1="B"
	g2="slash.case"
	k="10" />
    <hkern g1="B"
	g2="slash"
	k="29" />
    <hkern g1="B"
	g2="J,Jcircumflex"
	k="32" />
    <hkern g1="B"
	g2="X"
	k="30" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="34" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="V"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="39" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="32" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-19" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="parenright,bracketright,braceright"
	k="-9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="31" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="asterisk,registered,degree,trademark"
	k="4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="underscore"
	k="65" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="x"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="z,zacute,zdotaccent,zcaron"
	k="12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="question"
	k="-9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="slash.case"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="slash"
	k="40" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="X"
	k="29" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron,uni2780,uni2781,uni2782,uni2783,uni2784,uni2785,uni2786,uni2787,uni2788,uni278A,uni278B,uni278C,uni278D,uni278E,uni278F,uni2790,uni2791,uni2792,u1F10B,u1F10C,u1F10C.zero,u1F10B.zero"
	g2="seven,seven.ss08"
	k="21" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="76" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="27" />
    <hkern g1="F"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="12" />
    <hkern g1="F"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="46" />
    <hkern g1="F"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="4" />
    <hkern g1="F"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="37" />
    <hkern g1="F"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="12" />
    <hkern g1="F"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="37" />
    <hkern g1="F"
	g2="parenright,bracketright,braceright"
	k="-12" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="66" />
    <hkern g1="F"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-9" />
    <hkern g1="F"
	g2="asterisk,registered,degree,trademark"
	k="-1" />
    <hkern g1="F"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="8" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="24" />
    <hkern g1="F"
	g2="underscore"
	k="152" />
    <hkern g1="F"
	g2="v"
	k="12" />
    <hkern g1="F"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="12" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="12" />
    <hkern g1="F"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="24" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="25" />
    <hkern g1="F"
	g2="question"
	k="4" />
    <hkern g1="F"
	g2="slash.case"
	k="50" />
    <hkern g1="F"
	g2="J.ss06,Jcircumflex.ss06"
	k="64" />
    <hkern g1="F"
	g2="slash"
	k="80" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="100" />
    <hkern g1="F"
	g2="four.osf,four.osf.ss09"
	k="120" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron,j.ss04,jcircumflex.ss04"
	k="30" />
    <hkern g1="F"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="54" />
    <hkern g1="F"
	g2="colon"
	k="12" />
    <hkern g1="F"
	g2="germandbls"
	k="30" />
    <hkern g1="F"
	g2="nine.osf"
	k="30" />
    <hkern g1="F"
	g2="three.osf"
	k="70" />
    <hkern g1="F"
	g2="two.osf"
	k="60" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="58" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="V"
	k="41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="36" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="66" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="asterisk,registered,degree,trademark"
	k="34" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="v"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
	g2="question"
	k="14" />
    <hkern g1="J,Jcircumflex"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="21" />
    <hkern g1="J,Jcircumflex"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="18" />
    <hkern g1="J,Jcircumflex"
	g2="J,Jcircumflex"
	k="31" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-10" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="42" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="35" />
    <hkern g1="K,Kcommaaccent"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="6" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="13" />
    <hkern g1="K,Kcommaaccent"
	g2="V"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="39" />
    <hkern g1="K,Kcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-6" />
    <hkern g1="K,Kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="6" />
    <hkern g1="K,Kcommaaccent"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="6" />
    <hkern g1="K,Kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="16" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="40" />
    <hkern g1="K,Kcommaaccent"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="54" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="18" />
    <hkern g1="K,Kcommaaccent"
	g2="parenright,bracketright,braceright"
	k="-6" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="9" />
    <hkern g1="K,Kcommaaccent"
	g2="asterisk,registered,degree,trademark"
	k="25" />
    <hkern g1="K,Kcommaaccent"
	g2="seven.osf,seven.osf.ss08"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="12" />
    <hkern g1="K,Kcommaaccent"
	g2="underscore"
	k="-10" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="19" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="19" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="19" />
    <hkern g1="K,Kcommaaccent"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="12" />
    <hkern g1="K,Kcommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="-11" />
    <hkern g1="K,Kcommaaccent"
	g2="at"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="question"
	k="26" />
    <hkern g1="K,Kcommaaccent"
	g2="J,Jcircumflex"
	k="-4" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="19" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="23" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="102" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="50" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="V"
	k="120" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="99" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="140" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-3" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="8" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="18" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="52" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="22" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="parenright,bracketright,braceright"
	k="24" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="72" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="asterisk,registered,degree,trademark"
	k="120" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="underscore"
	k="-14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="v"
	k="72" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="72" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="72" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="z,zacute,zdotaccent,zcaron"
	k="-12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="question"
	k="92" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="slash.case"
	k="-40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="J.ss06,Jcircumflex.ss06"
	k="24" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="13" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="colon"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="51" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="V"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="54" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="46" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="53" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="asterisk,registered,degree,trademark"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="underscore"
	k="101" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="x"
	k="18" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="z,zacute,zdotaccent,zcaron"
	k="9" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="slash.case"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="slash"
	k="50" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Q.ss07"
	g2="X"
	k="48" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="88" />
    <hkern g1="P"
	g2="V"
	k="6" />
    <hkern g1="P"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="27" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="21" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="30" />
    <hkern g1="P"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-12" />
    <hkern g1="P"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="24" />
    <hkern g1="P"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="28" />
    <hkern g1="P"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="24" />
    <hkern g1="P"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="92" />
    <hkern g1="P"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="P"
	g2="asterisk,registered,degree,trademark"
	k="-6" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-12" />
    <hkern g1="P"
	g2="underscore"
	k="180" />
    <hkern g1="P"
	g2="v"
	k="-6" />
    <hkern g1="P"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-6" />
    <hkern g1="P"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-6" />
    <hkern g1="P"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="P"
	g2="question"
	k="-9" />
    <hkern g1="P"
	g2="slash.case"
	k="70" />
    <hkern g1="P"
	g2="J.ss06,Jcircumflex.ss06"
	k="92" />
    <hkern g1="P"
	g2="slash"
	k="90" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="100" />
    <hkern g1="P"
	g2="X"
	k="46" />
    <hkern g1="P"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="9" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="37" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="V"
	k="17" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="34" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="24" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="32" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="24" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="asterisk,registered,degree,trademark"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="underscore"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="slash.case"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J.ss06,Jcircumflex.ss06"
	k="38" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="slash"
	k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="X"
	k="4" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="25" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="11" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="V"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="45" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="22" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="asterisk,registered,degree,trademark"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="underscore"
	k="46" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="v"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="slash.case"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="slash"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="J,Jcircumflex"
	k="24" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="X"
	k="19" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="51" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="35" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="V"
	k="-6" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="4" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-3" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="14" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="parenright,bracketright,braceright"
	k="-16" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="90" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-15" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="asterisk,registered,degree,trademark"
	k="2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="seven.osf,seven.osf.ss08"
	k="70" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="underscore"
	k="82" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="v"
	k="34" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="34" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="34" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="z,zacute,zdotaccent,zcaron"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="at"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="question"
	k="12" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash.case"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J.ss06,Jcircumflex.ss06"
	k="125" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="slash"
	k="82" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="102" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="four.osf,four.osf.ss09"
	k="120" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron,j.ss04,jcircumflex.ss04"
	k="48" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="97" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="colon"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="germandbls"
	k="24" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="nine.osf"
	k="100" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="three.osf"
	k="80" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,i.loclTRK,ij.ss03,ij.ss04"
	k="6" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="eight.osf"
	k="40" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="39" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="36" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="underscore"
	k="72" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="v"
	k="3" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="3" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="x"
	k="26" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="3" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="z,zacute,zdotaccent,zcaron"
	k="3" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="slash.case"
	k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="slash"
	k="41" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,J.ss06,Jcircumflex.ss06"
	g2="J,Jcircumflex"
	k="45" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="94" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="34" />
    <hkern g1="V"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="21" />
    <hkern g1="V"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-6" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="66" />
    <hkern g1="V"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="6" />
    <hkern g1="V"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="56" />
    <hkern g1="V"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="50" />
    <hkern g1="V"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="32" />
    <hkern g1="V"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="56" />
    <hkern g1="V"
	g2="parenright,bracketright,braceright"
	k="-15" />
    <hkern g1="V"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="66" />
    <hkern g1="V"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-10" />
    <hkern g1="V"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="10" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="20" />
    <hkern g1="V"
	g2="underscore"
	k="150" />
    <hkern g1="V"
	g2="v"
	k="29" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="29" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="29" />
    <hkern g1="V"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="20" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="46" />
    <hkern g1="V"
	g2="question"
	k="14" />
    <hkern g1="V"
	g2="slash.case"
	k="50" />
    <hkern g1="V"
	g2="J.ss06,Jcircumflex.ss06"
	k="89" />
    <hkern g1="V"
	g2="slash"
	k="56" />
    <hkern g1="V"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="V"
	g2="X"
	k="20" />
    <hkern g1="V"
	g2="four.osf,four.osf.ss09"
	k="130" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron,j.ss04,jcircumflex.ss04"
	k="29" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="50" />
    <hkern g1="V"
	g2="colon"
	k="26" />
    <hkern g1="V"
	g2="germandbls"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="82" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="21" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="53" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="46" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="46" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="parenright,bracketright,braceright"
	k="-12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="60" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="21" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="underscore"
	k="120" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="21" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="36" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="question"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash.case"
	k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J.ss06,Jcircumflex.ss06"
	k="79" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="50" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="X"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron,j.ss04,jcircumflex.ss04"
	k="24" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="46" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon"
	k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="germandbls"
	k="20" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="48" />
    <hkern g1="X"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="19" />
    <hkern g1="X"
	g2="V"
	k="20" />
    <hkern g1="X"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="X"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="26" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="19" />
    <hkern g1="X"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="6" />
    <hkern g1="X"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="20" />
    <hkern g1="X"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="50" />
    <hkern g1="X"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="50" />
    <hkern g1="X"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="26" />
    <hkern g1="X"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="X"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-4" />
    <hkern g1="X"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="9" />
    <hkern g1="X"
	g2="asterisk,registered,degree,trademark"
	k="30" />
    <hkern g1="X"
	g2="seven.osf,seven.osf.ss08"
	k="40" />
    <hkern g1="X"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="12" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="10" />
    <hkern g1="X"
	g2="v"
	k="12" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="12" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="12" />
    <hkern g1="X"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="10" />
    <hkern g1="X"
	g2="question"
	k="40" />
    <hkern g1="X"
	g2="J.ss06,Jcircumflex.ss06"
	k="29" />
    <hkern g1="X"
	g2="slash"
	k="14" />
    <hkern g1="X"
	g2="X"
	k="10" />
    <hkern g1="X"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="24" />
    <hkern g1="X"
	g2="germandbls"
	k="12" />
    <hkern g1="X"
	g2="three.osf"
	k="-20" />
    <hkern g1="X"
	g2="eight.osf"
	k="35" />
    <hkern g1="X"
	g2="eight"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="110" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="54" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="6" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="81" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="asterisk,registered,degree,trademark"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="19" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="underscore"
	k="148" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash.case"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J.ss06,Jcircumflex.ss06"
	k="116" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,Jcircumflex"
	k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron,j.ss04,jcircumflex.ss04"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon"
	k="32" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="germandbls"
	k="24" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="b,h,k,l,thorn,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash,f.calt"
	k="6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="46" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="21" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="48" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="48" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="asterisk,registered,degree,trademark"
	k="4" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="11" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="slash.case"
	k="-10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J.ss06,Jcircumflex.ss06"
	k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="slash"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="23" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon"
	k="-6" />
    <hkern g1="Thorn"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="52" />
    <hkern g1="Thorn"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="64" />
    <hkern g1="Thorn"
	g2="V"
	k="40" />
    <hkern g1="Thorn"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="71" />
    <hkern g1="Thorn"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="31" />
    <hkern g1="Thorn"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-6" />
    <hkern g1="Thorn"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-13" />
    <hkern g1="Thorn"
	g2="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotleft.case,guilsinglleft.case"
	k="-20" />
    <hkern g1="Thorn"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="-3" />
    <hkern g1="Thorn"
	g2="parenright,bracketright,braceright"
	k="24" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="64" />
    <hkern g1="Thorn"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="15" />
    <hkern g1="Thorn"
	g2="asterisk,registered,degree,trademark"
	k="20" />
    <hkern g1="Thorn"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-6" />
    <hkern g1="Thorn"
	g2="underscore"
	k="100" />
    <hkern g1="Thorn"
	g2="z,zacute,zdotaccent,zcaron"
	k="11" />
    <hkern g1="Thorn"
	g2="slash"
	k="41" />
    <hkern g1="Thorn"
	g2="J,Jcircumflex"
	k="88" />
    <hkern g1="Thorn"
	g2="X"
	k="56" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="3" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="24" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="asterisk,registered,degree,trademark"
	k="35" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="v"
	k="16" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="16" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde,amacron,abreve,aogonek,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	g2="question"
	k="29" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="27" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="v"
	k="14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="21" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="question"
	k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="9" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="slash"
	k="6" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="underscore"
	k="62" />
    <hkern g1="dcaron,lcaron"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="-27" />
    <hkern g1="dcaron,lcaron"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-75" />
    <hkern g1="dcaron,lcaron"
	g2="asterisk,registered,degree,trademark"
	k="-59" />
    <hkern g1="dcaron,lcaron"
	g2="v"
	k="-29" />
    <hkern g1="dcaron,lcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-29" />
    <hkern g1="dcaron,lcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-29" />
    <hkern g1="dcaron,lcaron"
	g2="question"
	k="-56" />
    <hkern g1="dcaron,lcaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="12" />
    <hkern g1="dcaron,lcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="12" />
    <hkern g1="dcaron,lcaron"
	g2="slash"
	k="31" />
    <hkern g1="dcaron,lcaron"
	g2="underscore"
	k="39" />
    <hkern g1="dcaron,lcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="15" />
    <hkern g1="dcaron,lcaron"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-24" />
    <hkern g1="dcaron,lcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="20" />
    <hkern g1="dcaron,lcaron"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,i.loclTRK,ij.ss03,ij.ss04"
	k="-59" />
    <hkern g1="dcaron,lcaron"
	g2="j,jcircumflex,uni0237,j.short,j.ss03,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03"
	k="-54" />
    <hkern g1="dcaron,lcaron"
	g2="b,h,k,l,thorn,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash,f.calt"
	k="-49" />
    <hkern g1="dcaron,lcaron"
	g2="parenright,bracketright,braceright"
	k="-94" />
    <hkern g1="dcaron,lcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="16" />
    <hkern g1="dcaron,lcaron"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-44" />
    <hkern g1="dcaron,lcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-10" />
    <hkern g1="dcaron,lcaron"
	g2="exclam"
	k="-40" />
    <hkern g1="dcaron,lcaron"
	g2="exclamdown"
	k="-50" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="24" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-27" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="asterisk,registered,degree,trademark"
	k="-20" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="v"
	k="-8" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-8" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-8" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="question"
	k="-23" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="26" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="21" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="61" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="slash"
	k="41" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="underscore"
	k="92" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="14" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-26" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="21" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="parenright,bracketright,braceright"
	k="-26" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-26" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="exclam"
	k="-11" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="exclamdown"
	k="-20" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="colon"
	k="-6" />
    <hkern g1="f,f.calt,f_f.liga"
	g2="one"
	k="-34" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,fi,i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03,f_f_i.liga"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-13" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,ij,jcircumflex,uni0237,ij.ss03,ij.ss04,j.short,j.ss03,j.ss04,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03,jcircumflex.ss04,y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	g2="slash"
	k="-19" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,ij,jcircumflex,uni0237,ij.ss03,ij.ss04,j.short,j.ss03,j.ss04,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03,jcircumflex.ss04,y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-3" />
    <hkern g1="g,j,q,gcircumflex,gbreve,gdotaccent,gcommaaccent,ij,jcircumflex,uni0237,ij.ss03,ij.ss04,j.short,j.ss03,j.ss04,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03,jcircumflex.ss04,y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-3" />
    <hkern g1="k,kcommaaccent"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="6" />
    <hkern g1="k,kcommaaccent"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="3" />
    <hkern g1="k,kcommaaccent"
	g2="asterisk,registered,degree,trademark"
	k="7" />
    <hkern g1="k,kcommaaccent"
	g2="v"
	k="16" />
    <hkern g1="k,kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="16" />
    <hkern g1="k,kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="16" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="48" />
    <hkern g1="k,kcommaaccent"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="32" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="16" />
    <hkern g1="k,kcommaaccent"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="24" />
    <hkern g1="k,kcommaaccent"
	g2="parenright,bracketright,braceright"
	k="17" />
    <hkern g1="k,kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="20" />
    <hkern g1="k,kcommaaccent"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="6" />
    <hkern g1="k,kcommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="k,kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="8" />
    <hkern g1="k,kcommaaccent"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="8" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="6" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="43" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="asterisk,registered,degree,trademark"
	k="44" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="v"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="15" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="question"
	k="32" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="-6" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="31" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="slash"
	k="8" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="underscore"
	k="74" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="4" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="3" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="4" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="z,zacute,zdotaccent,zcaron"
	k="26" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="four.osf,four.osf.ss09"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="seven.osf,seven.osf.ss08"
	k="10" />
    <hkern g1="b,e,o,p,ae,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,aeacute,zero.osf,zero.osf.zero"
	g2="x"
	k="21" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="-15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-28" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="asterisk,registered,degree,trademark"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="v"
	k="-6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="question"
	k="-10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="13" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="56" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="slash"
	k="36" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="underscore"
	k="79" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-17" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="24" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-17" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-5" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="x"
	k="6" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="3" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="22" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="asterisk,registered,degree,trademark"
	k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="v"
	k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="question"
	k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="7" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="slash"
	k="6" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="underscore"
	k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="z,zacute,zdotaccent,zcaron"
	k="17" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="x"
	k="24" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="17" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-17" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="asterisk,registered,degree,trademark"
	k="-3" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="question"
	k="-9" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="22" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="11" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-6" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="underscore"
	k="-6" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="6" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-9" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="parenright,bracketright,braceright"
	k="-6" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-16" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt,f_f_t.liga,f_t.liga,t_t.liga"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="17" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-17" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="asterisk,registered,degree,trademark"
	k="-3" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="v"
	k="-4" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-4" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-4" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="question"
	k="-9" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="22" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="15" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-6" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="slash"
	k="10" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="underscore"
	k="21" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="6" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-16" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="4" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="parenright,bracketright,braceright"
	k="-6" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-16" />
    <hkern g1="t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-3" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-3" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="-6" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-15" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="asterisk,registered,degree,trademark"
	k="-13" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="2" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="2" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="question"
	k="-16" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="20" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="20" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="42" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="27" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="underscore"
	k="89" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="21" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-12" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="18" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="11" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-14" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="7" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="four.osf,four.osf.ss09"
	k="70" />
    <hkern g1="v,y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="-6" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="-15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="asterisk,registered,degree,trademark"
	k="-13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="v"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="question"
	k="-16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="42" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="slash"
	k="27" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="underscore"
	k="89" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="21" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="11" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="z,zacute,zdotaccent,zcaron"
	k="7" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="four.osf,four.osf.ss09"
	k="70" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	g2="x"
	k="15" />
    <hkern g1="x"
	g2="asterisk,registered,degree,trademark"
	k="6" />
    <hkern g1="x"
	g2="v"
	k="15" />
    <hkern g1="x"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="15" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="x"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="46" />
    <hkern g1="x"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="27" />
    <hkern g1="x"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="6" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="14" />
    <hkern g1="x"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="19" />
    <hkern g1="x"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="x"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="24" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="3" />
    <hkern g1="x"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="v"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,guillemotleft,periodcentered,endash,emdash,guilsinglleft"
	k="32" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="23" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="-6" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="15" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-6" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="parenright,bracketright,braceright"
	k="4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="18" />
    <hkern g1="germandbls"
	g2="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	k="24" />
    <hkern g1="germandbls"
	g2="asterisk,registered,degree,trademark"
	k="30" />
    <hkern g1="germandbls"
	g2="v"
	k="19" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="19" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="19" />
    <hkern g1="germandbls"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="19" />
    <hkern g1="germandbls"
	g2="slash"
	k="9" />
    <hkern g1="germandbls"
	g2="underscore"
	k="62" />
    <hkern g1="germandbls"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="6" />
    <hkern g1="germandbls"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="germandbls"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="9" />
    <hkern g1="germandbls"
	g2="z,zacute,zdotaccent,zcaron"
	k="14" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="30" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="J,Jcircumflex"
	k="36" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="J.ss06,Jcircumflex.ss06"
	k="-12" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="-16" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-6" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="80" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="V"
	k="50" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="40" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="X"
	k="50" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="34" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-10" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="-6" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="14" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="v"
	k="20" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="20" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="x"
	k="46" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="z,zacute,zdotaccent,zcaron"
	k="36" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="12" />
    <hkern g1="hyphen,periodcentered,guillemotright,endash,emdash,guilsinglright"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="10" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="30" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="J,Jcircumflex"
	k="72" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="J.ss06,Jcircumflex.ss06"
	k="12" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="-10" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="18" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="50" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="V"
	k="32" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="27" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="X"
	k="50" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="periodcentered.case,emdash.case,endash.case,hyphen.case,guillemotright.case,guilsinglright.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="48" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="20" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="V"
	k="-15" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-12" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="X"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="v"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="x"
	k="4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="j,jcircumflex,uni0237,j.short,j.ss03,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03"
	k="-84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-17" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="53" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="28" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="60" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="X"
	k="-4" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="11" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="v"
	k="42" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="42" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="x"
	k="6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="z,zacute,zdotaccent,zcaron"
	k="-6" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="32" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="j,jcircumflex,uni0237,j.short,j.ss03,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03"
	k="-19" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="36" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="60" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J,Jcircumflex"
	k="80" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="J.ss06,Jcircumflex.ss06"
	k="120" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="V"
	k="-10" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="X"
	k="9" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-7" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="49" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="43" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="17" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="v"
	k="-15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-15" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-12" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-20" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="40" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,i.loclTRK,ij.ss03,ij.ss04"
	k="-3" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="i.ss03,dotlessi.ss03,iacute.ss03,ibreve.ss03,icircumflex.ss03,idieresis.ss03,i.loclTRK.ss03,igrave.ss03,imacron.ss01,iogonek.ss03,itilde.ss03"
	k="-6" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="b,h,k,l,thorn,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash,f.calt"
	k="-10" />
    <hkern g1="quotedbl,quotesingle,quoteleft,quoteright,quotedblleft,quotedblright"
	g2="germandbls"
	k="10" />
    <hkern g1="asterisk,registered,degree"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="96" />
    <hkern g1="asterisk,registered,degree"
	g2="J,Jcircumflex"
	k="96" />
    <hkern g1="asterisk,registered,degree"
	g2="J.ss06,Jcircumflex.ss06"
	k="124" />
    <hkern g1="asterisk,registered,degree"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="30" />
    <hkern g1="asterisk,registered,degree"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="20" />
    <hkern g1="asterisk,registered,degree"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="2" />
    <hkern g1="asterisk,registered,degree"
	g2="X"
	k="30" />
    <hkern g1="asterisk,registered,degree"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="asterisk,registered,degree"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="24" />
    <hkern g1="asterisk,registered,degree"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="50" />
    <hkern g1="asterisk,registered,degree"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="44" />
    <hkern g1="asterisk,registered,degree"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="36" />
    <hkern g1="asterisk,registered,degree"
	g2="v"
	k="-13" />
    <hkern g1="asterisk,registered,degree"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="-13" />
    <hkern g1="asterisk,registered,degree"
	g2="x"
	k="6" />
    <hkern g1="asterisk,registered,degree"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="-13" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="24" />
    <hkern g1="slash"
	g2="J,Jcircumflex"
	k="26" />
    <hkern g1="slash"
	g2="J.ss06,Jcircumflex.ss06"
	k="57" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="-6" />
    <hkern g1="slash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="-6" />
    <hkern g1="slash"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-36" />
    <hkern g1="slash"
	g2="V"
	k="-26" />
    <hkern g1="slash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-16" />
    <hkern g1="slash"
	g2="X"
	k="-12" />
    <hkern g1="slash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-20" />
    <hkern g1="slash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-9" />
    <hkern g1="slash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="8" />
    <hkern g1="slash"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="8" />
    <hkern g1="slash"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="13" />
    <hkern g1="slash"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="slash"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="-26" />
    <hkern g1="slash"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="-29" />
    <hkern g1="slash"
	g2="j,jcircumflex,uni0237,j.short,j.ss03,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03"
	k="-21" />
    <hkern g1="slash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="-26" />
    <hkern g1="slash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="11" />
    <hkern g1="slash"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,i.loclTRK,ij.ss03,ij.ss04"
	k="-21" />
    <hkern g1="slash"
	g2="b,h,k,l,thorn,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash,f.calt"
	k="-29" />
    <hkern g1="slash"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
	k="-40" />
    <hkern g1="underscore"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="-10" />
    <hkern g1="underscore"
	g2="J,Jcircumflex"
	k="-6" />
    <hkern g1="underscore"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="101" />
    <hkern g1="underscore"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="72" />
    <hkern g1="underscore"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="82" />
    <hkern g1="underscore"
	g2="V"
	k="150" />
    <hkern g1="underscore"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="120" />
    <hkern g1="underscore"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="148" />
    <hkern g1="underscore"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="17" />
    <hkern g1="underscore"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="74" />
    <hkern g1="underscore"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="65" />
    <hkern g1="underscore"
	g2="v"
	k="89" />
    <hkern g1="underscore"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="89" />
    <hkern g1="underscore"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="underscore"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="46" />
    <hkern g1="underscore"
	g2="t,uni0163,tcaron,uni021B,t_t.liga"
	k="74" />
    <hkern g1="underscore"
	g2="j,jcircumflex,uni0237,j.short,j.ss03,uni0237.short,uni0237.ss03,uni0237.ss04,jcircumflex.short,jcircumflex.ss03"
	k="-31" />
    <hkern g1="underscore"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="72" />
    <hkern g1="underscore"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,t.calt,tbar.calt,tcaron.calt,uni0163.calt,uni021B.calt"
	k="62" />
    <hkern g1="underscore"
	g2="y.ss01,yacute.ss01,ycircumflex.ss01,ydieresis.ss01,ygrave.ss01"
	k="37" />
    <hkern g1="underscore"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="42" />
    <hkern g1="backslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="40" />
    <hkern g1="backslash"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="10" />
    <hkern g1="backslash"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="25" />
    <hkern g1="colon"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="colon"
	g2="V"
	k="26" />
    <hkern g1="colon"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="colon"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="32" />
    <hkern g1="colon"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-6" />
    <hkern g1="question"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="60" />
    <hkern g1="questiondown"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="14" />
    <hkern g1="questiondown"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="8" />
    <hkern g1="questiondown"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="110" />
    <hkern g1="questiondown"
	g2="V"
	k="104" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="88" />
    <hkern g1="questiondown"
	g2="X"
	k="24" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="110" />
    <hkern g1="questiondown"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="questiondown"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent"
	k="-6" />
    <hkern g1="questiondown"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="-9" />
    <hkern g1="questiondown"
	g2="v"
	k="43" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis,w_w_w"
	k="43" />
    <hkern g1="questiondown"
	g2="x"
	k="26" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="43" />
    <hkern g1="questiondown"
	g2="z,zacute,zdotaccent,zcaron"
	k="14" />
    <hkern g1="questiondown"
	g2="f,tbar,fi,t.ss05,tbar.ss05,tcaron.ss05,uni0163.ss05,uni021B.ss05,f_f.liga,f_f_i.liga,f_f_t.liga,f_t.liga"
	k="16" />
    <hkern g1="questiondown"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,IJ,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="questiondown"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute,a.ss02,aacute.ss02,abreve.ss02,acircumflex.ss02,adieresis.ss02,agrave.ss02,amacron.ss02,aogonek.ss02,aring.ss02,atilde.ss02"
	k="-6" />
    <hkern g1="slash.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,AEacute"
	k="60" />
    <hkern g1="slash.case"
	g2="J,Jcircumflex"
	k="70" />
    <hkern g1="slash.case"
	g2="J.ss06,Jcircumflex.ss06"
	k="100" />
    <hkern g1="slash.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="30" />
    <hkern g1="slash.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="10" />
    <hkern g1="slash.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="slash.case"
	g2="V"
	k="-8" />
    <hkern g1="slash.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="slash.case"
	g2="X"
	k="20" />
    <hkern g1="slash.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="-10" />
    <hkern g1="slash.case"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="seven,seven.ss08"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Q.ss07"
	k="34" />
    <hkern g1="seven.osf,seven.osf.ss08"
	g2="J.ss06,Jcircumflex.ss06"
	k="52" />
    <hkern g1="eight"
	g2="X"
	k="30" />
    <hkern g1="eight.osf"
	g2="X"
	k="35" />
    <hkern g1="eight.osf"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="30" />
    <hkern g1="five.osf"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01,zero.osf,zero.osf.zero"
	k="6" />
    <hkern g1="six"
	g2="X"
	k="45" />
    <hkern g1="six.osf"
	g2="X"
	k="35" />
  </font>
</defs></svg>
