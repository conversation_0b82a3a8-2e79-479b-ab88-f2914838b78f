<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Event;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Event::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $eventDate = fake()->dateTimeBetween('now', '+6 months');

        return [
            'title' => fake()->sentence(3),
            'description' => fake()->paragraph(4),
            'short_description' => fake()->sentence(8),
            'event_date' => $eventDate,
            'location' => fake()->address(),
            'image' => fake()->imageUrl(800, 600, 'events'),
            'price' => fake()->randomFloat(2, 0, 200),
            'max_participants' => fake()->numberBetween(50, 500),
            'is_active' => fake()->boolean(90), // 90% chance of being active
            'status' => fake()->randomElement(['upcoming', 'ongoing', 'completed', 'cancelled']),
            'contact_info' => json_encode([
                'email' => fake()->email(),
                'phone' => fake()->phoneNumber(),
                'contact_person' => fake()->name()
            ]),
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the event is active and upcoming.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'status' => 'upcoming',
        ]);
    }

    /**
     * Indicate that the event is upcoming.
     */
    public function upcoming(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'upcoming',
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the event is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0.00,
        ]);
    }

    /**
     * Indicate that the event is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'event_date' => fake()->dateTimeBetween('-6 months', '-1 day'),
        ]);
    }
}