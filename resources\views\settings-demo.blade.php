@extends('layouts.app')

@section('title', 'Settings Demo - ' . setting('site_name', 'PESCOT'))
@section('description', 'Demonstration of dynamic settings integration in the frontend')

@section('content')
<div class="container mt-5 mb-5">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Settings System Demo</h2>
                    <p class="text-muted mb-0">This page demonstrates how settings from the database are integrated into the frontend</p>
                </div>
                <div class="card-body">
                    @if(!isset($settings) || count($settings) === 0)
                    <div class="alert alert-warning">
                        <h4 class="alert-heading">No settings found!</h4>
                        <p>Settings should be automatically initialized. If you're seeing this message, it means the automatic initialization failed.</p>
                        <hr>
                        <p class="mb-0">Click the button below to manually initialize settings:</p>
                        <button id="initialize-settings" class="btn btn-primary mt-3">Initialize Settings</button>
                    </div>
                    <script>
                        document.getElementById('initialize-settings').addEventListener('click', function() {
                            fetch('{{ route("settings.initialize") }}')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        alert('Settings initialized successfully. Refreshing page...');
                                        window.location.reload();
                                    } else {
                                        alert('Failed to initialize settings: ' + data.message);
                                    }
                                })
                                .catch(error => {
                                    alert('Error: ' + error);
                                });
                        });
                    </script>
                    @else
                    <!-- Site Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4>Site Information</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>Site Name:</strong></td>
                                    <td>{{ setting('site_name', 'Not Set') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Site Tagline:</strong></td>
                                    <td>{{ setting('site_tagline', 'Not Set') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Site Description:</strong></td>
                                    <td>{{ Str::limit(setting('site_description', 'Not Set'), 100) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Maintenance Mode:</strong></td>
                                    <td>
                                        <span class="badge {{ setting('site_maintenance_mode') ? 'bg-warning' : 'bg-success' }}">
                                            {{ setting('site_maintenance_mode') ? 'Enabled' : 'Disabled' }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h4>Theme Colors</h4>
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <div class="p-3 text-white text-center" style="background-color: {{ setting('theme_primary_color', '#2678a1db') }}">
                                        Primary Color<br>
                                        <small>{{ setting('theme_primary_color', '#2678a1db') }}</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="p-3 text-white text-center" style="background-color: {{ setting('theme_secondary_color', '#6c757d') }}">
                                        Secondary Color<br>
                                        <small>{{ setting('theme_secondary_color', '#6c757d') }}</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="p-3 text-white text-center" style="background-color: {{ setting('theme_accent_color', '#28a745') }}">
                                        Accent Color<br>
                                        <small>{{ setting('theme_accent_color', '#28a745') }}</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="p-3 text-center" style="background-color: {{ setting('theme_background_color', '#ffffff') }}; color: {{ setting('theme_text_color', '#333333') }}; border: 1px solid #dee2e6;">
                                        Text on Background<br>
                                        <small>{{ setting('theme_text_color', '#333333') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4>Social Media Links</h4>
                            <div class="list-group">
                                @if(setting('social_facebook'))
                                <a href="{{ setting('social_facebook') }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fa-brands fa-facebook-f me-2"></i> Facebook
                                </a>
                                @endif
                                @if(setting('social_twitter'))
                                <a href="{{ setting('social_twitter') }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fa-brands fa-twitter me-2"></i> Twitter
                                </a>
                                @endif
                                @if(setting('social_instagram'))
                                <a href="{{ setting('social_instagram') }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fa-brands fa-instagram me-2"></i> Instagram
                                </a>
                                @endif
                                @if(setting('social_linkedin'))
                                <a href="{{ setting('social_linkedin') }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fa-brands fa-linkedin me-2"></i> LinkedIn
                                </a>
                                @endif
                                @if(setting('social_youtube'))
                                <a href="{{ setting('social_youtube') }}" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="fa-brands fa-youtube me-2"></i> YouTube
                                </a>
                                @endif
                                @if(!setting('social_facebook') && !setting('social_twitter') && !setting('social_instagram') && !setting('social_linkedin') && !setting('social_youtube'))
                                <div class="list-group-item">
                                    <em class="text-muted">No social media links configured</em>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>SEO Settings</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>Keywords:</strong></td>
                                    <td>{{ setting('seo_keywords', 'Not Set') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Google Analytics:</strong></td>
                                    <td>{{ setting('seo_google_analytics') ? 'Configured' : 'Not Set' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Google Tag Manager:</strong></td>
                                    <td>{{ setting('seo_google_tag_manager') ? 'Configured' : 'Not Set' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Facebook Pixel:</strong></td>
                                    <td>{{ setting('seo_facebook_pixel') ? 'Configured' : 'Not Set' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- JavaScript Integration Demo -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h4>JavaScript Integration</h4>
                            <p class="text-muted">The following demonstrates how settings are accessible via JavaScript:</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Settings Object</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre id="settings-json" class="bg-light p-3 rounded" style="font-size: 12px; max-height: 200px; overflow-y: auto;"></pre>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Interactive Demo</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="setting-key" class="form-label">Setting Key:</label>
                                                <input type="text" class="form-control" id="setting-key" placeholder="e.g., site_name">
                                            </div>
                                            <button type="button" class="btn btn-primary" onclick="getSetting()">Get Setting Value</button>
                                            <div id="setting-result" class="mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Demo -->
                    <div class="row">
                        <div class="col-12">
                            <h4>Theme Integration Demo</h4>
                            <p class="text-muted">These elements use CSS variables that are dynamically set from database settings:</p>

                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-primary w-100">Primary Button</button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <button class="btn btn-secondary w-100">Secondary Button</button>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="p-3 bg-primary text-white text-center rounded">Primary Background</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="p-3 text-primary border border-primary text-center rounded">Primary Text</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Settings are cached and automatically updated when changed in the admin panel.</small>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshSettings()">Refresh Settings</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Display settings JSON
function displaySettingsJson() {
    const settingsElement = document.getElementById('settings-json');
    if (window.appSettings) {
        settingsElement.textContent = JSON.stringify(window.appSettings, null, 2);
    } else {
        settingsElement.textContent = 'Settings not loaded';
    }
}

// Get setting value demo
function getSetting() {
    const key = document.getElementById('setting-key').value;
    const resultElement = document.getElementById('setting-result');

    if (!key) {
        resultElement.innerHTML = '<div class="alert alert-warning">Please enter a setting key</div>';
        return;
    }

    const value = window.Settings ? window.Settings.get(key) : null;

    if (value !== null && value !== undefined) {
        resultElement.innerHTML = `
            <div class="alert alert-success">
                <strong>Key:</strong> ${key}<br>
                <strong>Value:</strong> ${typeof value === 'object' ? JSON.stringify(value) : value}<br>
                <strong>Type:</strong> ${typeof value}
            </div>
        `;
    } else {
        resultElement.innerHTML = `
            <div class="alert alert-info">
                Setting "${key}" not found or has no value.
            </div>
        `;
    }
}

// Refresh settings demo
function refreshSettings() {
    if (window.Settings && window.Settings.refresh) {
        const button = event.target;
        button.disabled = true;
        button.textContent = 'Refreshing...';

        window.Settings.refresh(function(error, settings) {
            button.disabled = false;
            button.textContent = 'Refresh Settings';

            if (error) {
                alert('Error refreshing settings: ' + error.message);
            } else {
                alert('Settings refreshed successfully!');
                displaySettingsJson();
            }
        });
    } else {
        alert('Settings refresh function not available');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    displaySettingsJson();

    // Add enter key support for setting key input
    document.getElementById('setting-key').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            getSetting();
        }
    });
});
</script>
@endpush
