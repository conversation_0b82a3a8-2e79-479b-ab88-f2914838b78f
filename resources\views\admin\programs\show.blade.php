@extends('admin.layouts.app')

@section('title', 'Program Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Program Details: {{ $program->title }}</h3>
                    <div>
                        <a href="{{ route('admin.programs.edit', $program) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.programs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Programs
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-lg-8">
                            <!-- Basic Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Title:</strong></div>
                                        <div class="col-sm-9">{{ $program->title }}</div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Slug:</strong></div>
                                        <div class="col-sm-9">
                                            <code>{{ $program->slug }}</code>
                                            <a href="{{ route('programs.show', $program->slug) }}" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                                <i class="fas fa-external-link-alt"></i> View Public
                                            </a>
                                        </div>
                                    </div>

                                    @if($program->short_description)
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Short Description:</strong></div>
                                        <div class="col-sm-9">{{ $program->short_description }}</div>
                                    </div>
                                    @endif

                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Description:</strong></div>
                                        <div class="col-sm-9">
                                            <div class="border p-3 rounded bg-light">
                                                {!! nl2br(e($program->description)) !!}
                                            </div>
                                        </div>
                                    </div>

                                    @if($program->objectives)
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Learning Objectives:</strong></div>
                                        <div class="col-sm-9">
                                            <div class="border p-3 rounded bg-light">
                                                {!! nl2br(e($program->objectives)) !!}
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    @if($program->requirements)
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Requirements:</strong></div>
                                        <div class="col-sm-9">
                                            <div class="border p-3 rounded bg-light">
                                                {!! nl2br(e($program->requirements)) !!}
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Schedule & Logistics -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Schedule & Logistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            @if($program->start_date)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Start Date:</strong></div>
                                                <div class="col-sm-6">{{ $program->start_date->format('M d, Y') }}</div>
                                            </div>
                                            @endif

                                            @if($program->duration)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Duration:</strong></div>
                                                <div class="col-sm-6">{{ $program->duration }}</div>
                                            </div>
                                            @endif

                                            @if($program->schedule_time)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Schedule Time:</strong></div>
                                                <div class="col-sm-6">{{ $program->schedule_time }}</div>
                                            </div>
                                            @endif

                                            @if($program->location)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Location:</strong></div>
                                                <div class="col-sm-6">{{ $program->location }}</div>
                                            </div>
                                            @endif
                                        </div>
                                        
                                        <div class="col-md-6">
                                            @if($program->end_date)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>End Date:</strong></div>
                                                <div class="col-sm-6">{{ $program->end_date->format('M d, Y') }}</div>
                                            </div>
                                            @endif

                                            @if($program->schedule_days && count($program->schedule_days) > 0)
                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Schedule Days:</strong></div>
                                                <div class="col-sm-6">
                                                    @foreach($program->schedule_days as $day)
                                                        <span class="badge bg-secondary me-1">{{ $day }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                            @endif

                                            <div class="row mb-3">
                                                <div class="col-sm-6"><strong>Delivery Mode:</strong></div>
                                                <div class="col-sm-6">
                                                    @if($program->is_online)
                                                        <span class="badge bg-info"><i class="fas fa-laptop"></i> Online</span>
                                                    @else
                                                        <span class="badge bg-success"><i class="fas fa-map-marker-alt"></i> In-Person</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Event Linkage -->
                            @if($program->event)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Linked Event</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ $program->event->title }}</strong><br>
                                                <small class="text-muted">{{ $program->event->formatted_date }}</small>
                                            </div>
                                            <a href="{{ route('admin.events.show', $program->event) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> View Event
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- SEO Information -->
                            @if($program->meta_title || $program->meta_description)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Information</h5>
                                </div>
                                <div class="card-body">
                                    @if($program->meta_title)
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Meta Title:</strong></div>
                                        <div class="col-sm-9">{{ $program->meta_title }}</div>
                                    </div>
                                    @endif

                                    @if($program->meta_description)
                                    <div class="row mb-3">
                                        <div class="col-sm-3"><strong>Meta Description:</strong></div>
                                        <div class="col-sm-9">{{ $program->meta_description }}</div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Sidebar -->
                        <div class="col-lg-4">
                            <!-- Featured Image -->
                            @if($program->image)
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Featured Image</h5>
                                </div>
                                <div class="card-body text-center">
                                    <img src="{{ $program->image_url }}" alt="{{ $program->title }}" class="img-fluid rounded">
                                </div>
                            </div>
                            @endif

                            <!-- Program Details -->
                            <div class="card {{ $program->image ? 'mt-4' : '' }}">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Program Details</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Status:</strong></div>
                                        <div class="col-6">
                                            @php
                                                $statusColors = [
                                                    'upcoming' => 'primary',
                                                    'ongoing' => 'success',
                                                    'completed' => 'secondary',
                                                    'cancelled' => 'danger'
                                                ];
                                            @endphp
                                            <span class="badge bg-{{ $statusColors[$program->status] ?? 'secondary' }}">
                                                {{ ucfirst($program->status) }}
                                            </span>
                                        </div>
                                    </div>

                                    @if($program->level)
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Level:</strong></div>
                                        <div class="col-6">
                                            <span class="badge bg-info">{{ ucfirst($program->level) }}</span>
                                        </div>
                                    </div>
                                    @endif

                                    @if($program->instructor)
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Instructor:</strong></div>
                                        <div class="col-6">{{ $program->instructor }}</div>
                                    </div>
                                    @endif

                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Price:</strong></div>
                                        <div class="col-6">
                                            @if($program->price > 0)
                                                <span class="text-success fw-bold">${{ number_format($program->price, 2) }}</span>
                                            @else
                                                <span class="badge bg-success">Free</span>
                                            @endif
                                        </div>
                                    </div>

                                    @if($program->max_participants)
                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Max Participants:</strong></div>
                                        <div class="col-6">{{ $program->max_participants }}</div>
                                    </div>
                                    @endif

                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Active:</strong></div>
                                        <div class="col-6">
                                            @if($program->is_active)
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                            @else
                                                <span class="badge bg-danger"><i class="fas fa-times"></i> No</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-6"><strong>Sort Order:</strong></div>
                                        <div class="col-6">{{ $program->sort_order }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Information -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">System Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Created:</strong></div>
                                        <div class="col-6">
                                            <small>{{ $program->created_at->format('M d, Y H:i') }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-6"><strong>Updated:</strong></div>
                                        <div class="col-6">
                                            <small>{{ $program->updated_at->format('M d, Y H:i') }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-6"><strong>ID:</strong></div>
                                        <div class="col-6">
                                            <code>{{ $program->id }}</code>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.programs.edit', $program) }}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> Edit Program
                                        </a>
                                        
                                        <form action="{{ route('admin.programs.duplicate', $program) }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-info w-100">
                                                <i class="fas fa-copy"></i> Duplicate Program
                                            </button>
                                        </form>
                                        
                                        <form action="{{ route('admin.programs.toggle-status', $program) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-{{ $program->is_active ? 'secondary' : 'success' }} w-100">
                                                @if($program->is_active)
                                                    <i class="fas fa-pause"></i> Deactivate
                                                @else
                                                    <i class="fas fa-play"></i> Activate
                                                @endif
                                            </button>
                                        </form>
                                        
                                        <hr>
                                        
                                        <form action="{{ route('admin.programs.destroy', $program) }}" method="POST" 
                                              onsubmit="return confirm('Are you sure you want to delete this program? This action cannot be undone.')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger w-100">
                                                <i class="fas fa-trash"></i> Delete Program
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Add any specific JavaScript for the show page here
});
</script>
@endpush