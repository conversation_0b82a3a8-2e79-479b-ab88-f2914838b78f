<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Partner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PartnerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Partner::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('website', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } else {
                $query->where('status', $request->status);
            }
        }

        // Filter by featured
        if ($request->filled('featured')) {
            if ($request->featured === '1') {
                $query->featured();
            } else {
                $query->where('featured', false);
            }
        }

        $partners = $query->ordered()->paginate(15);

        return view('admin.partners.index', compact('partners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.partners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('partners', 'public');
        }

        // Set default sort order if not provided
        if (empty($validated['sort_order'])) {
            $validated['sort_order'] = Partner::max('sort_order') + 1;
        }

        Partner::create($validated);

        return redirect()->route('admin.partners.index')
                        ->with('success', 'Partner created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Partner $partner)
    {
        return view('admin.partners.show', compact('partner'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Partner $partner)
    {
        return view('admin.partners.edit', compact('partner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Partner $partner)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($partner->logo && Storage::disk('public')->exists($partner->logo)) {
                Storage::disk('public')->delete($partner->logo);
            }
            $validated['logo'] = $request->file('logo')->store('partners', 'public');
        }

        $partner->update($validated);

        return redirect()->route('admin.partners.index')
                        ->with('success', 'Partner updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Partner $partner)
    {
        // Delete logo if exists
        if ($partner->logo && Storage::disk('public')->exists($partner->logo)) {
            Storage::disk('public')->delete($partner->logo);
        }

        $partner->delete();

        return redirect()->route('admin.partners.index')
                        ->with('success', 'Partner deleted successfully.');
    }

    /**
     * Toggle partner status
     */
    public function toggleStatus(Partner $partner)
    {
        $partner->update([
            'status' => $partner->status === 'active' ? 'inactive' : 'active'
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Partner status updated successfully.',
            'status' => $partner->status
        ]);
    }

    /**
     * Toggle partner featured status
     */
    public function toggleFeatured(Partner $partner)
    {
        $partner->update([
            'featured' => !$partner->featured
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Partner featured status updated successfully.',
            'featured' => $partner->featured
        ]);
    }

    /**
     * Update partner sort order
     */
    public function updateSortOrder(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:partners,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($validated['items'] as $item) {
            Partner::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Partner order updated successfully.'
        ]);
    }

    /**
     * Bulk action for partners
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete',
            'partner_ids' => 'required|array',
            'partner_ids.*' => 'exists:partners,id'
        ]);

        $partners = Partner::whereIn('id', $validated['partner_ids']);
        
        switch ($validated['action']) {
            case 'activate':
                $partners->update(['status' => 'active']);
                $message = 'Partners activated successfully.';
                break;
            case 'deactivate':
                $partners->update(['status' => 'inactive']);
                $message = 'Partners deactivated successfully.';
                break;
            case 'feature':
                $partners->update(['featured' => true]);
                $message = 'Partners marked as featured successfully.';
                break;
            case 'unfeature':
                $partners->update(['featured' => false]);
                $message = 'Partners unmarked as featured successfully.';
                break;
            case 'delete':
                // Delete logos before deleting partners
                $partnerModels = $partners->get();
                foreach ($partnerModels as $partner) {
                    if ($partner->logo && Storage::disk('public')->exists($partner->logo)) {
                        Storage::disk('public')->delete($partner->logo);
                    }
                }
                $partners->delete();
                $message = 'Partners deleted successfully.';
                break;
        }

        return redirect()->route('admin.partners.index')
                        ->with('success', $message);
    }

    /**
     * Duplicate partner
     */
    public function duplicate(Partner $partner)
    {
        $newPartner = $partner->replicate();
        $newPartner->name = $partner->name . ' (Copy)';
        $newPartner->status = 'inactive';
        $newPartner->featured = false;
        $newPartner->sort_order = Partner::max('sort_order') + 1;
        
        // Copy logo if exists
        if ($partner->logo && Storage::disk('public')->exists($partner->logo)) {
            $extension = pathinfo($partner->logo, PATHINFO_EXTENSION);
            $newLogoPath = 'partners/' . uniqid() . '.' . $extension;
            Storage::disk('public')->copy($partner->logo, $newLogoPath);
            $newPartner->logo = $newLogoPath;
        }
        
        $newPartner->save();

        return redirect()->route('admin.partners.edit', $newPartner)
                        ->with('success', 'Partner duplicated successfully.');
    }
}
