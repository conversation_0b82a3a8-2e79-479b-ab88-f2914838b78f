<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SettingsService;
use App\Models\Setting;

class InitializeSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:initialize-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize default settings if they do not exist';

    /**
     * The settings service instance.
     *
     * @var \App\Services\SettingsService
     */
    protected $settingsService;

    /**
     * Create a new command instance.
     */
    public function __construct(SettingsService $settingsService)
    {
        parent::__construct();
        $this->settingsService = $settingsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing default settings...');

        try {
            // Check if any settings exist
            $settingsCount = Setting::count();
            
            if ($settingsCount > 0) {
                if (!$this->confirm('Settings already exist. Do you want to reinitialize default settings? This will not overwrite existing settings.')) {
                    $this->info('Operation cancelled.');
                    return 0;
                }
            }

            // Initialize default settings
            $this->settingsService->initializeDefaults();
            
            $this->info('Default settings have been initialized successfully!');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to initialize settings: ' . $e->getMessage());
            return 1;
        }
    }
}
