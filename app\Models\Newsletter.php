<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Newsletter extends Model
{
    protected $fillable = [
        'email',
        'name',
        'status',
        'verification_token',
        'verified_at',
        'unsubscribed_at',
        'ip_address',
        'source'
    ];

    protected $casts = [
        'verified_at' => 'datetime',
        'unsubscribed_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($newsletter) {
            if (empty($newsletter->verification_token)) {
                $newsletter->verification_token = Str::random(64);
            }
        });
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->whereNotNull('verified_at')
                    ->whereNull('unsubscribed_at');
    }

    public function scopeVerified($query)
    {
        return $query->whereNotNull('verified_at');
    }

    public function scopeUnverified($query)
    {
        return $query->whereNull('verified_at');
    }

    public function scopeUnsubscribed($query)
    {
        return $query->whereNotNull('unsubscribed_at');
    }

    // Accessors
    public function getIsVerifiedAttribute()
    {
        return !is_null($this->verified_at);
    }

    public function getIsUnsubscribedAttribute()
    {
        return !is_null($this->unsubscribed_at);
    }

    // Methods
    public function verify()
    {
        $this->update([
            'verified_at' => now(),
            'status' => 'active'
        ]);
    }

    public function unsubscribe()
    {
        $this->update([
            'unsubscribed_at' => now(),
            'status' => 'unsubscribed'
        ]);
    }

    /**
     * Get status badge class for display
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'active' => 'badge-success',
            'inactive' => 'badge-secondary',
            'unsubscribed' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    /**
     * Get status label for display
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'unsubscribed' => 'Unsubscribed',
            default => 'Unknown'
        };
    }

    /**
     * Get email verified at attribute for compatibility
     */
    public function getEmailVerifiedAtAttribute()
    {
        return $this->verified_at;
    }
}
