<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\SettingsService;
use App\Models\Setting;

class SettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Initialize settings if they don't exist
     */
    public function initialize()
    {
        try {
            // Check if any settings exist
            $settingsCount = Setting::count();
            
            if ($settingsCount === 0) {
                // Initialize default settings
                $this->settingsService->initializeDefaults();
                return response()->json([
                    'success' => true,
                    'message' => 'Settings initialized successfully.'
                ]);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Settings already exist.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all public settings
     */
    public function getPublicSettings()
    {
        try {
            $settings = $this->settingsService->getPublicSettings();
            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get settings: ' . $e->getMessage()
            ], 500);
        }
    }
}