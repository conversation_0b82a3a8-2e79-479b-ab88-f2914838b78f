<?php $__env->startSection('title', 'Programs Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Programs Management</h3>
                    <a href="<?php echo e(route('admin.programs.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Program
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="<?php echo e(route('admin.programs.index')); ?>" class="row g-3">
                                <div class="col-md-3">
                                    <input type="text" name="search" class="form-control" placeholder="Search programs..." value="<?php echo e(request('search')); ?>">
                                </div>
                                <div class="col-md-2">
                                    <select name="status" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="upcoming" <?php echo e(request('status') === 'upcoming' ? 'selected' : ''); ?>>Upcoming</option>
                                        <option value="ongoing" <?php echo e(request('status') === 'ongoing' ? 'selected' : ''); ?>>Ongoing</option>
                                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                        <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="level" class="form-select">
                                        <option value="">All Levels</option>
                                        <option value="beginner" <?php echo e(request('level') === 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                                        <option value="intermediate" <?php echo e(request('level') === 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                                        <option value="advanced" <?php echo e(request('level') === 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="is_active" class="form-select">
                                        <option value="">All</option>
                                        <option value="1" <?php echo e(request('is_active') === '1' ? 'selected' : ''); ?>>Active</option>
                                        <option value="0" <?php echo e(request('is_active') === '0' ? 'selected' : ''); ?>>Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select name="is_online" class="form-select">
                                        <option value="">Online/Offline</option>
                                        <option value="1" <?php echo e(request('is_online') === '1' ? 'selected' : ''); ?>>Online</option>
                                        <option value="0" <?php echo e(request('is_online') === '0' ? 'selected' : ''); ?>>Offline</option>
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form id="bulk-action-form" method="POST" action="<?php echo e(route('admin.programs.bulk-action')); ?>">
                                <?php echo csrf_field(); ?>
                                <div class="d-flex align-items-center gap-2">
                                    <select name="action" class="form-select" style="width: auto;">
                                        <option value="">Bulk Actions</option>
                                        <option value="activate">Activate</option>
                                        <option value="deactivate">Deactivate</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-secondary" onclick="return confirm('Are you sure you want to perform this action?')">
                                        Apply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Programs Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Instructor</th>
                                    <th>Level</th>
                                    <th>Status</th>
                                    <th>Price</th>
                                    <th>Participants</th>
                                    <th>Event</th>
                                    <th>Active</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="programs[]" value="<?php echo e($program->id); ?>" class="program-checkbox" form="bulk-action-form">
                                    </td>
                                    <td>
                                        <?php if($program->image): ?>
                                            <img src="<?php echo e(Storage::url($program->image)); ?>" alt="<?php echo e($program->title); ?>" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($program->title); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo e(Str::limit($program->short_description, 50)); ?></small>
                                        </div>
                                    </td>
                                    <td><?php echo e($program->instructor ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if($program->level): ?>
                                            <span class="badge bg-info"><?php echo e(ucfirst($program->level)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($program->status_badge_class); ?>">
                                            <?php echo e(ucfirst($program->status)); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($program->formatted_price); ?></td>
                                    <td><?php echo e($program->max_participants ?? 'Unlimited'); ?></td>
                                    <td>
                                        <?php if($program->event): ?>
                                            <a href="<?php echo e(route('admin.events.show', $program->event)); ?>" class="text-decoration-none">
                                                <i class="fas fa-calendar-alt"></i> <?php echo e(Str::limit($program->event->title, 20)); ?>

                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">No Event</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   data-id="<?php echo e($program->id); ?>" 
                                                   <?php echo e($program->is_active ? 'checked' : ''); ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.programs.show', $program)); ?>" class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.programs.edit', $program)); ?>" class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.programs.duplicate', $program)); ?>" class="btn btn-sm btn-outline-secondary" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </a>
                                            <form action="<?php echo e(route('admin.programs.destroy', $program)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this program?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                            <p>No programs found.</p>
                                            <a href="<?php echo e(route('admin.programs.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Create First Program
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($programs->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($programs->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.program-checkbox').prop('checked', this.checked);
    });

    // Individual checkbox change
    $('.program-checkbox').change(function() {
        if (!this.checked) {
            $('#select-all').prop('checked', false);
        } else if ($('.program-checkbox:checked').length === $('.program-checkbox').length) {
            $('#select-all').prop('checked', true);
        }
    });

    // Status toggle functionality
    $('.status-toggle').change(function() {
        const programId = $(this).data('id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: `/admin/programs/${programId}/toggle-status`,
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Failed to update status');
                    // Revert the toggle
                    $(this).prop('checked', !isActive);
                }
            },
            error: function() {
                toastr.error('Failed to update status');
                // Revert the toggle
                $(this).prop('checked', !isActive);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/programs/index.blade.php ENDPATH**/ ?>