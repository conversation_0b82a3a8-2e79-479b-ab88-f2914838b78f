<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsletterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Newsletter::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('email', 'like', '%' . $request->search . '%')
                  ->orWhere('name', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active();
                    break;
                case 'verified':
                    $query->verified();
                    break;
                case 'unverified':
                    $query->unverified();
                    break;
                case 'unsubscribed':
                    $query->unsubscribed();
                    break;
            }
        }

        // Filter by source
        if ($request->filled('source')) {
            $query->where('source', $request->source);
        }

        $newsletters = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.newsletters.index', compact('newsletters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.newsletters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:newsletters,email',
            'name' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'source' => 'nullable|string|max:255',
            'ip_address' => 'nullable|ip'
        ]);

        // Set IP if not provided
        if (empty($validated['ip_address'])) {
            $validated['ip_address'] = $request->ip();
        }

        // Auto-verify if created by admin
        $validated['verified_at'] = now();
        $validated['verification_token'] = Str::random(60);

        Newsletter::create($validated);

        return redirect()->route('admin.newsletters.index')
                        ->with('success', 'Newsletter subscription created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Newsletter $newsletter)
    {
        return view('admin.newsletters.show', compact('newsletter'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Newsletter $newsletter)
    {
        return view('admin.newsletters.edit', compact('newsletter'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Newsletter $newsletter)
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:newsletters,email,' . $newsletter->id,
            'name' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive',
            'source' => 'nullable|string|max:255'
        ]);

        $newsletter->update($validated);

        return redirect()->route('admin.newsletters.index')
                        ->with('success', 'Newsletter subscription updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Newsletter $newsletter)
    {
        $newsletter->delete();

        return redirect()->route('admin.newsletters.index')
                        ->with('success', 'Newsletter subscription deleted successfully.');
    }

    /**
     * Verify newsletter subscription
     */
    public function verify(Newsletter $newsletter)
    {
        $newsletter->verify();
        
        return response()->json([
            'success' => true,
            'message' => 'Newsletter subscription verified successfully.',
            'is_verified' => true
        ]);
    }

    /**
     * Unsubscribe newsletter
     */
    public function unsubscribe(Newsletter $newsletter)
    {
        $newsletter->unsubscribe();
        
        return response()->json([
            'success' => true,
            'message' => 'Newsletter subscription cancelled successfully.',
            'is_unsubscribed' => true
        ]);
    }

    /**
     * Resubscribe newsletter
     */
    public function resubscribe(Newsletter $newsletter)
    {
        $newsletter->update([
            'status' => 'active',
            'unsubscribed_at' => null
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Newsletter subscription reactivated successfully.',
            'is_unsubscribed' => false
        ]);
    }

    /**
     * Toggle newsletter status
     */
    public function toggleStatus(Newsletter $newsletter)
    {
        $newsletter->update([
            'status' => $newsletter->status === 'active' ? 'inactive' : 'active'
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Newsletter status updated successfully.',
            'status' => $newsletter->status
        ]);
    }

    /**
     * Bulk action for newsletters
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:verify,unsubscribe,resubscribe,activate,deactivate,delete',
            'newsletter_ids' => 'required|array',
            'newsletter_ids.*' => 'exists:newsletters,id'
        ]);

        $newsletters = Newsletter::whereIn('id', $validated['newsletter_ids']);
        
        switch ($validated['action']) {
            case 'verify':
                $newsletters->update([
                    'verified_at' => now(),
                    'verification_token' => null
                ]);
                $message = 'Newsletter subscriptions verified successfully.';
                break;
            case 'unsubscribe':
                $newsletters->update([
                    'status' => 'inactive',
                    'unsubscribed_at' => now()
                ]);
                $message = 'Newsletter subscriptions cancelled successfully.';
                break;
            case 'resubscribe':
                $newsletters->update([
                    'status' => 'active',
                    'unsubscribed_at' => null
                ]);
                $message = 'Newsletter subscriptions reactivated successfully.';
                break;
            case 'activate':
                $newsletters->update(['status' => 'active']);
                $message = 'Newsletter subscriptions activated successfully.';
                break;
            case 'deactivate':
                $newsletters->update(['status' => 'inactive']);
                $message = 'Newsletter subscriptions deactivated successfully.';
                break;
            case 'delete':
                $newsletters->delete();
                $message = 'Newsletter subscriptions deleted successfully.';
                break;
        }

        return redirect()->route('admin.newsletters.index')
                        ->with('success', $message);
    }

    /**
     * Export newsletter subscribers
     */
    public function export(Request $request)
    {
        $query = Newsletter::query();

        // Apply same filters as index
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active();
                    break;
                case 'verified':
                    $query->verified();
                    break;
                case 'unverified':
                    $query->unverified();
                    break;
                case 'unsubscribed':
                    $query->unsubscribed();
                    break;
            }
        }

        $newsletters = $query->get();

        $filename = 'newsletter_subscribers_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($newsletters) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Email', 'Name', 'Status', 'Verified', 'Source', 'Subscribed At']);

            foreach ($newsletters as $newsletter) {
                fputcsv($file, [
                    $newsletter->email,
                    $newsletter->name,
                    $newsletter->status,
                    $newsletter->is_verified ? 'Yes' : 'No',
                    $newsletter->source,
                    $newsletter->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
