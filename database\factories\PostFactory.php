<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Post;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Post::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->sentence(4),
            'slug' => fake()->unique()->slug(),
            'excerpt' => fake()->paragraph(2),
            'content' => fake()->paragraphs(8, true),
            'featured_image' => fake()->imageUrl(1200, 800, 'blog'),
            'gallery' => json_encode([]), // Empty gallery by default
            'category_id' => fake()->numberBetween(1, 5), // Assuming categories 1-5 exist
            'admin_id' => fake()->numberBetween(1, 3), // Assuming admins 1-3 exist
            'status' => fake()->randomElement(['draft', 'published', 'archived']),
            'featured' => fake()->boolean(15), // 15% chance of being featured
            'comments_enabled' => fake()->boolean(90), // 90% chance comments enabled
            'meta_title' => fake()->sentence(5),
            'meta_description' => fake()->sentence(12),
            'meta_keywords' => implode(',', fake()->words(fake()->numberBetween(3, 8))),
            'views' => fake()->numberBetween(0, 10000),
            'published_at' => fake()->optional(0.8)->dateTimeBetween('-2 years', 'now'),
            'created_at' => fake()->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the blog post is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => fake()->dateTimeBetween('-1 year', 'now'),
        ]);
    }

    /**
     * Indicate that the blog post is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
        ]);
    }

    /**
     * Indicate that the blog post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }

    /**
     * Create a blog post with high engagement.
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'views' => fake()->numberBetween(5000, 50000),
            'featured' => true,
        ]);
    }
}