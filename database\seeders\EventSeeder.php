<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Event;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 15 upcoming events
        Event::factory(15)->upcoming()->create();

        // Create 5 active upcoming events
        Event::factory(5)->active()->create();

        // Create 3 free upcoming events
        Event::factory(3)->free()->upcoming()->create();

        // Create 2 completed events
        Event::factory(2)->completed()->create();

        // Create 3 cancelled events
        Event::factory(3)->state([
            'status' => 'cancelled',
            'is_active' => false
        ])->create();
    }
}