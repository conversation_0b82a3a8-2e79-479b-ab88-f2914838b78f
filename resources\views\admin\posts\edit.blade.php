@extends('layouts.admin')

@section('title', 'Edit Post')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Post: {{ $post->title }}</h3>
                    <div>
                        <a href="{{ route('admin.posts.show', $post) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.posts.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Posts
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.posts.update', $post) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title -->
                                <div class="form-group">
                                    <label for="title">Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title', $post->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Excerpt -->
                                <div class="form-group">
                                    <label for="excerpt">Excerpt</label>
                                    <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                              id="excerpt" name="excerpt" rows="3" placeholder="Brief description of the post...">{{ old('excerpt', $post->excerpt) }}</textarea>
                                    @error('excerpt')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Optional. If left empty, will be auto-generated from content.</small>
                                </div>

                                <!-- Content -->
                                <div class="form-group">
                                    <label for="content">Content <span class="text-danger">*</span></label>
                                    <textarea class="form-control @error('content') is-invalid @enderror" 
                                              id="content" name="content" rows="15" required>{{ old('content', $post->content) }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- SEO Section -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">SEO Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Meta Title -->
                                        <div class="form-group">
                                            <label for="meta_title">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title', $post->meta_title) }}">
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">If empty, post title will be used.</small>
                                        </div>

                                        <!-- Meta Description -->
                                        <div class="form-group">
                                            <label for="meta_description">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                      id="meta_description" name="meta_description" rows="3">{{ old('meta_description', $post->meta_description) }}</textarea>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">If empty, excerpt will be used.</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publishing Options -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Publishing Options</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Status -->
                                        <div class="form-group">
                                            <label for="status">Status <span class="text-danger">*</span></label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                                <option value="draft" {{ old('status', $post->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="pending" {{ old('status', $post->status) == 'pending' ? 'selected' : '' }}>Pending Review</option>
                                                <option value="published" {{ old('status', $post->status) == 'published' ? 'selected' : '' }}>Published</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Published At -->
                                        <div class="form-group">
                                            <label for="published_at">Publish Date</label>
                                            <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror" 
                                                   id="published_at" name="published_at" 
                                                   value="{{ old('published_at', $post->published_at ? $post->published_at->format('Y-m-d\TH:i') : '') }}">
                                            @error('published_at')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Leave empty to publish immediately when status is 'Published'.</small>
                                        </div>

                                        <!-- Featured -->
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="featured" name="featured" value="1" 
                                                       {{ old('featured', $post->featured) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="featured">
                                                    Featured Post
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Featured posts appear prominently on the site.</small>
                                        </div>

                                        <!-- Comments Enabled -->
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="comments_enabled" name="comments_enabled" value="1" 
                                                       {{ old('comments_enabled', $post->comments_enabled) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="comments_enabled">
                                                    Enable Comments
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category & Tags -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Category & Tags</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Category -->
                                        <div class="form-group">
                                            <label for="category_id">Category</label>
                                            <select class="form-control @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                                <option value="">Select Category</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" 
                                                            {{ old('category_id', $post->category_id) == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Tags -->
                                        <div class="form-group">
                                            <label for="tags">Tags</label>
                                            <select class="form-control @error('tags') is-invalid @enderror" id="tags" name="tags[]" multiple>
                                                @foreach($tags as $tag)
                                                    <option value="{{ $tag->id }}" 
                                                            {{ in_array($tag->id, old('tags', $post->tags->pluck('id')->toArray())) ? 'selected' : '' }}>
                                                        {{ $tag->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('tags')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple tags.</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Featured Image</h5>
                                    </div>
                                    <div class="card-body">
                                        @if($post->featured_image)
                                        <div class="current-featured-image mb-3">
                                            <label>Current Featured Image</label>
                                            <div>
                                                <img src="{{ $post->featuredImageUrl }}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                <div class="mt-2">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" id="remove_featured_image" name="remove_featured_image" value="1">
                                                        <label class="form-check-label" for="remove_featured_image">
                                                            Remove current image
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                        
                                        <div class="form-group">
                                            <label for="featured_image">{{ $post->featured_image ? 'Replace Featured Image' : 'Featured Image' }}</label>
                                            <input type="file" class="form-control-file @error('featured_image') is-invalid @enderror" 
                                                   id="featured_image" name="featured_image" accept="image/*">
                                            @error('featured_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Supported formats: JPEG, PNG, JPG, GIF, SVG. Max size: 5MB</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gallery Images -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Gallery Images</h5>
                                    </div>
                                    <div class="card-body">
                                        @if($post->gallery_images && count($post->gallery_images) > 0)
                                        <div class="current-gallery mb-3">
                                            <label>Current Gallery Images</label>
                                            <div class="row">
                                                @foreach($post->gallery_images as $index => $image)
                                                <div class="col-4 mb-2">
                                                    <img src="{{ $post->getGalleryImageUrl($image) }}" class="img-thumbnail" style="width: 100%; height: 80px; object-fit: cover;">
                                                    <div class="form-check mt-1">
                                                        <input type="checkbox" class="form-check-input" name="remove_gallery_images[]" value="{{ $index }}">
                                                        <label class="form-check-label small">Remove</label>
                                                    </div>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        @endif
                                        
                                        <div class="form-group">
                                            <label for="gallery_images">{{ $post->gallery_images && count($post->gallery_images) > 0 ? 'Add More Images' : 'Gallery Images' }}</label>
                                            <input type="file" class="form-control-file @error('gallery_images') is-invalid @enderror" 
                                                   id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                            @error('gallery_images')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Select multiple images for post gallery.</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Post Info -->
                                <div class="card mt-3">
                                    <div class="card-body">
                                        <h6 class="card-title">Post Information</h6>
                                        <p class="card-text small">
                                            <strong>Slug:</strong> {{ $post->slug }}<br>
                                            <strong>Author:</strong> {{ $post->author->name ?? 'Unknown' }}<br>
                                            <strong>Views:</strong> {{ $post->views ?? 0 }}<br>
                                            <strong>Comments:</strong> {{ $post->comments()->count() }}<br>
                                            <strong>Created:</strong> {{ $post->created_at->format('M d, Y H:i') }}<br>
                                            <strong>Updated:</strong> {{ $post->updated_at->format('M d, Y H:i') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Post
                            </button>
                            <button type="submit" name="save_and_continue" value="1" class="btn btn-success">
                                <i class="fas fa-save"></i> Save & Continue Editing
                            </button>
                            <a href="{{ route('admin.posts.index') }}" class="btn btn-secondary ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
.select2-container--default .select2-selection--multiple {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.ckeditor.com/ckeditor5/35.3.2/classic/ckeditor.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for tags
    $('#tags').select2({
        placeholder: 'Select tags...',
        allowClear: true
    });

    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#content'), {
            toolbar: {
                items: [
                    'heading', '|',
                    'bold', 'italic', 'link', '|',
                    'bulletedList', 'numberedList', '|',
                    'outdent', 'indent', '|',
                    'imageUpload', 'blockQuote', 'insertTable', '|',
                    'undo', 'redo'
                ]
            },
            language: 'en',
            image: {
                toolbar: [
                    'imageTextAlternative',
                    'imageStyle:full',
                    'imageStyle:side'
                ]
            },
            table: {
                contentToolbar: [
                    'tableColumn',
                    'tableRow',
                    'mergeTableCells'
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });

    // Image preview for featured image
    $('#featured_image').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var preview = '<div class="mt-2"><img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px; max-height: 200px;"></div>';
                $('#featured_image').parent().find('.preview').remove();
                $('#featured_image').parent().append('<div class="preview">' + preview + '</div>');
            };
            reader.readAsDataURL(file);
        }
    });

    // Gallery images preview
    $('#gallery_images').change(function() {
        var files = this.files;
        var previewContainer = $('#gallery_images').parent().find('.gallery-preview');
        
        if (previewContainer.length === 0) {
            $('#gallery_images').parent().append('<div class="gallery-preview mt-2"></div>');
            previewContainer = $('#gallery_images').parent().find('.gallery-preview');
        }
        
        previewContainer.empty();
        
        if (files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                let file = files[i];
                let reader = new FileReader();
                reader.onload = function(e) {
                    previewContainer.append('<img src="' + e.target.result + '" class="img-thumbnail mr-2 mb-2" style="width: 80px; height: 80px; object-fit: cover;">');
                };
                reader.readAsDataURL(file);
            }
        }
    });

    // Remove featured image checkbox handler
    $('#remove_featured_image').change(function() {
        if ($(this).is(':checked')) {
            $('.current-featured-image img').addClass('opacity-50');
        } else {
            $('.current-featured-image img').removeClass('opacity-50');
        }
    });

    // Status change handler
    $('#status').change(function() {
        var status = $(this).val();
        if (status === 'published' && $('#published_at').val() === '') {
            // Set current datetime if publishing
            var now = new Date();
            var datetime = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(now.getDate()).padStart(2, '0') + 'T' + 
                          String(now.getHours()).padStart(2, '0') + ':' + 
                          String(now.getMinutes()).padStart(2, '0');
            $('#published_at').val(datetime);
        }
    });
});
</script>
@endpush