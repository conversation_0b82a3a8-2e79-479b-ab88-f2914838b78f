<?php

use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\ProgramController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\PostController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\CommentController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\Admin\FileController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\PartnerController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\NewsletterController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "admin" middleware group.
|
*/

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest routes (not authenticated)
    Route::middleware('guest:admin')->group(function () {
        Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [AuthController::class, 'login']);
    });

    // Authenticated admin routes
    Route::middleware('auth:admin')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
        Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');

        // Admin Management
        Route::resource('admins', AdminController::class);
        Route::post('admins/{admin}/toggle-status', [AdminController::class, 'toggleStatus'])->name('admins.toggle-status');
        Route::post('admins/bulk-action', [AdminController::class, 'bulkAction'])->name('admins.bulk-action');

        // Programs Management
        Route::resource('programs', ProgramController::class);
        Route::patch('programs/{program}/toggle-status', [ProgramController::class, 'toggleStatus'])->name('programs.toggle-status');
        Route::post('programs/{program}/duplicate', [ProgramController::class, 'duplicate'])->name('programs.duplicate');
        Route::post('programs/bulk-action', [ProgramController::class, 'bulkAction'])->name('programs.bulk-action');

        // Content Management routes
        Route::resource('posts', PostController::class);
        Route::post('posts/{post}/toggle-status', [PostController::class, 'toggleStatus'])->name('posts.toggle-status');
        Route::post('posts/bulk-action', [PostController::class, 'bulkAction'])->name('posts.bulk-action');

        Route::resource('categories', CategoryController::class);
        Route::post('categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
        Route::post('categories/bulk-action', [CategoryController::class, 'bulkAction'])->name('categories.bulk-action');

        Route::resource('comments', CommentController::class)->except(['create', 'store', 'edit', 'update']);
        Route::post('comments/{comment}/approve', [CommentController::class, 'approve'])->name('comments.approve');
        Route::post('comments/{comment}/reject', [CommentController::class, 'reject'])->name('comments.reject');
        Route::post('comments/{comment}/spam', [CommentController::class, 'markAsSpam'])->name('comments.spam');
        Route::post('comments/{comment}/reply', [CommentController::class, 'reply'])->name('comments.reply');
        Route::post('comments/bulk-action', [CommentController::class, 'bulkAction'])->name('comments.bulk-action');
        Route::get('comments/stats', [CommentController::class, 'getStats'])->name('comments.stats');

        // Business Management routes
        Route::resource('products', ProductController::class);
        Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');
        Route::post('products/{product}/toggle-featured', [ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');
        Route::post('products/bulk-action', [ProductController::class, 'bulkAction'])->name('products.bulk-action');

        Route::resource('services', ServiceController::class);
        Route::post('services/{service}/toggle-status', [ServiceController::class, 'toggleStatus'])->name('services.toggle-status');
        Route::post('services/{service}/toggle-featured', [ServiceController::class, 'toggleFeatured'])->name('services.toggle-featured');
        Route::post('services/bulk-action', [ServiceController::class, 'bulkAction'])->name('services.bulk-action');

        // Media Management routes
        Route::resource('gallery', GalleryController::class);
        Route::post('gallery/{gallery}/toggle-featured', [GalleryController::class, 'toggleFeatured'])->name('gallery.toggle-featured');
        Route::post('gallery/bulk-upload', [GalleryController::class, 'bulkUpload'])->name('gallery.bulk-upload');
        Route::post('gallery/bulk-action', [GalleryController::class, 'bulkAction'])->name('gallery.bulk-action');
        Route::get('gallery/categories', [GalleryController::class, 'getCategories'])->name('gallery.categories');

        // File Manager routes
        Route::prefix('files')->name('files.')->group(function () {
            Route::get('/', [FileController::class, 'index'])->name('index');
            Route::post('/upload', [FileController::class, 'upload'])->name('upload');
            Route::post('/create-directory', [FileController::class, 'createDirectory'])->name('create-directory');
            Route::post('/rename', [FileController::class, 'rename'])->name('rename');
            Route::delete('/delete', [FileController::class, 'delete'])->name('delete');
            Route::get('/download', [FileController::class, 'download'])->name('download');
            Route::get('/info', [FileController::class, 'getFileInfo'])->name('info');
            Route::post('/generate-link', [FileController::class, 'generatePublicLink'])->name('generate-link');
        });

        // Website Management routes
        Route::resource('events', EventController::class);
        Route::resource('faqs', FaqController::class);
        Route::resource('testimonials', TestimonialController::class);
        Route::resource('partners', PartnerController::class);
        Route::resource('contacts', ContactController::class)->except(['create', 'store']);
        Route::resource('newsletters', NewsletterController::class)->except(['create', 'store', 'edit', 'update']);

        // Settings Management
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::post('/update', [SettingsController::class, 'update'])->name('update');
            Route::post('/initialize-defaults', [SettingsController::class, 'initializeDefaults'])->name('initialize-defaults');
            Route::post('/clear-cache', [SettingsController::class, 'clearCache'])->name('clear-cache');
            Route::get('/export', [SettingsController::class, 'export'])->name('export');
            Route::post('/import', [SettingsController::class, 'import'])->name('import');
            Route::get('/api', [SettingsController::class, 'getSettings'])->name('api');
            Route::post('/api/update', [SettingsController::class, 'updateSetting'])->name('api.update');
        });

        // Legacy Settings Routes (for backward compatibility)
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/general', [SettingsController::class, 'index'])->name('general');
            Route::get('/seo', [SettingsController::class, 'index'])->name('seo');
            Route::get('/contact', [SettingsController::class, 'index'])->name('contact');
            Route::get('/social', [SettingsController::class, 'index'])->name('social');
            Route::get('/smtp', [SettingsController::class, 'index'])->name('smtp');
            Route::get('/recaptcha', [SettingsController::class, 'index'])->name('recaptcha');
            Route::get('/newsletter', [SettingsController::class, 'index'])->name('newsletter');
        });
    });
});