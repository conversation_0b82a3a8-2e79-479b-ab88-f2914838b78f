@extends('layouts.admin')

@section('title', 'Edit Contact')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Contact</h3>
                    <div>
                        <a href="{{ route('admin.contacts.show', $contact) }}" class="btn btn-info mr-2">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Contacts
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.contacts.update', $contact) }}" method="POST" id="contactForm">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <!-- Contact Information -->
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Contact Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name">Full Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                           id="name" name="name" value="{{ old('name', $contact->name) }}" required>
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                           id="email" name="email" value="{{ old('email', $contact->email) }}" required>
                                                    @error('email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="phone">Phone Number</label>
                                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                           id="phone" name="phone" value="{{ old('phone', $contact->phone) }}">
                                                    @error('phone')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="company">Company</label>
                                                    <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                                           id="company" name="company" value="{{ old('company', $contact->company) }}">
                                                    @error('company')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="subject">Subject <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                                   id="subject" name="subject" value="{{ old('subject', $contact->subject) }}" required>
                                            @error('subject')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="message">Message <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                                      id="message" name="message" rows="6" required>{{ old('message', $contact->message) }}</textarea>
                                            <small class="form-text text-muted">
                                                Character count: <span id="messageCount">{{ strlen($contact->message) }}</span>
                                            </small>
                                            @error('message')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Admin Notes -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h4 class="card-title">Admin Notes</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="admin_notes">Internal Notes</label>
                                            <textarea class="form-control @error('admin_notes') is-invalid @enderror" 
                                                      id="admin_notes" name="admin_notes" rows="4" 
                                                      placeholder="Add internal notes about this contact...">{{ old('admin_notes', $contact->admin_notes) }}</textarea>
                                            <small class="form-text text-muted">
                                                These notes are only visible to administrators.
                                            </small>
                                            @error('admin_notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Settings & Info -->
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">Contact Settings</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="new" {{ old('status', $contact->status) === 'new' ? 'selected' : '' }}>New</option>
                                                <option value="in_progress" {{ old('status', $contact->status) === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                                <option value="resolved" {{ old('status', $contact->status) === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                                <option value="closed" {{ old('status', $contact->status) === 'closed' ? 'selected' : '' }}>Closed</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="is_read" name="is_read" value="1" {{ old('is_read', $contact->is_read) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_read">Mark as Read</label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="priority">Priority</label>
                                            <select class="form-control @error('priority') is-invalid @enderror" id="priority" name="priority">
                                                <option value="low" {{ old('priority', $contact->priority) === 'low' ? 'selected' : '' }}>Low</option>
                                                <option value="medium" {{ old('priority', $contact->priority) === 'medium' ? 'selected' : '' }}>Medium</option>
                                                <option value="high" {{ old('priority', $contact->priority) === 'high' ? 'selected' : '' }}>High</option>
                                                <option value="urgent" {{ old('priority', $contact->priority) === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                            </select>
                                            @error('priority')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="ip_address">IP Address</label>
                                            <input type="text" class="form-control @error('ip_address') is-invalid @enderror" 
                                                   id="ip_address" name="ip_address" value="{{ old('ip_address', $contact->ip_address) }}">
                                            @error('ip_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="user_agent">User Agent</label>
                                            <textarea class="form-control @error('user_agent') is-invalid @enderror" 
                                                      id="user_agent" name="user_agent" rows="3">{{ old('user_agent', $contact->user_agent) }}</textarea>
                                            @error('user_agent')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h4 class="card-title">Contact Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="info-item">
                                            <strong>Created:</strong>
                                            <span>{{ $contact->created_at->format('M d, Y H:i') }}</span>
                                        </div>
                                        <div class="info-item">
                                            <strong>Updated:</strong>
                                            <span>{{ $contact->updated_at->format('M d, Y H:i') }}</span>
                                        </div>
                                        @if($contact->replied_at)
                                        <div class="info-item">
                                            <strong>Replied:</strong>
                                            <span>{{ $contact->replied_at->format('M d, Y H:i') }}</span>
                                        </div>
                                        @endif
                                        <div class="info-item">
                                            <strong>ID:</strong>
                                            <span>#{{ $contact->id }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h4 class="card-title">Quick Actions</h4>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group-vertical w-100">
                                            @if(!$contact->is_read)
                                                <button type="button" class="btn btn-success btn-sm mb-2" onclick="markAsRead({{ $contact->id }})">
                                                    <i class="fas fa-envelope-open"></i> Mark as Read
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-secondary btn-sm mb-2" onclick="markAsUnread({{ $contact->id }})">
                                                    <i class="fas fa-envelope"></i> Mark as Unread
                                                </button>
                                            @endif
                                            <button type="button" class="btn btn-info btn-sm mb-2" onclick="changeStatus('{{ $contact->id }}', 'in_progress')">
                                                <i class="fas fa-clock"></i> Set In Progress
                                            </button>
                                            <button type="button" class="btn btn-success btn-sm mb-2" onclick="changeStatus('{{ $contact->id }}', 'resolved')">
                                                <i class="fas fa-check"></i> Mark Resolved
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteContact({{ $contact->id }})">
                                                <i class="fas fa-trash"></i> Delete Contact
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Preview -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h4 class="card-title">Preview</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="contactPreview">
                                            <div class="preview-item">
                                                <strong>Name:</strong> <span id="previewName">{{ $contact->name }}</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Email:</strong> <span id="previewEmail">{{ $contact->email }}</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Subject:</strong> <span id="previewSubject">{{ $contact->subject }}</span>
                                            </div>
                                            <div class="preview-item">
                                                <strong>Message:</strong> <span id="previewMessage">{{ Str::limit($contact->message, 100) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Contact
                                </button>
                                <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.contacts.show', $contact) }}" class="btn btn-info mr-2">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{{ route('admin.contacts.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this contact?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.preview-item, .info-item {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

.preview-item:last-child, .info-item:last-child {
    border-bottom: none;
}

.preview-item strong, .info-item strong {
    color: #495057;
}

.preview-item span, .info-item span {
    color: #6c757d;
    word-wrap: break-word;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
}

.btn-group-vertical .btn {
    border-radius: 0.25rem !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Character counting
    $('#message').on('input', function() {
        const count = $(this).val().length;
        $('#messageCount').text(count);
    });

    // Real-time preview
    function updatePreview() {
        $('#previewName').text($('#name').val() || '-');
        $('#previewEmail').text($('#email').val() || '-');
        $('#previewSubject').text($('#subject').val() || '-');
        const message = $('#message').val();
        $('#previewMessage').text(message ? (message.length > 100 ? message.substring(0, 100) + '...' : message) : '-');
    }

    // Update preview on input
    $('#name, #email, #subject, #message').on('input', updatePreview);

    // Form validation
    $('#contactForm').on('submit', function(e) {
        let isValid = true;
        const requiredFields = ['name', 'email', 'subject', 'message'];
        
        requiredFields.forEach(function(field) {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid');
            }
        });
        
        // Email validation
        const email = $('#email').val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
        }
    });

    // Remove validation errors on input
    $('.form-control').on('input', function() {
        $(this).removeClass('is-invalid');
    });
});

// Reset form function
function resetForm() {
    if (confirm('Are you sure you want to reset the form? All changes will be lost.')) {
        location.reload();
    }
}

// Mark as read
function markAsRead(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-read`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as read');
            }
        },
        error: function() {
            alert('Error marking contact as read');
        }
    });
}

// Mark as unread
function markAsUnread(contactId) {
    $.ajax({
        url: `/admin/contacts/${contactId}/mark-unread`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error marking contact as unread');
            }
        },
        error: function() {
            alert('Error marking contact as unread');
        }
    });
}

// Change status
function changeStatus(contactId, status) {
    $.ajax({
        url: `/admin/contacts/${contactId}/status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            status: status
        },
        success: function(response) {
            if (response.success) {
                $('#status').val(status);
                alert('Status updated successfully');
            } else {
                alert('Error updating contact status');
            }
        },
        error: function() {
            alert('Error updating contact status');
        }
    });
}

// Delete contact
function deleteContact(contactId) {
    $('#deleteForm').attr('action', `/admin/contacts/${contactId}`);
    $('#deleteModal').modal('show');
}
</script>
@endpush