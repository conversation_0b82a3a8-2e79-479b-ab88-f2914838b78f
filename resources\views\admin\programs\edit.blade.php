@extends('admin.layouts.app')

@section('title', 'Edit Program')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Program: {{ $program->title }}</h3>
                    <div>
                        <a href="{{ route('admin.programs.show', $program) }}" class="btn btn-info me-2">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.programs.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Programs
                        </a>
                    </div>
                </div>
                
                <form action="{{ route('admin.programs.update', $program) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $program->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="slug" class="form-label">Slug</label>
                                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                   id="slug" name="slug" value="{{ old('slug', $program->slug) }}">
                                            <div class="form-text">Leave empty to auto-generate from title</div>
                                            @error('slug')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="short_description" class="form-label">Short Description</label>
                                            <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                                      id="short_description" name="short_description" rows="3">{{ old('short_description', $program->short_description) }}</textarea>
                                            @error('short_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="8" required>{{ old('description', $program->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="objectives" class="form-label">Learning Objectives</label>
                                            <textarea class="form-control @error('objectives') is-invalid @enderror" 
                                                      id="objectives" name="objectives" rows="4">{{ old('objectives', $program->objectives) }}</textarea>
                                            @error('objectives')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="requirements" class="form-label">Requirements</label>
                                            <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                                      id="requirements" name="requirements" rows="3">{{ old('requirements', $program->requirements) }}</textarea>
                                            @error('requirements')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Schedule Information -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Schedule & Logistics</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="start_date" class="form-label">Start Date</label>
                                                    <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                                           id="start_date" name="start_date" value="{{ old('start_date', $program->start_date?->format('Y-m-d')) }}">
                                                    @error('start_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="end_date" class="form-label">End Date</label>
                                                    <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                                           id="end_date" name="end_date" value="{{ old('end_date', $program->end_date?->format('Y-m-d')) }}">
                                                    @error('end_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="duration" class="form-label">Duration</label>
                                                    <input type="text" class="form-control @error('duration') is-invalid @enderror" 
                                                           id="duration" name="duration" value="{{ old('duration', $program->duration) }}" 
                                                           placeholder="e.g., 8 weeks, 3 months">
                                                    @error('duration')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="schedule_time" class="form-label">Schedule Time</label>
                                                    <input type="time" class="form-control @error('schedule_time') is-invalid @enderror" 
                                                           id="schedule_time" name="schedule_time" value="{{ old('schedule_time', $program->schedule_time) }}">
                                                    @error('schedule_time')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Schedule Days</label>
                                            <div class="row">
                                                @php
                                                    $scheduleDays = old('schedule_days', $program->schedule_days ?? []);
                                                @endphp
                                                @foreach(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as $day)
                                                <div class="col-md-3 col-sm-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="schedule_days[]" 
                                                               value="{{ $day }}" id="day_{{ strtolower($day) }}"
                                                               {{ in_array($day, $scheduleDays) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="day_{{ strtolower($day) }}">
                                                            {{ $day }}
                                                        </label>
                                                    </div>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="location" class="form-label">Location</label>
                                                    <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                                           id="location" name="location" value="{{ old('location', $program->location) }}">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <div class="form-check mt-4">
                                                        <input class="form-check-input" type="checkbox" name="is_online" 
                                                               value="1" id="is_online" {{ old('is_online', $program->is_online) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="is_online">
                                                            Online Program
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="col-lg-4">
                                <!-- Program Settings -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Program Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                                <option value="upcoming" {{ old('status', $program->status) === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                                                <option value="ongoing" {{ old('status', $program->status) === 'ongoing' ? 'selected' : '' }}>Ongoing</option>
                                                <option value="completed" {{ old('status', $program->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                                                <option value="cancelled" {{ old('status', $program->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="level" class="form-label">Level</label>
                                            <select class="form-select @error('level') is-invalid @enderror" id="level" name="level">
                                                <option value="">Select Level</option>
                                                <option value="beginner" {{ old('level', $program->level) === 'beginner' ? 'selected' : '' }}>Beginner</option>
                                                <option value="intermediate" {{ old('level', $program->level) === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                                <option value="advanced" {{ old('level', $program->level) === 'advanced' ? 'selected' : '' }}>Advanced</option>
                                            </select>
                                            @error('level')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="instructor" class="form-label">Instructor</label>
                                            <input type="text" class="form-control @error('instructor') is-invalid @enderror" 
                                                   id="instructor" name="instructor" value="{{ old('instructor', $program->instructor) }}">
                                            @error('instructor')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="price" class="form-label">Price ($)</label>
                                                    <input type="number" step="0.01" min="0" class="form-control @error('price') is-invalid @enderror" 
                                                           id="price" name="price" value="{{ old('price', $program->price) }}">
                                                    <div class="form-text">Leave empty for free</div>
                                                    @error('price')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="max_participants" class="form-label">Max Participants</label>
                                                    <input type="number" min="1" class="form-control @error('max_participants') is-invalid @enderror" 
                                                           id="max_participants" name="max_participants" value="{{ old('max_participants', $program->max_participants) }}">
                                                    @error('max_participants')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" min="0" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $program->sort_order) }}">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="is_active" 
                                                   value="1" id="is_active" {{ old('is_active', $program->is_active) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Event Linkage -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Event Linkage</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="event_id" class="form-label">Link to Event</label>
                                            <select class="form-select @error('event_id') is-invalid @enderror" id="event_id" name="event_id">
                                                <option value="">No Event</option>
                                                @foreach($events as $event)
                                                    <option value="{{ $event->id }}" {{ old('event_id', $program->event_id) == $event->id ? 'selected' : '' }}>
                                                        {{ $event->title }} ({{ $event->formatted_date }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="form-text">Optional: Link this program to an event</div>
                                            @error('event_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        @if($program->event)
                                            <div class="alert alert-info">
                                                <strong>Currently linked to:</strong><br>
                                                <a href="{{ route('admin.events.show', $program->event) }}" target="_blank">
                                                    {{ $program->event->title }}
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Featured Image</h5>
                                    </div>
                                    <div class="card-body">
                                        @if($program->image)
                                            <div class="mb-3">
                                                <img src="{{ $program->image_url }}" alt="Current Image" class="img-fluid rounded" style="max-height: 200px;">
                                                <div class="form-check mt-2">
                                                    <input class="form-check-input" type="checkbox" name="remove_image" value="1" id="remove_image">
                                                    <label class="form-check-label" for="remove_image">
                                                        Remove current image
                                                    </label>
                                                </div>
                                            </div>
                                        @endif
                                        <div class="mb-3">
                                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                                   id="image" name="image" accept="image/*">
                                            <div class="form-text">Recommended size: 800x600px</div>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div id="image-preview" class="mt-3" style="display: none;">
                                            <img id="preview-img" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">SEO</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="meta_title" class="form-label">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title', $program->meta_title) }}">
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                      id="meta_description" name="meta_description" rows="3">{{ old('meta_description', $program->meta_description) }}</textarea>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.programs.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                    <i class="fas fa-save"></i> Update Program
                                </button>
                                <button type="submit" name="action" value="save_and_continue" class="btn btn-success">
                                    <i class="fas fa-save"></i> Update & Continue Editing
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate slug from title (only if slug is empty)
    $('#title').on('input', function() {
        if (!$('#slug').val()) {
            const title = $(this).val();
            const slug = title.toLowerCase()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
            $('#slug').val(slug);
        }
    });

    // Image preview
    $('#image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

    // Toggle location field based on online status
    $('#is_online').change(function() {
        if ($(this).is(':checked')) {
            $('#location').prop('disabled', true);
        } else {
            $('#location').prop('disabled', false);
        }
    }).trigger('change'); // Trigger on page load

    // Remove image checkbox
    $('#remove_image').change(function() {
        if ($(this).is(':checked')) {
            $('#image').prop('required', false);
        }
    });
});
</script>
@endpush