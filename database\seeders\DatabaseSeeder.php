<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed in order of dependencies
        $this->call([
            UserSeeder::class,
            AdminSeeder::class,
            EventSeeder::class,
            BlogSeeder::class,
            SettingsSeeder::class,
            TestimonialSeeder::class,
        ]);
    }
}
