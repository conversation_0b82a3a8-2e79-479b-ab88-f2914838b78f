//>>>>> Section Title Start <<<<<//

.section-title {
	z-index: 1;
	h5{
		font-size: 20px;
		font-family: $sub-font;
		margin-bottom: 10px;
		line-height: 32px;
		font-weight: 400;
	}
	h2{
		font-weight: 400;
		color: $p900-clr;
	}
}

//>>>>> Section Title End <<<<<//

//>>>>> Basic Css Start <<<<<//

.section-bg-3 {
	background-color: $p1-clr;
}

.section-padding {
	padding: 120px 0;

	@include breakpoint(max-xl) {
		padding: 100px 0;
	}

	@include breakpoint(max-lg) {
		padding: 80px 0;
	}
}
.space-top {
	padding-top: 120px;
	@include breakpoint(max-xl) {
		padding-top: 100px;
	}
	@include breakpoint(max-lg) {
		padding-top: 80px;
	}
}
.space-bottom {
	padding-bottom: 120px;
	@include breakpoint(max-xl) {
		padding-bottom: 100px;
	}
	@include breakpoint(max-lg) {
		padding-bottom: 80px;
	}
}
//>>>>> Basic Css End <<<<<//

// Sidebar Checking
.cmn-overlay {
	transition: all 0.8s;
	background: rgb(2 1 1 / 78%);
	z-index: 999;
	position: fixed;
	top: 0;
	height: 0%;
	width: 100%;
	right: 0;
	&.active {
		height: 100%;
	}
}
.sidebar-wrapper {
	position: fixed;
	z-index: 99999;
	top: 0;
	right: -320px;
	transition: all 0.9s;
	overflow-y: scroll;
	height: 100vh;
	height: 100%;
	background: var(--n0);
	width: 320px;
	padding: 10px;
	&.active {
		right: 0px;
	}
	.ramove-area {
		border-bottom: 1px solid var(--n40);
		padding: 5px 10px 15px;
		.side-logo {
			width: 45px;
			height: 45px;
			img {
				width: 100%;
				width: 100%;
				object-fit: contain;
			}
		}
	}
	.side-remove {
		width: 40px;
		height: 40px;
		border-radius: 0;
		background: var(--p1);
		display: flex;
		align-items: center;
		justify-content: center;
		&:hover {
			cursor: pointer;
		}
	}
	//box
	.box {
		padding: 18px 10px;
		.sideba-gallery {
			display: flex;
			flex-wrap: wrap;
			gap: 5px;
			.small-gl {
				width: 32%;
			}
		}
		.side-infocontact {
			li {
				display: flex;
				align-items: center;
				gap: 10px;
				.icon {
					width: 40px;
					height: 40px;
					border-radius: 50%;
					border: 1px solid var(--n40);
					i {
						color: var(--n50);
						font-size: 18px;
					}
				}
				a {
					color: var(--n50);
					font-weight: 500;
				}
			}
		}
		.header-social {
			display: flex;
			align-items: center;
			gap: 10px;
			li {
				a {
					width: 40px;
					height: 40px;
					transition: all 0.4s;
					border: 1px solid var(--n40);
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					svg {
						width: 15px;
						height: 15px;
						transition: all 0.4s;
						path {
							transition: all 0.4s;
						}
					}
					i {
						font-size: 18px;
						color: var(--n50);
						transition: all 0.4s;
					}
					&:hover {
						background: var(--p1);
						svg {
							path {
								stroke: var(--n0);
								fill: var(--n0);
							}
						}
						i {
							color: var(--n0);
						}
					}
				}
			}
		}
	}
}
// Sidebar Checking
