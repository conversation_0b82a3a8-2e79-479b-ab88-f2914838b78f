<?php

namespace App\Providers;

use App\Services\SettingsService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SettingsService::class, function ($app) {
            return new SettingsService();
        });

        $this->app->alias(SettingsService::class, 'settings');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Initialize default settings if none exist
        $this->initializeDefaultSettings();
        
        // Share public settings with all views
        View::composer('*', function ($view) {
            $settingsService = app(SettingsService::class);
            $publicSettings = $settingsService->getPublicSettings();
            
            $view->with('settings', $publicSettings);
        });

        // Update mail configuration from settings
        $this->updateMailConfig();
    }

    /**
     * Initialize default settings if none exist
     */
    private function initializeDefaultSettings()
    {
        try {
            // Check if any settings exist
            $settingsCount = \App\Models\Setting::count();
            
            // If no settings exist, initialize them
            if ($settingsCount === 0) {
                $settingsService = app(SettingsService::class);
                $settingsService->initializeDefaults();
            }
        } catch (\Exception $e) {
            // Silently fail if settings table doesn't exist yet (during migrations)
        }
    }

    /**
     * Update mail configuration from database settings
     */
    private function updateMailConfig()
    {
        try {
            $settingsService = app(SettingsService::class);
            $emailSettings = $settingsService->getEmailSettings();

            if (!empty($emailSettings['smtp_host'])) {
                Config::set('mail.mailers.smtp.host', $emailSettings['smtp_host']);
            }
            
            if (!empty($emailSettings['smtp_port'])) {
                Config::set('mail.mailers.smtp.port', $emailSettings['smtp_port']);
            }
            
            if (!empty($emailSettings['smtp_username'])) {
                Config::set('mail.mailers.smtp.username', $emailSettings['smtp_username']);
            }
            
            if (!empty($emailSettings['smtp_password'])) {
                Config::set('mail.mailers.smtp.password', $emailSettings['smtp_password']);
            }
            
            if (!empty($emailSettings['mail_from_address'])) {
                Config::set('mail.from.address', $emailSettings['mail_from_address']);
            }
            
            if (!empty($emailSettings['mail_from_name'])) {
                Config::set('mail.from.name', $emailSettings['mail_from_name']);
            }
        } catch (\Exception $e) {
            // Silently fail if settings table doesn't exist yet
        }
    }
}