<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'category_id',
        'price',
        'compare_price',
        'cost_price',
        'stock_quantity',
        'min_stock_level',
        'weight',
        'dimensions',
        'featured_image',
        'gallery',
        'status',
        'is_featured',
        'meta_title',
        'meta_description',
        'specifications',
        'features',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'is_featured' => 'boolean',
        'gallery' => 'array',
        'specifications' => 'array',
        'features' => 'array',
        'stock_quantity' => 'integer',
        'min_stock_level' => 'integer',
        'sort_order' => 'integer',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the category that owns the product.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include featured products.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include in-stock products.
     */
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    /**
     * Get the product's featured image URL.
     */
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset('storage/' . $this->featured_image);
        }
        return asset('assets/img/products/default-product.jpg');
    }

    /**
     * Get the product's gallery URLs.
     */
    public function getGalleryUrlsAttribute()
    {
        if ($this->gallery && is_array($this->gallery)) {
            return array_map(function($image) {
                return asset('storage/' . $image);
            }, $this->gallery);
        }
        return [];
    }

    /**
     * Check if product is in stock.
     */
    public function getIsInStockAttribute()
    {
        return $this->stock_quantity > 0;
    }

    /**
     * Check if product is low stock.
     */
    public function getIsLowStockAttribute()
    {
        return $this->stock_quantity <= $this->min_stock_level;
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        return $this->price ? '₦' . number_format($this->price, 2) : 'Price on request';
    }

    /**
     * Get formatted compare price.
     */
    public function getFormattedComparePriceAttribute()
    {
        return $this->compare_price ? '₦' . number_format($this->compare_price, 2) : null;
    }

    /**
     * Get discount percentage.
     */
    public function getDiscountPercentageAttribute()
    {
        if ($this->price && $this->compare_price && $this->compare_price > $this->price) {
            return round((($this->compare_price - $this->price) / $this->compare_price) * 100);
        }
        return 0;
    }

    /**
     * Get formatted weight.
     */
    public function getFormattedWeightAttribute()
    {
        return $this->weight ? $this->weight . ' kg' : null;
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        switch ($this->status) {
            case 'active':
                return 'badge-success';
            case 'inactive':
                return 'badge-secondary';
            case 'out_of_stock':
                return 'badge-danger';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute()
    {
        switch ($this->status) {
            case 'active':
                return 'Active';
            case 'inactive':
                return 'Inactive';
            case 'out_of_stock':
                return 'Out of Stock';
            default:
                return 'Unknown';
        }
    }
}
