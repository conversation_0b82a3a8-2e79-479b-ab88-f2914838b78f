<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $services = [
            [
                'name' => 'Farmer Training Programs',
                'description' => 'Comprehensive training on modern agricultural practices, proper fertilizer application, and sustainable farming techniques. Our expert trainers provide hands-on guidance to help farmers maximize their crop yields and improve their farming operations.',
                'short_description' => 'Professional agricultural training for modern farming techniques',
                'price' => 25000.00,
                'duration' => '3 days',
                'features' => [
                    'Modern farming techniques',
                    'Fertilizer application methods',
                    'Crop management practices',
                    'Pest and disease control',
                    'Soil health management'
                ],
                'benefits' => [
                    'Increased crop yields',
                    'Reduced farming costs',
                    'Improved soil health',
                    'Better pest management',
                    'Enhanced farming knowledge'
                ],
                'requirements' => [
                    'Basic farming experience',
                    'Willingness to learn',
                    'Commitment to attend all sessions'
                ]
            ],
            [
                'name' => 'Soil Analysis Services',
                'description' => 'Professional soil testing and analysis to determine nutrient levels, pH, and soil health for optimal crop production. Our laboratory provides detailed reports with fertilizer recommendations.',
                'short_description' => 'Professional soil testing and nutrient analysis',
                'price' => 15000.00,
                'duration' => '5-7 days',
                'features' => [
                    'Nutrient level testing',
                    'pH analysis',
                    'Soil health assessment',
                    'Fertilizer recommendations',
                    'Detailed laboratory reports'
                ],
                'benefits' => [
                    'Optimized fertilizer use',
                    'Improved crop yields',
                    'Cost-effective farming',
                    'Better soil management',
                    'Data-driven decisions'
                ],
                'requirements' => [
                    'Soil samples from farm',
                    'Farm location details',
                    'Crop type information'
                ]
            ],
            [
                'name' => 'GAP Training & Certification',
                'description' => 'Good Agricultural Practices training to help farmers meet international standards and access premium markets. Includes certification facilitation and ongoing support.',
                'short_description' => 'GAP training for international market access',
                'price' => 35000.00,
                'duration' => '5 days',
                'features' => [
                    'Food safety protocols',
                    'Environmental sustainability',
                    'Worker health and safety',
                    'Record keeping systems',
                    'Traceability procedures'
                ],
                'benefits' => [
                    'Access to premium markets',
                    'Higher product prices',
                    'International certification',
                    'Improved farm management',
                    'Enhanced product quality'
                ],
                'requirements' => [
                    'Existing farming operation',
                    'Commitment to GAP standards',
                    'Record keeping capability'
                ]
            ]
        ];

        $service = $this->faker->randomElement($services);

        return [
            'name' => $service['name'],
            'slug' => Str::slug($service['name']),
            'description' => $service['description'],
            'short_description' => $service['short_description'],
            'category_id' => null,
            'price' => $service['price'],
            'duration' => $service['duration'],
            'status' => 'active',
            'is_featured' => $this->faker->boolean(40),
            'meta_title' => $service['name'] . ' - Pescot Agro Industry',
            'meta_description' => $service['short_description'] . ' - Professional agricultural services from Pescot Agro Industry Limited.',
            'features' => $service['features'],
            'benefits' => $service['benefits'],
            'requirements' => $service['requirements'],
            'sort_order' => $this->faker->numberBetween(1, 100),
        ];
    }
}
