<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get a setting value
     */
    public function get($key, $default = null)
    {
        return Setting::get($key, $default);
    }

    /**
     * Set a setting value
     */
    public function set($key, $value, $type = 'text', $group = 'general', $isPublic = false)
    {
        return Setting::set($key, $value, $type, $group, $isPublic);
    }

    /**
     * Get multiple settings
     */
    public function getMultiple(array $keys, $default = null)
    {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = $this->get($key, $default);
        }
        return $result;
    }

    /**
     * Set multiple settings
     */
    public function setMultiple(array $settings, $group = 'general')
    {
        foreach ($settings as $key => $data) {
            if (is_array($data)) {
                $this->set(
                    $key,
                    $data['value'],
                    $data['type'] ?? 'text',
                    $data['group'] ?? $group,
                    $data['is_public'] ?? false
                );
            } else {
                $this->set($key, $data, 'text', $group);
            }
        }
    }

    /**
     * Get settings by group
     */
    public function getByGroup($group)
    {
        return Setting::getByGroup($group);
    }

    /**
     * Get all public settings for frontend
     */
    public function getPublicSettings()
    {
        return Setting::getPublicSettings();
    }

    /**
     * Get site configuration settings
     */
    public function getSiteConfig()
    {
        return $this->getByGroup('site');
    }

    /**
     * Get SEO settings
     */
    public function getSeoSettings()
    {
        return $this->getByGroup('seo');
    }

    /**
     * Get social media settings
     */
    public function getSocialSettings()
    {
        return $this->getByGroup('social');
    }

    /**
     * Get email settings
     */
    public function getEmailSettings()
    {
        return $this->getByGroup('email');
    }

    /**
     * Get theme settings
     */
    public function getThemeSettings()
    {
        return $this->getByGroup('theme');
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults()
    {
        $defaults = [
            // Site Settings
            'site_name' => ['value' => 'Pescot Agro Industry Limited', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_description' => ['value' => 'Leading provider of quality fertilizers and agricultural solutions in Nigeria', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_logo' => ['value' => 'assets/img/logo/pescot-logo.png', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_favicon' => ['value' => 'assets/img/logo/favicon.ico', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'site', 'is_public' => true],
            'contact_phone' => ['value' => '+234 ************', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'contact_phone_2' => ['value' => '+234 ************', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_address' => ['value' => 'Pescot Agro Industry Limited, Industrial Area, Nigeria', 'type' => 'textarea', 'group' => 'site', 'is_public' => true],
            'business_hours' => ['value' => 'Monday - Friday: 8:00 AM - 5:00 PM', 'type' => 'text', 'group' => 'site', 'is_public' => true],

            // SEO Settings
            'seo_title' => ['value' => 'Pescot Agro Industry Limited - Quality Fertilizers & Agricultural Solutions', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'seo_description' => ['value' => 'Leading provider of organic and inorganic fertilizers, agricultural training, and farming solutions in Nigeria. Quality products for maximum crop yield.', 'type' => 'textarea', 'group' => 'seo', 'is_public' => true],
            'seo_keywords' => ['value' => 'fertilizers, agriculture, farming, NPK, organic fertilizer, Nigeria, crop yield, agricultural training', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'google_analytics_id' => ['value' => '', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'google_tag_manager_id' => ['value' => '', 'type' => 'text', 'group' => 'seo', 'is_public' => true],

            // Social Media Settings
            'facebook_url' => ['value' => 'https://facebook.com/pescotagroindustry', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'twitter_url' => ['value' => 'https://twitter.com/pescotagroltd', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'instagram_url' => ['value' => 'https://instagram.com/pescotagroindustry', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'linkedin_url' => ['value' => 'https://linkedin.com/company/pescot-agro-industry', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'youtube_url' => ['value' => 'https://youtube.com/c/pescotagroindustry', 'type' => 'url', 'group' => 'social', 'is_public' => true],

            // Contact Settings
            'contact_form_email' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'contact', 'is_public' => false],
            'sales_email' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'contact', 'is_public' => true],
            'support_email' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'contact', 'is_public' => true],
            'whatsapp_number' => ['value' => '+2348000000000', 'type' => 'text', 'group' => 'contact', 'is_public' => true],

            // SMTP Settings
            'smtp_host' => ['value' => '', 'type' => 'text', 'group' => 'smtp', 'is_public' => false],
            'smtp_port' => ['value' => '587', 'type' => 'integer', 'group' => 'smtp', 'is_public' => false],
            'smtp_username' => ['value' => '', 'type' => 'text', 'group' => 'smtp', 'is_public' => false],
            'smtp_password' => ['value' => '', 'type' => 'password', 'group' => 'smtp', 'is_public' => false],
            'mail_from_address' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'smtp', 'is_public' => false],
            'mail_from_name' => ['value' => 'Pescot Agro Industry Limited', 'type' => 'text', 'group' => 'smtp', 'is_public' => false],

            // Company Information
            'company_registration' => ['value' => 'RC-XXXXXXX', 'type' => 'text', 'group' => 'company', 'is_public' => true],
            'years_experience' => ['value' => '15', 'type' => 'number', 'group' => 'company', 'is_public' => true],
            'farmers_trained' => ['value' => '1200', 'type' => 'number', 'group' => 'company', 'is_public' => true],
            'bags_distributed' => ['value' => '5000', 'type' => 'number', 'group' => 'company', 'is_public' => true],
            'states_covered' => ['value' => '25', 'type' => 'number', 'group' => 'company', 'is_public' => true],

            // Newsletter Settings
            'newsletter_enabled' => ['value' => '1', 'type' => 'boolean', 'group' => 'newsletter', 'is_public' => false],
            'newsletter_title' => ['value' => 'Stay Updated with Pescot Agro Industry', 'type' => 'text', 'group' => 'newsletter', 'is_public' => true],
            'newsletter_description' => ['value' => 'Get the latest updates on agricultural products, farming tips, and industry news.', 'type' => 'text', 'group' => 'newsletter', 'is_public' => true],

            // reCAPTCHA Settings
            'recaptcha_site_key' => ['value' => '', 'type' => 'text', 'group' => 'recaptcha', 'is_public' => false],
            'recaptcha_secret_key' => ['value' => '', 'type' => 'password', 'group' => 'recaptcha', 'is_public' => false],
            'recaptcha_enabled' => ['value' => false, 'type' => 'boolean', 'group' => 'recaptcha', 'is_public' => false],
        ];

        foreach ($defaults as $key => $data) {
            // Only create if doesn't exist
            if (!Setting::where('key', $key)->exists()) {
                Setting::create([
                    'key' => $key,
                    'value' => $data['value'],
                    'type' => $data['type'],
                    'group' => $data['group'],
                    'is_public' => $data['is_public'],
                    'description' => $this->getSettingDescription($key)
                ]);
            }
        }
    }

    /**
     * Get setting description
     */
    private function getSettingDescription($key)
    {
        $descriptions = [
            'site_name' => 'The name of your website',
            'site_description' => 'A brief description of your website',
            'site_logo' => 'URL or path to your site logo',
            'site_favicon' => 'URL or path to your site favicon',
            'contact_email' => 'Primary contact email address',
            'contact_phone' => 'Primary contact phone number',
            'site_address' => 'Physical address of your organization',
            'seo_title' => 'Default SEO title for your site',
            'seo_description' => 'Default SEO description for your site',
            'seo_keywords' => 'Default SEO keywords for your site',
            'google_analytics_id' => 'Google Analytics tracking ID',
            'google_tag_manager_id' => 'Google Tag Manager container ID',
            'facebook_url' => 'Facebook page URL',
            'twitter_url' => 'Twitter profile URL',
            'instagram_url' => 'Instagram profile URL',
            'linkedin_url' => 'LinkedIn profile URL',
            'youtube_url' => 'YouTube channel URL',
            'primary_color' => 'Primary theme color',
            'secondary_color' => 'Secondary theme color',
            'font_family' => 'Default font family for the site',
            'enable_dark_mode' => 'Enable dark mode theme option',
            'timezone' => 'Default timezone for the application',
            'date_format' => 'Default date format',
            'time_format' => 'Default time format',
            'posts_per_page' => 'Number of posts to display per page',
            'enable_comments' => 'Allow comments on posts',
            'enable_registration' => 'Allow new user registration',
            'maintenance_mode' => 'Put site in maintenance mode',
        ];

        return $descriptions[$key] ?? '';
    }

    /**
     * Clear all settings cache
     */
    public function clearCache()
    {
        Setting::clearCache();
    }
}
