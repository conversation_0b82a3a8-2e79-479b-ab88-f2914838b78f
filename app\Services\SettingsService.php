<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get a setting value
     */
    public function get($key, $default = null)
    {
        return Setting::get($key, $default);
    }

    /**
     * Set a setting value
     */
    public function set($key, $value, $type = 'text', $group = 'general', $isPublic = false)
    {
        return Setting::set($key, $value, $type, $group, $isPublic);
    }

    /**
     * Get multiple settings
     */
    public function getMultiple(array $keys, $default = null)
    {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = $this->get($key, $default);
        }
        return $result;
    }

    /**
     * Set multiple settings
     */
    public function setMultiple(array $settings, $group = 'general')
    {
        foreach ($settings as $key => $data) {
            if (is_array($data)) {
                $this->set(
                    $key,
                    $data['value'],
                    $data['type'] ?? 'text',
                    $data['group'] ?? $group,
                    $data['is_public'] ?? false
                );
            } else {
                $this->set($key, $data, 'text', $group);
            }
        }
    }

    /**
     * Get settings by group
     */
    public function getByGroup($group)
    {
        return Setting::getByGroup($group);
    }

    /**
     * Get all public settings for frontend
     */
    public function getPublicSettings()
    {
        return Setting::getPublicSettings();
    }

    /**
     * Get site configuration settings
     */
    public function getSiteConfig()
    {
        return $this->getByGroup('site');
    }

    /**
     * Get SEO settings
     */
    public function getSeoSettings()
    {
        return $this->getByGroup('seo');
    }

    /**
     * Get social media settings
     */
    public function getSocialSettings()
    {
        return $this->getByGroup('social');
    }

    /**
     * Get email settings
     */
    public function getEmailSettings()
    {
        return $this->getByGroup('email');
    }

    /**
     * Get theme settings
     */
    public function getThemeSettings()
    {
        return $this->getByGroup('theme');
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults()
    {
        $defaults = [
            // Site Settings
            'site_name' => ['value' => 'My Laravel Site', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_description' => ['value' => 'A modern Laravel application', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_logo' => ['value' => '', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_favicon' => ['value' => '', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'site', 'is_public' => true],
            'contact_phone' => ['value' => '', 'type' => 'text', 'group' => 'site', 'is_public' => true],
            'site_address' => ['value' => '', 'type' => 'textarea', 'group' => 'site', 'is_public' => true],

            // SEO Settings
            'seo_title' => ['value' => 'My Laravel Site', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'seo_description' => ['value' => 'A modern Laravel application', 'type' => 'textarea', 'group' => 'seo', 'is_public' => true],
            'seo_keywords' => ['value' => 'laravel, php, web development', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'google_analytics_id' => ['value' => '', 'type' => 'text', 'group' => 'seo', 'is_public' => true],
            'google_tag_manager_id' => ['value' => '', 'type' => 'text', 'group' => 'seo', 'is_public' => true],

            // Social Media Settings
            'facebook_url' => ['value' => '', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'twitter_url' => ['value' => '', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'instagram_url' => ['value' => '', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'linkedin_url' => ['value' => '', 'type' => 'url', 'group' => 'social', 'is_public' => true],
            'youtube_url' => ['value' => '', 'type' => 'url', 'group' => 'social', 'is_public' => true],

            // Email Settings
            'smtp_host' => ['value' => '', 'type' => 'text', 'group' => 'email', 'is_public' => false],
            'smtp_port' => ['value' => '587', 'type' => 'integer', 'group' => 'email', 'is_public' => false],
            'smtp_username' => ['value' => '', 'type' => 'text', 'group' => 'email', 'is_public' => false],
            'smtp_password' => ['value' => '', 'type' => 'password', 'group' => 'email', 'is_public' => false],
            'mail_from_address' => ['value' => '<EMAIL>', 'type' => 'email', 'group' => 'email', 'is_public' => false],
            'mail_from_name' => ['value' => 'My Laravel Site', 'type' => 'text', 'group' => 'email', 'is_public' => false],

            // Theme Settings
            'primary_color' => ['value' => '#2678a1db', 'type' => 'color', 'group' => 'theme', 'is_public' => true],
            'secondary_color' => ['value' => '#6c757d', 'type' => 'color', 'group' => 'theme', 'is_public' => true],
            'font_family' => ['value' => 'Inter, sans-serif', 'type' => 'text', 'group' => 'theme', 'is_public' => true],
            'enable_dark_mode' => ['value' => false, 'type' => 'boolean', 'group' => 'theme', 'is_public' => true],

            // General Settings
            'timezone' => ['value' => 'UTC', 'type' => 'text', 'group' => 'general', 'is_public' => false],
            'date_format' => ['value' => 'Y-m-d', 'type' => 'text', 'group' => 'general', 'is_public' => true],
            'time_format' => ['value' => 'H:i:s', 'type' => 'text', 'group' => 'general', 'is_public' => true],
            'posts_per_page' => ['value' => 10, 'type' => 'integer', 'group' => 'general', 'is_public' => true],
            'enable_comments' => ['value' => true, 'type' => 'boolean', 'group' => 'general', 'is_public' => true],
            'enable_registration' => ['value' => true, 'type' => 'boolean', 'group' => 'general', 'is_public' => false],
            'maintenance_mode' => ['value' => false, 'type' => 'boolean', 'group' => 'general', 'is_public' => false],
        ];

        foreach ($defaults as $key => $data) {
            // Only create if doesn't exist
            if (!Setting::where('key', $key)->exists()) {
                Setting::create([
                    'key' => $key,
                    'value' => $data['value'],
                    'type' => $data['type'],
                    'group' => $data['group'],
                    'is_public' => $data['is_public'],
                    'description' => $this->getSettingDescription($key)
                ]);
            }
        }
    }

    /**
     * Get setting description
     */
    private function getSettingDescription($key)
    {
        $descriptions = [
            'site_name' => 'The name of your website',
            'site_description' => 'A brief description of your website',
            'site_logo' => 'URL or path to your site logo',
            'site_favicon' => 'URL or path to your site favicon',
            'contact_email' => 'Primary contact email address',
            'contact_phone' => 'Primary contact phone number',
            'site_address' => 'Physical address of your organization',
            'seo_title' => 'Default SEO title for your site',
            'seo_description' => 'Default SEO description for your site',
            'seo_keywords' => 'Default SEO keywords for your site',
            'google_analytics_id' => 'Google Analytics tracking ID',
            'google_tag_manager_id' => 'Google Tag Manager container ID',
            'facebook_url' => 'Facebook page URL',
            'twitter_url' => 'Twitter profile URL',
            'instagram_url' => 'Instagram profile URL',
            'linkedin_url' => 'LinkedIn profile URL',
            'youtube_url' => 'YouTube channel URL',
            'primary_color' => 'Primary theme color',
            'secondary_color' => 'Secondary theme color',
            'font_family' => 'Default font family for the site',
            'enable_dark_mode' => 'Enable dark mode theme option',
            'timezone' => 'Default timezone for the application',
            'date_format' => 'Default date format',
            'time_format' => 'Default time format',
            'posts_per_page' => 'Number of posts to display per page',
            'enable_comments' => 'Allow comments on posts',
            'enable_registration' => 'Allow new user registration',
            'maintenance_mode' => 'Put site in maintenance mode',
        ];

        return $descriptions[$key] ?? '';
    }

    /**
     * Clear all settings cache
     */
    public function clearCache()
    {
        Setting::clearCache();
    }
}
