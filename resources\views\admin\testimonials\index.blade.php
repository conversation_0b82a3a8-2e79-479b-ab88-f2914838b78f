@extends('layouts.admin')

@section('title', 'Testimonials Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Testimonials Management</h3>
                    <a href="{{ route('admin.testimonials.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Testimonial
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.testimonials.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search testimonials..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.testimonials.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Testimonials Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Testimonial</th>
                                    <th>Rating</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($testimonials as $testimonial)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($testimonial->client_image)
                                                <img src="{{ asset('storage/' . $testimonial->client_image) }}" 
                                                     alt="{{ $testimonial->client_name }}" 
                                                     class="rounded-circle me-3" 
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    {{ strtoupper(substr($testimonial->client_name, 0, 2)) }}
                                                </div>
                                            @endif
                                            <div>
                                                <h6 class="mb-0">{{ $testimonial->client_name }}</h6>
                                                <small class="text-muted">{{ $testimonial->client_position }}</small>
                                                @if($testimonial->client_company)
                                                    <br><small class="text-muted">{{ $testimonial->client_company }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ Str::limit($testimonial->testimonial, 100) }}</span>
                                        @if($testimonial->is_featured)
                                            <br><span class="badge badge-warning">Featured</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($testimonial->rating)
                                            <div class="text-warning">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star{{ $i <= $testimonial->rating ? '' : '-o' }}"></i>
                                                @endfor
                                                <br><small class="text-muted">{{ $testimonial->rating }}/5</small>
                                            </div>
                                        @else
                                            <span class="text-muted">No rating</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $testimonial->status_badge_class }}">
                                            {{ $testimonial->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.testimonials.show', $testimonial) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-{{ $testimonial->status === 'active' ? 'warning' : 'success' }}" 
                                                    onclick="toggleStatus({{ $testimonial->id }})"
                                                    title="{{ $testimonial->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $testimonial->status === 'active' ? 'eye-slash' : 'eye' }}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteTestimonial({{ $testimonial->id }})"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-quote-left fa-3x mb-3"></i>
                                            <h5>No testimonials found</h5>
                                            <p>Start by adding your first testimonial.</p>
                                            <a href="{{ route('admin.testimonials.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Add Testimonial
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($testimonials->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $testimonials->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle testimonial status
function toggleStatus(testimonialId) {
    if (confirm('Are you sure you want to change the status of this testimonial?')) {
        fetch(`/admin/testimonials/${testimonialId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating testimonial status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating testimonial status');
        });
    }
}

// Delete testimonial
function deleteTestimonial(testimonialId) {
    if (confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/testimonials/${testimonialId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
