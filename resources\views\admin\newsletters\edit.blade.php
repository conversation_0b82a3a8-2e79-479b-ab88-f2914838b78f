@extends('layouts.admin')

@section('title', 'Edit Newsletter Subscriber')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Newsletter Subscriber</h3>
                    <div>
                        <a href="{{ route('admin.newsletters.show', $newsletter) }}" class="btn btn-info mr-2">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.newsletters.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.newsletters.update', $newsletter) }}" method="POST" id="newsletterForm">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Subscriber Information -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscriber Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email">Email Address <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                           id="email" name="email" value="{{ old('email', $newsletter->email) }}" required>
                                                    @error('email')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Primary email address for newsletter subscription</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name">Full Name</label>
                                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                           id="name" name="name" value="{{ old('name', $newsletter->name) }}">
                                                    @error('name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: Subscriber's full name</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="phone">Phone Number</label>
                                                    <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                                           id="phone" name="phone" value="{{ old('phone', $newsletter->phone) }}">
                                                    @error('phone')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: Contact phone number</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">Location</label>
                                                    <input type="text" class="form-control @error('location') is-invalid @enderror"
                                                           id="location" name="location" value="{{ old('location', $newsletter->location) }}">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">Optional: City, country or region</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Newsletter Preferences -->
                                        <div class="form-group">
                                            <label>Newsletter Preferences</label>
                                            @php
                                                $currentPreferences = json_decode($newsletter->preferences, true) ?? [];
                                            @endphp
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_weekly"
                                                               name="preferences[]" value="weekly_digest"
                                                               {{ in_array('weekly_digest', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_weekly">
                                                            Weekly Digest
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_breaking"
                                                               name="preferences[]" value="breaking_news"
                                                               {{ in_array('breaking_news', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_breaking">
                                                            Breaking News
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_promotions"
                                                               name="preferences[]" value="promotions"
                                                               {{ in_array('promotions', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_promotions">
                                                            Promotions
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_events"
                                                               name="preferences[]" value="events"
                                                               {{ in_array('events', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_events">
                                                            Events & Updates
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_products"
                                                               name="preferences[]" value="product_updates"
                                                               {{ in_array('product_updates', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_products">
                                                            Product Updates
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="pref_tips"
                                                               name="preferences[]" value="tips_tutorials"
                                                               {{ in_array('tips_tutorials', old('preferences', $currentPreferences)) ? 'checked' : '' }}>
                                                        <label class="custom-control-label" for="pref_tips">
                                                            Tips & Tutorials
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">Select the types of newsletters this subscriber wants to receive</small>
                                        </div>

                                        <!-- Additional Notes -->
                                        <div class="form-group">
                                            <label for="notes">Admin Notes</label>
                                            <textarea class="form-control @error('notes') is-invalid @enderror"
                                                      id="notes" name="notes" rows="3"
                                                      placeholder="Internal notes about this subscriber...">{{ old('notes', $newsletter->notes) }}</textarea>
                                            @error('notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Internal notes (not visible to subscriber)</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Activity History -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Activity History</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Subscribed:</strong> {{ $newsletter->created_at->format('M d, Y H:i') }}</p>
                                                <p><strong>Last Updated:</strong> {{ $newsletter->updated_at->format('M d, Y H:i') }}</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Last Activity:</strong>
                                                    @if($newsletter->last_activity_at)
                                                        {{ $newsletter->last_activity_at->format('M d, Y H:i') }}
                                                    @else
                                                        <span class="text-muted">No activity recorded</span>
                                                    @endif
                                                </p>
                                                <p><strong>Email Opens:</strong> {{ $newsletter->email_opens ?? 0 }}</p>
                                            </div>
                                        </div>
                                        @if($newsletter->unsubscribed_at)
                                            <div class="alert alert-warning">
                                                <strong>Unsubscribed:</strong> {{ $newsletter->unsubscribed_at->format('M d, Y H:i') }}
                                                @if($newsletter->unsubscribe_reason)
                                                    <br><strong>Reason:</strong> {{ $newsletter->unsubscribe_reason }}
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-4">
                                <!-- Quick Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Quick Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group-vertical w-100 mb-3">
                                            <button type="button" class="btn btn-{{ $newsletter->status === 'active' ? 'warning' : 'success' }}"
                                                    onclick="toggleStatus({{ $newsletter->id }})">
                                                <i class="fas fa-{{ $newsletter->status === 'active' ? 'pause' : 'play' }}"></i>
                                                {{ $newsletter->status === 'active' ? 'Deactivate' : 'Activate' }}
                                            </button>
                                            @if(!$newsletter->is_verified)
                                                <button type="button" class="btn btn-success" onclick="verifySubscriber({{ $newsletter->id }})">
                                                    <i class="fas fa-check"></i> Verify Email
                                                </button>
                                            @endif
                                            <button type="button" class="btn btn-info" onclick="sendTestEmail({{ $newsletter->id }})">
                                                <i class="fas fa-envelope"></i> Send Test Email
                                            </button>
                                            <button type="button" class="btn btn-danger" onclick="deleteNewsletter({{ $newsletter->id }})">
                                                <i class="fas fa-trash"></i> Delete Subscriber
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Subscription Settings -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscription Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                                <option value="active" {{ old('status', $newsletter->status) === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status', $newsletter->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                <option value="unsubscribed" {{ old('status', $newsletter->status) === 'unsubscribed' ? 'selected' : '' }}>Unsubscribed</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="is_verified"
                                                       name="is_verified" value="1"
                                                       {{ old('is_verified', $newsletter->is_verified) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_verified">
                                                    Email Verified
                                                </label>
                                            </div>
                                            <small class="form-text text-muted">Mark as verified to skip email confirmation</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="subscribed_at">Subscription Date</label>
                                            <input type="datetime-local" class="form-control @error('subscribed_at') is-invalid @enderror"
                                                   id="subscribed_at" name="subscribed_at"
                                                   value="{{ old('subscribed_at', $newsletter->created_at->format('Y-m-d\TH:i')) }}">
                                            @error('subscribed_at')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">When the subscription was created</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="source">Subscription Source</label>
                                            <select class="form-control @error('source') is-invalid @enderror" id="source" name="source">
                                                <option value="manual" {{ old('source', $newsletter->source) === 'manual' ? 'selected' : '' }}>Manual (Admin)</option>
                                                <option value="website" {{ old('source', $newsletter->source) === 'website' ? 'selected' : '' }}>Website Form</option>
                                                <option value="popup" {{ old('source', $newsletter->source) === 'popup' ? 'selected' : '' }}>Popup</option>
                                                <option value="footer" {{ old('source', $newsletter->source) === 'footer' ? 'selected' : '' }}>Footer</option>
                                                <option value="social_media" {{ old('source', $newsletter->source) === 'social_media' ? 'selected' : '' }}>Social Media</option>
                                                <option value="import" {{ old('source', $newsletter->source) === 'import' ? 'selected' : '' }}>Import</option>
                                                <option value="api" {{ old('source', $newsletter->source) === 'api' ? 'selected' : '' }}>API</option>
                                                <option value="other" {{ old('source', $newsletter->source) === 'other' ? 'selected' : '' }}>Other</option>
                                            </select>
                                            @error('source')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="ip_address">IP Address</label>
                                            <input type="text" class="form-control @error('ip_address') is-invalid @enderror"
                                                   id="ip_address" name="ip_address"
                                                   value="{{ old('ip_address', $newsletter->ip_address) }}">
                                            @error('ip_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">IP address when subscribed</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="user_agent">User Agent</label>
                                            <textarea class="form-control @error('user_agent') is-invalid @enderror"
                                                      id="user_agent" name="user_agent" rows="3"
                                                      placeholder="Browser/device information...">{{ old('user_agent', $newsletter->user_agent) }}</textarea>
                                            @error('user_agent')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Browser/device information</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Preview Card -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Subscriber Preview</h5>
                                    </div>
                                    <div class="card-body" id="subscriberPreview">
                                        <!-- Preview will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary mr-2">
                                    <i class="fas fa-save"></i> Update Subscriber
                                </button>
                                <button type="button" class="btn btn-success mr-2" id="updateAndNotify">
                                    <i class="fas fa-envelope"></i> Update & Notify
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.newsletters.show', $newsletter) }}" class="btn btn-info mr-2">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{{ route('admin.newsletters.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this newsletter subscription?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.custom-control-label {
    font-weight: normal;
}

.form-text {
    font-size: 0.875rem;
}

#subscriberPreview .subscriber-info {
    text-align: left;
}

#subscriberPreview .subscriber-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #2678a1db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
}

#subscriberPreview .preferences-list {
    list-style: none;
    padding: 0;
}

#subscriberPreview .preferences-list li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

#subscriberPreview .preferences-list li:last-child {
    border-bottom: none;
}

.btn-group-vertical .btn {
    margin-bottom: 0.5rem;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Real-time preview update
    function updatePreview() {
        const email = $('#email').val();
        const name = $('#name').val();
        const phone = $('#phone').val();
        const location = $('#location').val();
        const status = $('#status').val();
        const isVerified = $('#is_verified').is(':checked');
        const source = $('#source').val();

        // Get selected preferences
        const preferences = [];
        $('input[name="preferences[]"]').each(function() {
            if ($(this).is(':checked')) {
                preferences.push($(this).next('label').text().trim());
            }
        });

        if (email) {
            const avatar = email.charAt(0).toUpperCase();
            let previewHtml = `
                <div class="subscriber-info">
                    <div class="subscriber-avatar">${avatar}</div>
                    <div class="text-center mb-3">
                        <strong>${name || 'No name provided'}</strong><br>
                        <span class="text-muted">${email}</span>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <span class="badge badge-${status === 'active' ? 'success' : (status === 'inactive' ? 'warning' : 'danger')}">
                                ${status.charAt(0).toUpperCase() + status.slice(1)}
                            </span>
                        </div>
                        <div class="col-6">
                            <span class="badge badge-${isVerified ? 'success' : 'warning'}">
                                ${isVerified ? 'Verified' : 'Unverified'}
                            </span>
                        </div>
                    </div>

                    ${phone ? `<p><i class="fas fa-phone"></i> ${phone}</p>` : ''}
                    ${location ? `<p><i class="fas fa-map-marker-alt"></i> ${location}</p>` : ''}
                    <p><i class="fas fa-source"></i> Source: ${source.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>

                    ${preferences.length > 0 ? `
                        <div class="mt-3">
                            <strong>Preferences:</strong>
                            <ul class="preferences-list mt-2">
                                ${preferences.map(pref => `<li><i class="fas fa-check text-success"></i> ${pref}</li>`).join('')}
                            </ul>
                        </div>
                    ` : '<p class="text-muted">No preferences selected</p>'}
                </div>
            `;

            $('#subscriberPreview').html(previewHtml);
        }
    }

    // Update preview on input change
    $('#email, #name, #phone, #location, #status, #source').on('input change', updatePreview);
    $('#is_verified').on('change', updatePreview);
    $('input[name="preferences[]"]').on('change', updatePreview);

    // Initial preview update
    updatePreview();

    // Form validation
    $('#newsletterForm').on('submit', function(e) {
        const email = $('#email').val().trim();

        if (!email) {
            e.preventDefault();
            alert('Email address is required.');
            $('#email').focus();
            return false;
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            $('#email').focus();
            return false;
        }
    });

    // Update and notify
    $('#updateAndNotify').on('click', function() {
        // Add hidden input to indicate notification should be sent
        $('<input>').attr({
            type: 'hidden',
            name: 'send_notification',
            value: '1'
        }).appendTo('#newsletterForm');

        $('#newsletterForm').submit();
    });

    // Character counter for notes
    $('#notes').on('input', function() {
        const maxLength = 500;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after(`<small class="form-text text-muted char-counter"></small>`);
        }

        $(this).next('.char-counter').text(`${currentLength}/${maxLength} characters`);

        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger');
        }
    });
});

// Toggle newsletter status
function toggleStatus(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating newsletter status');
            }
        },
        error: function() {
            alert('Error updating newsletter status');
        }
    });
}

// Verify subscriber
function verifySubscriber(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/verify`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error verifying subscriber');
            }
        },
        error: function() {
            alert('Error verifying subscriber');
        }
    });
}

// Send test email
function sendTestEmail(newsletterId) {
    if (confirm('Send a test email to this subscriber?')) {
        $.ajax({
            url: `/admin/newsletters/${newsletterId}/send-test`,
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Test email sent successfully!');
                } else {
                    alert('Error sending test email');
                }
            },
            error: function() {
                alert('Error sending test email');
            }
        });
    }
}

// Delete newsletter
function deleteNewsletter(newsletterId) {
    $('#deleteForm').attr('action', `/admin/newsletters/${newsletterId}`);
    $('#deleteModal').modal('show');
}
</script>
@endpush
