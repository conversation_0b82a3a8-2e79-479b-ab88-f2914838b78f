<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Admin Panel') - {{ config('app.name') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 250px;
            --primary-color: #2d5016;
            --secondary-color: #4a7c59;
            --accent-color: #6b8e23;
            --light-green: #8fbc8f;
            --dark-green: #1a3009;
            --success-green: #228b22;
            --warning-orange: #ff8c00;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #f0f8f0 100%);
            min-height: 100vh;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-green) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 4px 0 20px rgba(45, 80, 22, 0.15);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            text-align: center;
        }

        .sidebar-header h4 {
            color: white;
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.7rem;
            display: block;
            margin-top: 0.5rem;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
            height: calc(100vh - 120px);
            overflow-y: auto;
            overflow-x: hidden;
        }
        
        .sidebar-menu .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .sidebar-menu .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(107, 142, 35, 0.3);
            border-left: 4px solid var(--light-green);
        }

        .sidebar-menu .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid #e9ecef;
        }
        
        .content-wrapper {
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 25px rgba(45, 80, 22, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid var(--accent-color);
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 35px rgba(45, 80, 22, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--light-green) 0%, var(--accent-color) 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
        }

        .btn {
            border-radius: 10px;
            font-weight: 600;
            padding: 10px 20px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(45, 80, 22, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--dark-green) 0%, var(--primary-color) 100%);
            box-shadow: 0 6px 20px rgba(45, 80, 22, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-green) 0%, var(--accent-color) 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(34, 139, 34, 0.2);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--success-green) 100%);
            box-shadow: 0 6px 20px rgba(34, 139, 34, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 6px 20px rgba(45, 80, 22, 0.3);
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        /* Table Styling */
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(45, 80, 22, 0.08);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 700;
            padding: 18px;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.05) 0%, rgba(107, 142, 35, 0.05) 100%);
            transform: scale(1.01);
        }

        /* Form Styling */
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(107, 142, 35, 0.25);
            transform: translateY(-1px);
        }

        /* Badge Styling */
        .badge {
            border-radius: 25px;
            padding: 8px 16px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: linear-gradient(135deg, var(--success-green) 0%, var(--accent-color) 100%);
        }

        .badge-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .badge-warning {
            background: linear-gradient(135deg, var(--warning-orange) 0%, #ff7f00 100%);
        }

        /* Alert Styling */
        .alert {
            border: none;
            border-radius: 15px;
            padding: 20px 25px;
            border-left: 5px solid;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(34, 139, 34, 0.1) 0%, rgba(107, 142, 35, 0.1) 100%);
            border-left-color: var(--success-green);
            color: var(--dark-green);
        }

        /* Dropdown Styling */
        .dropdown-menu {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 50px rgba(45, 80, 22, 0.15);
            padding: 15px 0;
            border: 1px solid rgba(45, 80, 22, 0.1);
        }

        .dropdown-item {
            padding: 12px 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(45, 80, 22, 0.1) 0%, rgba(107, 142, 35, 0.1) 100%);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        /* Navbar Styling */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 15px rgba(45, 80, 22, 0.2);
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .navbar .nav-link:hover {
            color: #ffffff !important;
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar,
        .sidebar-menu::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track,
        .sidebar-menu::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb,
        .sidebar-menu::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb:hover,
        .sidebar-menu::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox scrollbar */
        .sidebar,
        .sidebar-menu {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        /* Animation */
        .main-content {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1050;
                width: 280px;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>
    @stack('styles')
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4 class="mb-0">
                <i class="fas fa-seedling me-2"></i>
                Pescot Agro
            </h4>
            <small>Admin Panel</small>
        </div>
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.admins.*') ? 'active' : '' }}" href="{{ route('admin.admins.index') }}">
                        <i class="fas fa-users-cog me-2"></i>
                        Admin Users
                    </a>
                </li>

                <!-- Content Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.posts.*') || request()->routeIs('admin.categories.*') || request()->routeIs('admin.comments.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#contentMenu">
                        <i class="fas fa-edit me-2"></i>
                        Content Management
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.posts.*') || request()->routeIs('admin.categories.*') || request()->routeIs('admin.comments.*') ? 'show' : '' }}" id="contentMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.posts.*') ? 'active' : '' }}" href="{{ route('admin.posts.index') }}">
                                    <i class="fas fa-blog me-2"></i>
                                    Blog Posts
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}" href="{{ route('admin.categories.index') }}">
                                    <i class="fas fa-tags me-2"></i>
                                    Categories
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.comments.*') ? 'active' : '' }}" href="{{ route('admin.comments.index') }}">
                                    <i class="fas fa-comments me-2"></i>
                                    Comments
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Business Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.products.*') || request()->routeIs('admin.services.*') || request()->routeIs('admin.programs.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#businessMenu">
                        <i class="fas fa-industry me-2"></i>
                        Business Management
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.products.*') || request()->routeIs('admin.services.*') || request()->routeIs('admin.programs.*') ? 'show' : '' }}" id="businessMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.products.*') ? 'active' : '' }}" href="{{ route('admin.products.index') }}">
                                    <i class="fas fa-boxes me-2"></i>
                                    Products
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.services.*') ? 'active' : '' }}" href="{{ route('admin.services.index') }}">
                                    <i class="fas fa-handshake me-2"></i>
                                    Services
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.programs.*') ? 'active' : '' }}" href="{{ route('admin.programs.index') }}">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Programs
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Media Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.gallery.*') || request()->routeIs('admin.files.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#mediaMenu">
                        <i class="fas fa-images me-2"></i>
                        Media Management
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.gallery.*') || request()->routeIs('admin.files.*') ? 'show' : '' }}" id="mediaMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.gallery.*') ? 'active' : '' }}" href="{{ route('admin.gallery.index') }}">
                                    <i class="fas fa-photo-video me-2"></i>
                                    Gallery
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.files.*') ? 'active' : '' }}" href="{{ route('admin.files.index') }}">
                                    <i class="fas fa-folder-open me-2"></i>
                                    File Manager
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- Website Management -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.events.*') || request()->routeIs('admin.faqs.*') || request()->routeIs('admin.testimonials.*') || request()->routeIs('admin.partners.*') || request()->routeIs('admin.contacts.*') || request()->routeIs('admin.newsletters.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#websiteMenu">
                        <i class="fas fa-globe-americas me-2"></i>
                        Website Management
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.events.*') || request()->routeIs('admin.faqs.*') || request()->routeIs('admin.testimonials.*') || request()->routeIs('admin.partners.*') || request()->routeIs('admin.contacts.*') || request()->routeIs('admin.newsletters.*') ? 'show' : '' }}" id="websiteMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.events.*') ? 'active' : '' }}" href="{{ route('admin.events.index') }}">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Events
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.faqs.*') ? 'active' : '' }}" href="{{ route('admin.faqs.index') }}">
                                    <i class="fas fa-question-circle me-2"></i>
                                    FAQs
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.testimonials.*') ? 'active' : '' }}" href="{{ route('admin.testimonials.index') }}">
                                    <i class="fas fa-quote-right me-2"></i>
                                    Testimonials
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.partners.*') ? 'active' : '' }}" href="{{ route('admin.partners.index') }}">
                                    <i class="fas fa-handshake me-2"></i>
                                    Partners
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.contacts.*') ? 'active' : '' }}" href="{{ route('admin.contacts.index') }}">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Messages
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.newsletters.*') ? 'active' : '' }}" href="{{ route('admin.newsletters.index') }}">
                                    <i class="fas fa-newspaper me-2"></i>
                                    Newsletter Subscribers
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}" href="#" role="button" data-bs-toggle="collapse" data-bs-target="#settingsMenu">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </a>
                    <div class="collapse {{ request()->routeIs('admin.settings.*') ? 'show' : '' }}" id="settingsMenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.general') ? 'active' : '' }}" href="{{ route('admin.settings.general') }}">
                                    <i class="fas fa-globe me-2"></i>
                                    General
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.seo') ? 'active' : '' }}" href="{{ route('admin.settings.seo') }}">
                                    <i class="fas fa-search me-2"></i>
                                    SEO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.contact') ? 'active' : '' }}" href="{{ route('admin.settings.contact') }}">
                                    <i class="fas fa-address-book me-2"></i>
                                    Contact
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.social') ? 'active' : '' }}" href="{{ route('admin.settings.social') }}">
                                    <i class="fas fa-share-alt me-2"></i>
                                    Social Media
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.smtp') ? 'active' : '' }}" href="{{ route('admin.settings.smtp') }}">
                                    <i class="fas fa-envelope me-2"></i>
                                    SMTP
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.recaptcha') ? 'active' : '' }}" href="{{ route('admin.settings.recaptcha') }}">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    reCAPTCHA
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings.newsletter') ? 'active' : '' }}" href="{{ route('admin.settings.newsletter') }}">
                                    <i class="fas fa-newspaper me-2"></i>
                                    Newsletter
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <button class="btn btn-link d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            @if(auth('admin')->user()->avatar)
                                <img src="{{ asset('storage/' . auth('admin')->user()->avatar) }}" alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                            @else
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            @endif
                            {{ auth('admin')->user()->name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ route('admin.admins.show', auth('admin')->id()) }}">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('admin.logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <div class="content-wrapper">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li><i class="fas fa-exclamation-circle me-2"></i>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @yield('content')
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    @stack('scripts')
</body>
</html>