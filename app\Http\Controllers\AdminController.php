<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    public function dashboard()
    {
        return view('admin.dashboard');
    }

    public function pages()
    {
        return view('admin.pages.index');
    }

    public function events()
    {
        return view('admin.events.index');
    }

    public function createEvent()
    {
        return view('admin.events.create');
    }

    public function storeEvent(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'date' => 'required|date',
            'location' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Handle file upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('events', 'public');
        }

        // Store event data (you'll need to create Event model and migration)
        // Event::create([
        //     'title' => $request->title,
        //     'description' => $request->description,
        //     'date' => $request->date,
        //     'location' => $request->location,
        //     'image' => $imagePath,
        // ]);

        return redirect()->route('admin.events')->with('success', 'Event created successfully!');
    }

    public function editEvent($id)
    {
        // $event = Event::findOrFail($id);
        return view('admin.events.edit', compact('id'));
    }

    public function updateEvent(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'date' => 'required|date',
            'location' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Update event logic here
        return redirect()->route('admin.events')->with('success', 'Event updated successfully!');
    }

    public function deleteEvent($id)
    {
        // Event::findOrFail($id)->delete();
        return redirect()->route('admin.events')->with('success', 'Event deleted successfully!');
    }

    public function blog()
    {
        return view('admin.blog.index');
    }

    public function testimonials()
    {
        return view('admin.testimonials.index');
    }

    public function programs()
    {
        return view('admin.programs.index');
    }

    public function settings()
    {
        return view('admin.settings');
    }
}