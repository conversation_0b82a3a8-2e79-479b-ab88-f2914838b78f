//>>>>> Header Top Start <<<<<//
.header-top-section {
	position: relative;
	z-index: 9;
	.header-top-wrapper {
		@include flex;
		justify-content: space-between;
		padding: 12px 0;
		.contact-list {
			@include flex;
			gap: 40px;

			li {
				a {
					font-size: 15px;
				}
				color: $white-clr;
				font-weight: 400;

				i {
					margin-right: 5px;
				}

				a {
					color: $white-clr;
					font-weight: 400;
				}
			}
		}

		.social-wrapper {
			gap: 14px;
			position: relative;
			z-index: 1;
			a {
				width: 22px;
				height: 22px;
				display: flex;
				align-items: center;
				justify-content: center;
				background: var(--white-clr);
				transition: all 0.4s;
				svg {
					transition: all 0.4s;
					stroke: $p900-clr;
					width: 8px;
				}
				i {
					transition: all 0.4s;
					font-size: 12px;
					color: var(--p900-clr);
				}
				.ani-arrow {
					transition: all 0.5s;
				}
				&:hover {
					background: $p1-clr;
					i {
						color: $white-clr;
					}
					svg {
						stroke: $white-clr;
					}
				}
			}
		}
	}
	&.style-v01 {
		background: var(--p2-clr);
		.header-top-wrapper {
			.location-area {
				color: var(--p900-clr);
				font-size: 14px;
				font-weight: 500;
				font-family: $body-font;
			}
			.contact-list {
				display: flex;
				gap: 40px;
				li {
					a {
						color: var(--p900-clr);
						font-size: 14px;
						font-weight: 500;
						font-family: $body-font;
					}
				}
			}
		}
	}
	&.topcmn-style {
		background: var(--p900-clr);
		position: relative;
		.header-top-wrapper {
			padding: 9px 0;
		}
		&::before {
			position: absolute;
			right: 0;
			top: 0;
			height: 100%;
			width: 300px;
			background: linear-gradient(
				270deg,
				#1e5322 0%,
				rgba(30, 83, 34, 0) 100%
			);
			content: "";
		}
		@include breakpoint(xl) {
			.container {
				max-width: 1530px;
				margin: 0 auto;
			}
		}
	}
}

//>>>>> Header Main Area Start <<<<<//
.header-main {
	display: flex;
	align-items: center;
	justify-content: space-between;
	.main-menu {
		ul {
			margin-bottom: 0;
			li {
				position: relative;
				list-style: none;
				display: inline-block;
				margin-inline-end: 26px;

				&:last-child {
					margin-inline-end: 0;
				}

				a {
					display: inline-block;
					font-size: 16px;
					font-weight: 500;
					color: $p700-clr;
					padding: 20px 0;
					text-align: left;
					font-family: $body-font;
					position: relative;
					text-transform: capitalize;
					@include transition;

					i {
						margin-left: 2px;
						font-size: 16px;
					}

					&:hover {
						color: $p1-clr !important;
					}
				}
				.submenu {
					position: absolute;
					top: 100%;
					inset-inline-start: 0;
					min-width: 240px;
					background: $white-clr;
					z-index: 99999;
					visibility: hidden;
					opacity: 0;
					transform-origin: top center;
					color: $p700-clr;
					box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					-webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					-moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
					transform: translateY(10px);
					@include transition;

					li {
						display: block;
						width: 100%;
						margin: 0;
						padding: 0;

						a {
							position: relative;
							z-index: 11;
							font-size: 16px;
							font-weight: 600;
							color: $p700-clr;
							padding: 0 25px;
							padding-bottom: 11px;
							padding-top: 11px;
							width: 100%;
							border-bottom: 1px solid #eeeeee;
						}
						&:last-child {
							a {
								border: none;
							}
						}
						.submenu {
							inset-inline-start: 100%;
							top: 0;
							visibility: hidden;
							opacity: 0;
						}
						&:hover {
							> a {
								background: $p900-clr;
								color: $white-clr !important;

								&::after {
									color: $p2-clr;
								}
							}
							> .submenu {
								-webkit-transform: translateY(1);
								-moz-transform: translateY(1);
								-ms-transform: translateY(1);
								-o-transform: translateY(1);
								transform: translateY(1);
								visibility: visible;
								opacity: 1;
							}
						}
					}
					li.has-dropdown {
						> a {
							&::after {
								position: absolute;
								top: 50%;
								inset-inline-end: 25px;
								-webkit-transform: translateY(-50%);
								-moz-transform: translateY(-50%);
								-ms-transform: translateY(-50%);
								-o-transform: translateY(-50%);
								transform: translateY(-50%);
								color: $p2-clr;
							}
						}
					}
				}

				.has-homemenu {
					width: 800px;
					padding: 30px 30px 10px 30px;
					opacity: 0;
					left: -250px;
					visibility: hidden;
					padding: 20px 20px 20px 20px;

					.homemenu-items {
						@include flex;
						gap: 18px;
						justify-content: space-between;
						@include breakpoint(max-lg) {
							flex-wrap: wrap;
						}
						.homemenu {
							position: relative;
							box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
							width: 32%;
							.homemenu-thumb {
								position: relative;
								width: 100%;
								.demo-button {
									position: absolute;
									top: 50%;
									left: 50%;
									transform: translate(-50%, -50%);
									width: 60%;
									gap: 10px;
									display: flex;
									justify-content: center;
									flex-direction: column;
									opacity: 0;
									visibility: hidden;
									@include transition;
									margin-top: 20px;
									.theme-btn {
										padding: 14px 20px;
										color: $white-clr !important;
										width: initial;
										font-size: 14px;
										text-align: center;
										border-radius: 0;

										&:hover {
											color: $white-clr !important;
										}
									}
								}
								&::before {
									background: -webkit-gradient(
										linear,
										left top,
										left bottom,
										from(rgba(20, 19, 19, 0)),
										to(#5e5ef6)
									);
									background: linear-gradient(
										to bottom,
										rgba(99, 92, 92, 0) 0%,
										#252527 100%
									);
									background-repeat: no-repeat;
									background-size: cover;
									background-position: center;
									width: 100%;
									height: 100%;
									position: absolute;
									left: 0;
									top: 0;
									overflow: hidden;
									opacity: 0;
									-webkit-transition: all 0.3s ease-in-out;
									transition: all 0.3s ease-in-out;
									content: "";
								}
								&:hover {
									&::before {
										visibility: visible;
										opacity: 1;
									}

									.demo-button {
										opacity: 1;
										visibility: visible;
										margin-top: 0;
									}
									& .homemenu-btn {
										opacity: 1;
										visibility: visible;
										bottom: 50%;
										transform: translateY(50%);
									}
								}
								img {
									width: 100%;
								}
							}

							.homemenu-title {
								text-align: center;
								margin: 15px auto;
								display: inline-block;
								font-size: 16px;
							}
						}
					}
				}

				&:hover {
					> a {
						color: $p2-clr;
						&::after {
							color: $p2-clr;
						}
					}
					> .submenu {
						visibility: visible;
						opacity: 1;
						transform: translateY(0px);
					}
				}
			}
		}
	}
	.sidebar__toggle {
		cursor: pointer;
		font-size: 39px;
		color: $p700-clr;
		@include breakpoint(max-sm) {
			font-size: 30px;
		}
	}
}

//Header Style V01
.header-1 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1;
	padding: 28px 0;
	.header-main {
		.header-right {
			.shop-adjust {
				display: flex;
				align-items: center;
				gap: 18px;
				.shop-bar {
					border: 1px solid var(--white-clr);
					border-radius: 100px;
					padding: 10px 11px 10px;
					display: flex;
					align-items: center;
					gap: 20px;
					.cart{
						&:hover{
							cursor: pointer;
						}
					}
					i {
						font-size: 28px;
					}
					.serial-count {
						width: 17px;
						height: 17px;
						border-radius: 50%;
						background: var(--p1-clr);
						font-size: 10px;
						font-family: $heading-font;
						position: absolute;
						top: -4px;
						right: -8px;
					}
					font-size: 14px;
					color: var(--white-clr);
					line-height: 18px;
					font-family: $body-font;
				}
				.cmn-btn {
					height: 60px;
				}
			}
			ul {
				li {
					a {
						color: var(--white-clr);
					}
				}
			}
			gap: 40px;
		}
	}
	.sidebar__toggle {
		i {
			color: var(--white-clr);
		}
	}
	@include breakpoint(max-xxl) {
		.header-main {
			.header-right {
				gap: 10px;
				.shop-adjust {
					gap: 10px;
					.shop-bar {
						padding: 8px 11px 8px;
						display: flex;
						align-items: center;
						gap: 20px;
						i {
							font-size: 22px;
						}
						font-size: 12px;
						line-height: 16px;
					}
					.cmn-btn {
						height: 49px;
						padding: 8px 16px;
						font-size: 13px;
					}
				}
			}
			.main-menu ul li {
				margin-inline-end: 20px;
			}
		}
	}
	@include breakpoint(max-xl) {
		.header-main {
			.header-right {
				gap: 10px;
			}
		}
	}
	@include breakpoint(max-md) {
		.header-main {
			.header-right {
				.shop-adjust {
					.cmn-btn {
						padding: 12px 14px;
						height: 42px;
						font-size: 12px;
						font-weight: 500;
					}
				}
			}
		}
	}
	@include breakpoint(max-xs) {
		.header-main {
			.header-right {
				.shop-adjust {
					display: none;
				}
			}
		}
	}
	@include breakpoint(xl) {
		.container {
			max-width: 1450px;
			margin: 0 auto;
		}
	}
}
.header-1.sticky {
	background: var(--p900-clr);
	padding: 10px 0;
}
//Header Style v02
.header-2 {
	padding: 16px 0;

	.header-right {
		gap: 40px;
	}
	.search-adjust {
		display: flex;
		align-items: center;
		gap: 16px;
		.search-trigger {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			background: rgba(42, 185, 57, 0.16);
			i {
				color: var(--p1-clr);
				font-size: 18px;
			}
		}
	}
	@include breakpoint(max-xxl) {
		.header-right {
			gap: 20px;
			.search-adjust {
				.cmn-btn {
					padding: 16px 18px;
					font-size: 13px;
					font-weight: 500;
				}
				.search-trigger {
					width: 38px;
					height: 38px;
					i {
						font-size: 15px;
					}
				}
			}
		}
		.header-main .main-menu ul li {
			margin-inline-end: 18px;
		}
	}
	@include breakpoint(max-xl) {
		.header-right {
			gap: 20px;
		}
	}
	@include breakpoint(max-lg) {
		.search-adjust {
			.cmn-btn {
				padding: 12px 18px;
				font-size: 13px;
				font-weight: 500;
			}
			.search-trigger {
				width: 38px;
				height: 38px;
				i {
					font-size: 15px;
				}
			}
		}
	}
}
//Header Style v03
.header-3 {
	z-index: 9;
	@include breakpoint(xl) {
		.container {
			max-width: 1314px;
		}
	}
}
//Header Style v04
.header-common-adjustment {
	width: 100%;
	display: flex;
	align-items: center;

	.logo-v04 {
		background: $p1-clr;
		width: 250px;
		height: 138px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.topheader-mainheader {
		width: calc(100% - 250px);
	}
	@media screen and (max-width: 1350px) {
		.logo-v04 {
			width: 160px;
			height: 138px;
			img {
				width: 120px;
			}
		}
		.topheader-mainheader {
			width: calc(100% - 160px);
		}
	}
	@media screen and (max-width: 1199px) {
		.logo-v04 {
			display: none;
		}
		.topheader-mainheader {
			width: calc(100%);
		}
	}
}
.header-v4 {
	padding: 11px 0;
	@include breakpoint(xl) {
		.container {
			max-width: 1530px;
			margin: 0 auto;
		}
	}
	.header-main {
		.main-menu ul li .has-homemenu {
			left: 0;
		}
		.common-adjust-toggle {
			display: flex;
			align-items: center;
			gap: 35px;
			.header-help {
				display: flex;
				align-items: center;
				gap: 15px;
				span {
					.need {
						font-size: 16px;
						font-weight: 400;
						color: $p800-clr;
						line-height: 30px;
						font-family: $body-font;
					}
					.call {
						font-size: 20px;
						font-weight: 400;
						font-family: $heading-font !important;
						color: $p900-clr;
						display: block;
					}
				}
			}
			.sidebar__toggle {
				width: 42px;
				height: 42px;
				border-radius: 100%;
				border: 1px solid $p1-clr;
			}
		}
	}
	@include breakpoint(max-sm) {
		.header-main {
			.common-adjust-toggle {
				.header-help {
					display: none;
				}
			}
		}
	}
}

.sidebar__toggle {
	cursor: pointer;
}
//>>>>> Sticky Start <<<<</
.sticky {
	position: fixed !important;
	top: 0 !important;
	left: 0;
	width: 100%;
	z-index: 100;
	transition: all 0.9s;
	background-color: $white-clr;
	box-shadow: $shadow-clr;
	-webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
}

//>>>>> Offcanvas Start <<<<<//
.offcanvas__info {
	position: fixed;
	right: 0;
	top: 0;
	width: 400px;
	height: 100%;
	-webkit-transform: translateX(calc(100% + 80px));
	-moz-transform: translateX(calc(100% + 80px));
	-ms-transform: translateX(calc(100% + 80px));
	-o-transform: translateX(calc(100% + 80px));
	transform: translateX(calc(100% + 80px));
	-webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	-moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
	z-index: 99999;
	overflow-y: scroll;
	overscroll-behavior-y: contain;
	scrollbar-width: none;
	background: $white-clr;
	&::-webkit-scrollbar {
		display: none;
	}
}
.offcanvas__info.info-open {
	opacity: 1;
	-webkit-transform: translateX(0);
	-moz-transform: translateX(0);
	-ms-transform: translateX(0);
	-o-transform: translateX(0);
	transform: translateX(0);
	background: $white-clr;
}
.offcanvas__wrapper {
	position: relative;
	height: 100%;
	padding: 30px 30px;
	.offcanvas__content {
		.text {
			color: $p200-clr;
		}
		.offcanvas__close {
			width: 40px;
			height: 40px;
			line-height: 40px;
			text-align: center;
			border-radius: 4px;
			background-color: $p1-clr;
			position: relative;
			z-index: 9;
			cursor: pointer;

			i {
				color: $white-clr;
			}
		}
		.offcanvas__contact {
			margin-top: 35px;
			ul {
				margin-top: 20px;

				li {
					font-size: 13px;
					font-weight: 600;
					text-transform: capitalize;

					a {
						color: $p800-clr;
						font-size: 13px;
					}

					&:not(:last-child) {
						margin-bottom: 10px;
					}

					.offcanvas__contact-icon {
						margin-right: 20px;

						i {
							color: $p800-clr;
							font-size: 16px;
						}
					}
				}
			}

			span {
				text-transform: initial;
			}

			.social-icon {
				margin-top: 30px;
				gap: 10px;

				a {
					width: 36px;
					height: 36px;
					line-height: 36px;
					text-align: center;
					font-size: 16px;
					display: block;
					background: $white-clr;
					color: $p700-clr;
					border-radius: 4px;
					-webkit-transition: all 0.4s ease-in-out;
					transition: all 0.4s ease-in-out;
					text-align: center;
					border: 1px solid $p1-clr;

					&:hover {
						border-color: $p2-clr;
						background-color: $p2-clr;
						color: $white-clr;
					}
				}
			}
		}
	}
}

.offcanvas__overlay {
	position: fixed;
	height: 100%;
	width: 100%;
	background: #151515;
	z-index: 900;
	top: 0;
	opacity: 0;
	visibility: hidden;
	right: 0;
}

.offcanvas__overlay.overlay-open {
	opacity: 0.8;
	visibility: visible;
}

@media (max-width: 450px) {
	.offcanvas__info {
		width: 300px;
	}
}

@media (max-width: 575px) {
	.offcanvas__wrapper {
		padding: 20px;
	}
}
