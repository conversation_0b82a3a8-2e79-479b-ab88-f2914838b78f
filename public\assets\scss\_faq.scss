.faq-section {
	position: relative;
	z-index: 1;
	.faq-element {
		position: absolute;
		left: 0;
		bottom: 0;
		z-index: -1;
	}
	.faq-content-left {
		h2 {
			margin-bottom: 20px;
			max-width: 462px;
		}
		p {
			color: $p800-clr;
			margin-bottom: 30px;
		}
		.faq-watch {
			border-radius: 20px;
			background: $white-clr;
			display: inline-flex;
			align-items: center;
			padding: 10px 40px;
			gap: 15px;
			margin-left: 20px;
			.video-cmn {
				width: 50px;
				height: 50px;
				border-radius: 50%;
				background: $p1-clr;
				position: relative;
				margin-left: -61px;
				z-index: 1;
				&::before {
					position: absolute;
					left: 0px;
					top: 0px;
					border-radius: 50%;
					width: 50px;
					height: 50px;
					background: $p1-clr;
					content: "";
					animation: ropple 2s linear infinite;
					z-index: -1;
				}
				i {
					color: $white-clr;
					font-size: 18px;
				}
			}
			h5 {
				font-size: 20px;
				color: $p900-clr;
				font-weight: 500;
				line-height: 30px;
			}
		}
	}
	@include breakpoint(max-xxxl) {
		.faq-element {
			left: 0;
			bottom: 0;
			width: 190px;
		}
	}
	@include breakpoint(max-xl) {
		.faq-content-left {
			h2 {
				margin-bottom: 14px;
				max-width: 462px;
			}
			p {
				margin-bottom: 24px;
			}
			.faq-watch {
				border-radius: 20px;
				padding: 10px 20px;
				gap: 12px;
				h5 {
					font-size: 16px;
					line-height: 19px;
				}
			}
		}
	}
	@include breakpoint(max-lg) {
		.faq-element {
			display: none;
		}
		.faq-content-left {
			.faq-watch {
				margin-left: 44px;
			}
		}
	}
}
@keyframes smzom {
	50% {
		transform: scale(1.04);
	}
}

.faq {
	.accordion-single {
		transition: all 0.4s;
		border-radius: 20px;
		padding: 20px 26px;
		font-weight: 400;
		background: $white-clr;
		box-shadow: 0px 0px 60px 0px rgba(0, 0, 0, 0.05);
	}
	.header-area {
		cursor: pointer;
		button {
			font-size: 20px;
			font-weight: 400;
			font-family: $heading-font;
			color: $p900-clr;
			padding-right: 40px;
			text-align: start;
			&::after {
				position: absolute;
				z-index: 1;
				right: 0;
				border-radius: 50%;
				font-size: 24px;
				transform: rotate(0deg);
				transition: all 0.5s;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: "Font Awesome 5 Free";
				content: "\2b";
				font-weight: 900;
				width: 40px;
				min-width: 40px;
				height: 40px;
				background: $p100-clr;
				color: $p1-clr;
			}
		}
	}
	.active {
		background: $p1-clr;
		.header-area {
			button {
				color: $white-clr;
				&::after {
					position: absolute;
					z-index: 1;
					right: 0;
					border-radius: 50%;
					font-size: 24px;
					transform: rotate(0deg);
					transition: all 0.5s;
					background: rgba(255, 255, 255, 0.1) !important;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: "Font Awesome 5 Free";
					content: "-" !important;
					font-weight: 900;
					width: 40px;
					min-width: 40px;
					height: 40px;
					background: $p2-clr;
					color: $white-clr;
				}
			}
		}
	}
	.content-area {
		display: none;
		padding-top: 18px;
		p {
			font-size: 14px;
			font-family: $body;
			color: $white-clr;
		}
	}
	@include breakpoint(max-xl) {
		.accordion-single {
			padding: 15px 15px;
		}
		.header-area {
			cursor: pointer;
			button {
				font-size: 17px;
				padding-right: 50px;
				text-align: start;
				&::after {
					width: 34px;
					min-width: 34px;
					height: 34px;
				}
			}
		}
	}
}
