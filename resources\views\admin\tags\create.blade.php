@extends('layouts.admin')

@section('title', 'Create Tag')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Create New Tag</h3>
                    <a href="{{ route('admin.tags.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tags
                    </a>
                </div>
                <form action="{{ route('admin.tags.store') }}" method="POST">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="name" class="required">Tag Name</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" 
                                                   placeholder="Enter tag name" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                The name of the tag as it will appear on the website.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="slug">Slug</label>
                                            <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                                   id="slug" name="slug" value="{{ old('slug') }}" 
                                                   placeholder="Auto-generated from name">
                                            @error('slug')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                URL-friendly version of the name. Leave blank to auto-generate.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="description">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" 
                                                      placeholder="Enter tag description (optional)">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Optional description for the tag.
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO Settings -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">SEO Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="meta_title">Meta Title</label>
                                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                                   id="meta_title" name="meta_title" value="{{ old('meta_title') }}" 
                                                   placeholder="Enter meta title (optional)" maxlength="60">
                                            @error('meta_title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <span id="meta_title_count">0</span>/60 characters. Leave blank to use tag name.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="meta_description">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                                      id="meta_description" name="meta_description" rows="3" 
                                                      placeholder="Enter meta description (optional)" maxlength="160">{{ old('meta_description') }}</textarea>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <span id="meta_description_count">0</span>/160 characters. Leave blank to use tag description.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publishing Options -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Publishing Options</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror" 
                                                    id="status" name="status" required>
                                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Only active tags will be visible on the website.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <label for="sort_order">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                                                   min="0" placeholder="0">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                Lower numbers appear first. Default is 0.
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tag Preview -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Preview</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="tag-preview">
                                            <h6 id="preview_name" class="text-primary">Tag Name</h6>
                                            <p id="preview_description" class="text-muted small mb-2">Tag description will appear here...</p>
                                            <code id="preview_slug">tag-slug</code>
                                        </div>
                                    </div>
                                </div>

                                <!-- Help -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Help</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small text-muted mb-0">
                                            <li><i class="fas fa-info-circle text-info"></i> Tags help organize and categorize your posts</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Use descriptive names for better SEO</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Slug is used in URLs, keep it short and meaningful</li>
                                            <li><i class="fas fa-info-circle text-info"></i> Meta information helps with search engine optimization</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Tag
                                </button>
                                <button type="submit" name="action" value="save_and_new" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Save & Create New
                                </button>
                            </div>
                            <div class="col-md-6 text-right">
                                <a href="{{ route('admin.tags.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: ' *';
    color: red;
}

.tag-preview {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
}

.tag-preview h6 {
    margin-bottom: 0.5rem;
}

.tag-preview code {
    font-size: 0.875rem;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        const name = $(this).val();
        const slug = name.toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
        
        $('#slug').val(slug);
        updatePreview();
    });

    // Update preview when fields change
    $('#name, #description, #slug').on('input', updatePreview);

    // Auto-fill meta title from name
    $('#name').on('input', function() {
        const name = $(this).val();
        if (name && !$('#meta_title').val()) {
            $('#meta_title').val(name);
            updateCharCount('#meta_title', '#meta_title_count');
        }
    });

    // Auto-fill meta description from description
    $('#description').on('input', function() {
        const description = $(this).val();
        if (description && !$('#meta_description').val()) {
            $('#meta_description').val(description.substring(0, 160));
            updateCharCount('#meta_description', '#meta_description_count');
        }
    });

    // Character count for meta fields
    $('#meta_title').on('input', function() {
        updateCharCount(this, '#meta_title_count');
    });

    $('#meta_description').on('input', function() {
        updateCharCount(this, '#meta_description_count');
    });

    // Initialize character counts
    updateCharCount('#meta_title', '#meta_title_count');
    updateCharCount('#meta_description', '#meta_description_count');
    updatePreview();

    function updateCharCount(input, counter) {
        const length = $(input).val().length;
        $(counter).text(length);
        
        // Add warning color if approaching limit
        const maxLength = $(input).attr('maxlength');
        if (length > maxLength * 0.8) {
            $(counter).addClass('text-warning');
        } else {
            $(counter).removeClass('text-warning');
        }
        
        if (length >= maxLength) {
            $(counter).addClass('text-danger').removeClass('text-warning');
        } else {
            $(counter).removeClass('text-danger');
        }
    }

    function updatePreview() {
        const name = $('#name').val() || 'Tag Name';
        const description = $('#description').val() || 'Tag description will appear here...';
        const slug = $('#slug').val() || 'tag-slug';
        
        $('#preview_name').text(name);
        $('#preview_description').text(description);
        $('#preview_slug').text(slug);
    }
});

// Form validation
$('form').on('submit', function(e) {
    const name = $('#name').val().trim();
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a tag name.');
        $('#name').focus();
        return false;
    }
    
    // Show loading state
    $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating...');
});
</script>
@endpush