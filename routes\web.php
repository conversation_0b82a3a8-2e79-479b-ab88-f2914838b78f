<?php

use Illuminate\Support\Facades\Route;

// Home page
Route::get('/', function () {
    return view('home');
})->name('home');

// About routes
Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/philosophy', function () {
    return view('about.philosophy');
})->name('philosophy');

Route::get('/team', function () {
    return view('about.team');
})->name('team');

// Products routes
Route::get('/products', function () {
    return view('products.index');
})->name('products.index');

Route::get('/products/organic', function () {
    return view('products.organic');
})->name('products.organic');

Route::get('/products/inorganic', function () {
    return view('products.inorganic');
})->name('products.inorganic');

Route::get('/products/bio-stimulants', function () {
    return view('products.bio-stimulants');
})->name('products.bio-stimulants');

Route::get('/products/bio-pesticides', function () {
    return view('products.bio-pesticides');
})->name('products.bio-pesticides');

// Services routes
Route::get('/services', function () {
    return view('services.index');
})->name('services.index');

Route::get('/services/training', function () {
    return view('services.training');
})->name('services.training');

Route::get('/services/soil-analysis', function () {
    return view('services.soil-analysis');
})->name('services.soil-analysis');

Route::get('/services/gap-training', function () {
    return view('services.gap-training');
})->name('services.gap-training');

Route::get('/services/certification', function () {
    return view('services.certification');
})->name('services.certification');

// Projects & Impact routes
Route::get('/projects', function () {
    return view('projects.index');
})->name('projects.index');

Route::get('/projects/past-performance', function () {
    return view('projects.past-performance');
})->name('projects.past-performance');

Route::get('/projects/impact-stories', function () {
    return view('projects.impact-stories');
})->name('projects.impact-stories');

// Certifications routes
Route::get('/certifications', function () {
    return view('certifications.index');
})->name('certifications.index');

// Resources routes
Route::get('/blog', function () {
    return view('blog.index');
})->name('blog');

Route::get('/blog/details', function () {
    return view('blog.details');
})->name('blog.details');

Route::get('/testimonials', function () {
    return view('testimonials');
})->name('testimonials');

// Contact
Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// Terms and Privacy
Route::get('/terms', function () {
    return view('terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('privacy');
})->name('privacy');

// Additional Agricultural Routes for backward compatibility
Route::get('/organic-fertilizers', function () {
    return view('products.organic');
})->name('organic-fertilizers');

Route::get('/inorganic-fertilizers', function () {
    return view('products.inorganic');
})->name('inorganic-fertilizers');

Route::get('/farmer-training', function () {
    return view('services.training');
})->name('farmer-training');

Route::get('/soil-testing', function () {
    return view('services.soil-analysis');
})->name('soil-testing');

Route::get('/agricultural-consulting', function () {
    return view('services.consulting');
})->name('agricultural-consulting');

// Settings Demo (for development/testing)
Route::get('/settings-demo', function () {
    return view('settings-demo');
})->name('settings-demo');

// FAQ and Support
Route::get('/faq', function () {
    return view('faq');
})->name('faq');

Route::get('/blog-list', function () {
    return view('blog.list');
})->name('blog-list');

Route::get('/blog-grid', function () {
    return view('blog.grid');
})->name('blog-grid');

Route::get('/blog-grid-two', function () {
    return view('blog.grid-two');
})->name('blog-grid-two');

Route::get('/blog-grid-four', function () {
    return view('blog.grid-four');
})->name('blog-grid-four');

Route::get('/blog-masonry', function () {
    return view('blog.masonry');
})->name('blog-masonry');

// Authentication routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');

Route::get('/register', function () {
    return view('auth.register');
})->name('register');

// Admin CMS Routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/pages', [\App\Http\Controllers\AdminController::class, 'pages'])->name('pages');
    
    // Events Management
    Route::get('/events', [\App\Http\Controllers\AdminController::class, 'events'])->name('events');
    Route::get('/events/create', [\App\Http\Controllers\AdminController::class, 'createEvent'])->name('events.create');
    Route::post('/events', [\App\Http\Controllers\AdminController::class, 'storeEvent'])->name('events.store');
    Route::get('/events/{id}/edit', [\App\Http\Controllers\AdminController::class, 'editEvent'])->name('events.edit');
    Route::put('/events/{id}', [\App\Http\Controllers\AdminController::class, 'updateEvent'])->name('events.update');
    Route::delete('/events/{id}', [\App\Http\Controllers\AdminController::class, 'deleteEvent'])->name('events.delete');
    
    // Blog Management
    Route::get('/blog', [\App\Http\Controllers\AdminController::class, 'blog'])->name('blog');
    
    // Testimonials Management
    Route::get('/testimonials', [\App\Http\Controllers\AdminController::class, 'testimonials'])->name('testimonials');
    
    // Programs Management
    Route::get('/programs', [\App\Http\Controllers\AdminController::class, 'programs'])->name('programs');
    
    // Settings
    Route::get('/settings', [\App\Http\Controllers\AdminController::class, 'settings'])->name('settings');
});

// Test route to check seeded data
Route::get('/test-data', function () {
    $data = [
        'settings' => \App\Models\Setting::count(),
        'categories' => \App\Models\Category::count(),
        'posts' => \App\Models\Post::count(),
        'products' => \App\Models\Product::count(),
        'services' => \App\Models\Service::count(),
        'faqs' => \App\Models\Faq::count(),
        'galleries' => \App\Models\Gallery::count(),
    ];

    return response()->json($data);
});

// Test route to check admin routes
Route::get('/test-admin-routes', function () {
    $routes = [
        'Dashboard' => route('admin.dashboard'),
        'Settings' => route('admin.settings.index'),
        'Posts' => route('admin.posts.index'),
        'Categories' => route('admin.categories.index'),
        'Comments' => route('admin.comments.index'),
        'Products' => route('admin.products.index'),
        'Services' => route('admin.services.index'),
        'Gallery' => route('admin.gallery.index'),
        'Files' => route('admin.files.index'),
    ];

    return response()->json($routes);
});
