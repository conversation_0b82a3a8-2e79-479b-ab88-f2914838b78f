<?php

use Illuminate\Support\Facades\Route;

// Home page
Route::get('/', function () {
    return view('home');
})->name('home');

// About routes
Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/philosophy', function () {
    return view('about.philosophy');
})->name('philosophy');

Route::get('/team', function () {
    return view('about.team');
})->name('team');

// Programs routes
Route::get('/programs', function () {
    return view('programs.index');
})->name('programs.index');

// Alias route for backward compatibility
Route::get('/programs-alt', function () {
    return view('programs.index');
})->name('programs');

Route::get('/programs/leadership', function () {
    return view('programs.leadership');
})->name('programs.leadership');

Route::get('/programs/relationship', function () {
    return view('programs.relationship');
})->name('programs.relationship');

Route::get('/programs/family', function () {
    return view('programs.family');
})->name('programs.family');

Route::get('/programs/fatherhood', function () {
    return view('programs.fatherhood');
})->name('programs.fatherhood');

Route::get('/programs/education', function () {
    return view('programs.education');
})->name('programs.education');

Route::get('/programs/outreach', function () {
    return view('programs.outreach');
})->name('programs.outreach');

// Resources routes
Route::get('/blog', function () {
    return view('blog.index');
})->name('blog');

Route::get('/blog/details', function () {
    return view('blog.details');
})->name('blog.details');

Route::get('/blog-details', function () {
    return view('blog.details');
})->name('blog-details');

Route::get('/testimonials', function () {
    return view('testimonials');
})->name('testimonials');

Route::get('/events', function () {
    return view('events');
})->name('events');

// Enrollment and Partnership
Route::get('/enrollment', function () {
    return view('enrollment');
})->name('enrollment');

Route::get('/partnership', function () {
    return view('partnership');
})->name('partnership');

// Contact
Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// Impact Areas
Route::get('/impact/leadership', function () {
    return view('impact.leadership');
})->name('impact.leadership');

Route::get('/impact/family', function () {
    return view('impact.family');
})->name('impact.family');

Route::get('/impact/community', function () {
    return view('impact.community');
})->name('impact.community');

Route::get('/impact/education', function () {
    return view('impact.education');
})->name('impact.education');

Route::get('/impact/youth', function () {
    return view('impact.youth');
})->name('impact.youth');

Route::get('/impact/women', function () {
    return view('impact.women');
})->name('impact.women');

// Additional Program Routes
Route::get('/leadership-development', function () {
    return view('programs.leadership-development');
})->name('leadership-development');

Route::get('/relationship-education', function () {
    return view('programs.relationship-education');
})->name('relationship-education');

Route::get('/manhood-fatherhood', function () {
    return view('programs.manhood-fatherhood');
})->name('manhood-fatherhood');

Route::get('/values-education', function () {
    return view('programs.values-education');
})->name('values-education');

Route::get('/character-development', function () {
    return view('programs.character-development');
})->name('character-development');

Route::get('/community-outreach', function () {
    return view('programs.community-outreach');
})->name('community-outreach');

Route::get('/youth-programs', function () {
    return view('programs.youth-programs');
})->name('youth-programs');

Route::get('/family-life', function () {
    return view('programs.family-life');
})->name('family-life');

Route::get('/mentorship', function () {
    return view('programs.mentorship');
})->name('mentorship');

Route::get('/workshops', function () {
    return view('programs.workshops');
})->name('workshops');

Route::get('/certification', function () {
    return view('programs.certification');
})->name('certification');

Route::get('/apply', function () {
    return view('programs.apply');
})->name('apply');

Route::get('/scholarships', function () {
    return view('programs.scholarships');
})->name('scholarships');

Route::get('/success-stories', function () {
    return view('programs.success-stories');
})->name('success-stories');

Route::get('/faq', function () {
    return view('programs.faq');
})->name('faq');

Route::get('/program-calendar', function () {
    return view('programs.program-calendar');
})->name('program-calendar');

Route::get('/impact-areas', function () {
    return view('programs.impact-areas');
})->name('impact-areas');

Route::get('/community-partnerships', function () {
    return view('programs.community-partnerships');
})->name('community-partnerships');

Route::get('/consultation', function () {
    return view('programs.consultation');
})->name('consultation');

// Missing routes from header and footer
Route::get('/mentorship-programs', function () {
    return view('programs.mentorship-programs');
})->name('mentorship-programs');

Route::get('/workshops-seminars', function () {
    return view('programs.workshops-seminars');
})->name('workshops-seminars');

Route::get('/education-impact', function () {
    return view('impact.education-impact');
})->name('education-impact');

// Settings Demo (for development/testing)
Route::get('/settings-demo', function () {
    return view('settings-demo');
})->name('settings-demo');

Route::get('/community-development', function () {
    return view('impact.community-development');
})->name('community-development');

Route::get('/family-strengthening', function () {
    return view('impact.family-strengthening');
})->name('family-strengthening');

Route::get('/youth-empowerment', function () {
    return view('impact.youth-empowerment');
})->name('youth-empowerment');

Route::get('/leadership-training', function () {
    return view('impact.leadership-training');
})->name('leadership-training');

Route::get('/character-building', function () {
    return view('impact.character-building');
})->name('character-building');

Route::get('/careers', function () {
    return view('careers');
})->name('careers');

Route::get('/partners', function () {
    return view('partners');
})->name('partners');

Route::get('/terms', function () {
    return view('terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('privacy');
})->name('privacy');

Route::get('/mission-vision', function () {
    return view('about.mission-vision');
})->name('mission-vision');

Route::get('/leadership-team', function () {
    return view('about.leadership-team');
})->name('leadership-team');

Route::get('/core-values', function () {
    return view('about.core-values');
})->name('core-values');

Route::get('/impact-stories', function () {
    return view('impact-stories');
})->name('impact-stories');

Route::get('/why-choose-us', function () {
    return view('about.why-choose-us');
})->name('why-choose-us');

Route::get('/awards', function () {
    return view('awards');
})->name('awards');

Route::get('/blog-list', function () {
    return view('blog.list');
})->name('blog-list');

Route::get('/blog-grid', function () {
    return view('blog.grid');
})->name('blog-grid');

Route::get('/blog-grid-two', function () {
    return view('blog.grid-two');
})->name('blog-grid-two');

Route::get('/blog-grid-four', function () {
    return view('blog.grid-four');
})->name('blog-grid-four');

Route::get('/blog-masonry', function () {
    return view('blog.masonry');
})->name('blog-masonry');

// Authentication routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');

Route::get('/register', function () {
    return view('auth.register');
})->name('register');

// Admin CMS Routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/pages', [\App\Http\Controllers\AdminController::class, 'pages'])->name('pages');
    
    // Events Management
    Route::get('/events', [\App\Http\Controllers\AdminController::class, 'events'])->name('events');
    Route::get('/events/create', [\App\Http\Controllers\AdminController::class, 'createEvent'])->name('events.create');
    Route::post('/events', [\App\Http\Controllers\AdminController::class, 'storeEvent'])->name('events.store');
    Route::get('/events/{id}/edit', [\App\Http\Controllers\AdminController::class, 'editEvent'])->name('events.edit');
    Route::put('/events/{id}', [\App\Http\Controllers\AdminController::class, 'updateEvent'])->name('events.update');
    Route::delete('/events/{id}', [\App\Http\Controllers\AdminController::class, 'deleteEvent'])->name('events.delete');
    
    // Blog Management
    Route::get('/blog', [\App\Http\Controllers\AdminController::class, 'blog'])->name('blog');
    
    // Testimonials Management
    Route::get('/testimonials', [\App\Http\Controllers\AdminController::class, 'testimonials'])->name('testimonials');
    
    // Programs Management
    Route::get('/programs', [\App\Http\Controllers\AdminController::class, 'programs'])->name('programs');
    
    // Settings
    Route::get('/settings', [\App\Http\Controllers\AdminController::class, 'settings'])->name('settings');
});
