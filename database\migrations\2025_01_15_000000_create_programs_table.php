<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->string('image')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->string('duration')->nullable(); // e.g., "6 weeks", "3 months"
            $table->integer('max_participants')->nullable();
            $table->json('schedule')->nullable(); // flexible schedule data
            $table->string('level')->nullable(); // beginner, intermediate, advanced
            $table->text('requirements')->nullable();
            $table->text('objectives')->nullable();
            $table->string('instructor')->nullable();
            $table->string('location')->nullable();
            $table->boolean('is_online')->default(false);
            $table->boolean('is_active')->default(true);
            $table->string('status')->default('upcoming'); // upcoming, ongoing, completed, cancelled
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->unsignedBigInteger('event_id')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'is_active']);
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};