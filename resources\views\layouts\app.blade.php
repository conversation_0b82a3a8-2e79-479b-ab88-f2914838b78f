<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="@yield('description', setting('site_description', 'PESCOT - Center for Kingdom and Leadership Studies - Raising Values, Building Leaders, Strengthening Nations'))">
    <meta name="keywords" content="@yield('keywords', setting('seo_keywords', 'PESCOT, leadership, kingdom studies, education'))">
    <meta name="author" content="{{ setting('site_name', 'PESCOT') }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', setting('site_name', 'PESCOT'))">
    <meta property="og:description" content="@yield('og_description', setting('site_description', 'PESCOT - Center for Kingdom and Leadership Studies'))">
    <meta property="og:image" content="@yield('og_image', setting('site_logo') ? asset('storage/' . setting('site_logo')) : asset('assets/images/cklsfavicon.png'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', setting('site_name', 'PESCOT'))">
    <meta name="twitter:description" content="@yield('twitter_description', setting('site_description', 'PESCOT - Center for Kingdom and Leadership Studies'))">
    <meta name="twitter:image" content="@yield('twitter_image', setting('site_logo') ? asset('storage/' . setting('site_logo')) : asset('assets/images/cklsfavicon.png'))">
    @if(setting('social_twitter'))
    <meta name="twitter:site" content="{{ setting('social_twitter') }}">
    @endif

    <link rel="shortcut icon" type="image/x-icon" href="{{ setting('site_favicon') ? asset('storage/' . setting('site_favicon')) : asset('assets/images/logo/favicon.png') }}">
    <title>@yield('title', setting('site_name', 'PESCOT') . ' - ' . setting('site_tagline', 'Center for Kingdom and Leadership Studies'))</title>
    <link rel="stylesheet preload" href="{{ asset('assets/css/vendor/bootstrap.min.css') }}" as="style" media="print"
        onload="this.media='all'">
    <link rel="stylesheet preload" href="{{ asset('assets/css/plugins/fontawesome.css') }}" as="style" media="print"
        onload="this.media='all'">
    <link rel="stylesheet preload" href="{{ asset('assets/css/plugins/magnifying-popup.css') }}" as="style" media="print"
        onload="this.media='all'">
    <link rel="stylesheet preload" href="{{ asset('assets/css/plugins/swiper.css') }}" as="style" media="print"
        onload="this.media='all'">
    <link rel="stylesheet preload" href="{{ asset('assets/css/plugins/metismenu.css') }}" as="style" media="print"
        onload="this.media='all'">
    <link rel="stylesheet preload" href="{{ asset('assets/css/style.css') }}" as="style">

    <!-- Dynamic Theme Settings -->
    <style>
        :root {
            --primary-color: {{ setting('theme_primary_color', '#2678a1db') }};
            --secondary-color: {{ setting('theme_secondary_color', '#6c757d') }};
            --accent-color: {{ setting('theme_accent_color', '#28a745') }};
            --text-color: {{ setting('theme_text_color', '#333333') }};
            --background-color: {{ setting('theme_background_color', '#ffffff') }};
            --font-family: {{ setting('theme_font_family', 'Arial, sans-serif') }};
        }

        body {
            font-family: var(--font-family);
            color: var(--text-color);
            background-color: var(--background-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }
    </style>

    @stack('styles')
</head>

<body>
    @include('partials.header')

    @yield('content')

    @include('partials.footer')

    <!-- Scripts -->
    <script defer src="{{ asset('assets/js/plugins/jquery.js') }}"></script>
    <script defer src="{{ asset('assets/js/vendor/waw.js') }}"></script>
    <script defer src="{{ asset('assets/js/vendor/jarallax.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/smooth-scroll.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/counter-up.js') }}"></script>
    <script defer src="{{ asset('assets/js/vendor/waypoint.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/popup.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/swiper.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/svg-inject.js') }}"></script>
    <script defer src="{{ asset('assets/js/plugins/metismenu.js') }}"></script>
    <script defer src="{{ asset('assets/js/vendor/contact-form.js') }}"></script>
    <script defer src="{{ asset('assets/js/vendor/bootstrap.min.js') }}"></script>
    <script defer src="{{ asset('assets/js/main.js') }}"></script>
    <script src="{{ asset('assets/js/settings.js') }}"></script>


    <!-- Settings JavaScript Object -->
    <script>
        // Global settings object for frontend use
        window.appSettings = @json(public_settings());

        // Helper functions for accessing settings
        window.setting = function(key, defaultValue = null) {
            return window.appSettings[key] || defaultValue;
        };

        window.siteInfo = {
            name: setting('site_name', 'PESCOT'),
            tagline: setting('site_tagline', 'Center for Kingdom and Leadership Studies'),
            description: setting('site_description', ''),
            logo: setting('site_logo') ? '{{ asset("storage") }}/' + setting('site_logo') : null,
            favicon: setting('site_favicon') ? '{{ asset("storage") }}/' + setting('site_favicon') : null
        };

        window.themeSettings = {
            primaryColor: setting('theme_primary_color', '#2678a1db'),
            secondaryColor: setting('theme_secondary_color', '#6c757d'),
            accentColor: setting('theme_accent_color', '#28a745'),
            textColor: setting('theme_text_color', '#333333'),
            backgroundColor: setting('theme_background_color', '#ffffff'),
            fontFamily: setting('theme_font_family', 'Arial, sans-serif')
        };

        window.socialLinks = {
            facebook: setting('social_facebook'),
            twitter: setting('social_twitter'),
            instagram: setting('social_instagram'),
            linkedin: setting('social_linkedin'),
            youtube: setting('social_youtube')
        };

        // Apply theme settings to CSS variables
        if (window.themeSettings) {
            const root = document.documentElement;
            Object.keys(window.themeSettings).forEach(key => {
                const cssVar = '--' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
                root.style.setProperty(cssVar, window.themeSettings[key]);
            });
        }
    </script>

    @stack('scripts')

    <script>
        // Ensure preloader is hidden on page load
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });
        // Fallback in case window load event doesn't fire
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.body.classList.add('loaded');
            }, 1000);
        });
    </script>
</body>

</html>
