<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'PESCOT', 'type' => 'text', 'group' => 'general'],
            ['key' => 'tagline', 'value' => 'Your Trusted Learning Partner', 'type' => 'text', 'group' => 'general'],
            ['key' => 'logo', 'value' => '', 'type' => 'file', 'group' => 'general'],
            ['key' => 'favicon', 'value' => '', 'type' => 'file', 'group' => 'general'],

            // SEO Settings
            ['key' => 'meta_title', 'value' => 'PESCOT - Your Trusted Learning Partner', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'meta_description', 'value' => 'Professional learning and development services', 'type' => 'textarea', 'group' => 'seo'],
            ['key' => 'meta_keywords', 'value' => 'learning, education, training, development', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'google_analytics', 'value' => '', 'type' => 'text', 'group' => 'seo'],
            ['key' => 'google_tag_manager', 'value' => '', 'type' => 'text', 'group' => 'seo'],

            // Contact Settings
            ['key' => 'phone', 'value' => '+****************', 'type' => 'text', 'group' => 'contact'],
            ['key' => 'email', 'value' => '<EMAIL>', 'type' => 'email', 'group' => 'contact'],
            ['key' => 'address', 'value' => '123 Learning Street, Education City, EC 12345', 'type' => 'textarea', 'group' => 'contact'],
            ['key' => 'working_hours', 'value' => 'Mon-Fri: 9:00 AM - 6:00 PM', 'type' => 'text', 'group' => 'contact'],

            // Social Media Settings
            ['key' => 'facebook', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'twitter', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'instagram', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'linkedin', 'value' => '', 'type' => 'url', 'group' => 'social'],
            ['key' => 'youtube', 'value' => '', 'type' => 'url', 'group' => 'social'],

            // SMTP Settings
            ['key' => 'smtp_host', 'value' => '', 'type' => 'text', 'group' => 'smtp'],
            ['key' => 'smtp_port', 'value' => '587', 'type' => 'number', 'group' => 'smtp'],
            ['key' => 'smtp_username', 'value' => '', 'type' => 'text', 'group' => 'smtp'],
            ['key' => 'smtp_password', 'value' => '', 'type' => 'password', 'group' => 'smtp'],
            ['key' => 'smtp_encryption', 'value' => 'tls', 'type' => 'select', 'group' => 'smtp'],
            ['key' => 'mail_from_address', 'value' => '', 'type' => 'email', 'group' => 'smtp'],
            ['key' => 'mail_from_name', 'value' => 'PESCOT', 'type' => 'text', 'group' => 'smtp'],

            // reCAPTCHA Settings
            ['key' => 'recaptcha_site_key', 'value' => '', 'type' => 'text', 'group' => 'recaptcha'],
            ['key' => 'recaptcha_secret_key', 'value' => '', 'type' => 'text', 'group' => 'recaptcha'],
            ['key' => 'recaptcha_enabled', 'value' => '0', 'type' => 'boolean', 'group' => 'recaptcha'],

            // Newsletter Popup Settings
            ['key' => 'newsletter_enabled', 'value' => '1', 'type' => 'boolean', 'group' => 'newsletter'],
            ['key' => 'popup_delay', 'value' => '5', 'type' => 'number', 'group' => 'newsletter'],
            ['key' => 'popup_title', 'value' => 'Subscribe to Our Newsletter', 'type' => 'text', 'group' => 'newsletter'],
            ['key' => 'popup_message', 'value' => 'Stay updated with our latest news and offers!', 'type' => 'textarea', 'group' => 'newsletter'],
            ['key' => 'popup_image', 'value' => '', 'type' => 'file', 'group' => 'newsletter'],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
