<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'category_id',
        'price',
        'duration',
        'featured_image',
        'gallery',
        'status',
        'is_featured',
        'meta_title',
        'meta_description',
        'features',
        'benefits',
        'requirements',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_featured' => 'boolean',
        'gallery' => 'array',
        'features' => 'array',
        'benefits' => 'array',
        'requirements' => 'array',
        'sort_order' => 'integer',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the category that owns the service.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active services.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include featured services.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the service's featured image URL.
     */
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset('storage/' . $this->featured_image);
        }
        return asset('assets/img/services/default-service.jpg');
    }

    /**
     * Get the service's gallery URLs.
     */
    public function getGalleryUrlsAttribute()
    {
        if ($this->gallery && is_array($this->gallery)) {
            return array_map(function($image) {
                return asset('storage/' . $image);
            }, $this->gallery);
        }
        return [];
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        return $this->price ? '₦' . number_format($this->price, 2) : 'Price on request';
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute()
    {
        switch ($this->status) {
            case 'active':
                return 'badge-success';
            case 'inactive':
                return 'badge-secondary';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute()
    {
        switch ($this->status) {
            case 'active':
                return 'Active';
            case 'inactive':
                return 'Inactive';
            default:
                return 'Unknown';
        }
    }

    /**
     * Get excerpt from description.
     */
    public function getExcerptAttribute($length = 150)
    {
        if ($this->short_description) {
            return $this->short_description;
        }
        
        return strlen($this->description) > $length 
            ? substr(strip_tags($this->description), 0, $length) . '...'
            : strip_tags($this->description);
    }
}
