@extends('layouts.admin')

@section('title', 'FAQs Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">FAQs Management</h3>
                    <a href="{{ route('admin.faqs.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New FAQ
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.faqs.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search FAQs..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.faqs.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- FAQs Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Question</th>
                                    <th>Answer</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($faqs as $faq)
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1">{{ Str::limit($faq->question, 60) }}</h6>
                                            <small class="text-muted">ID: {{ $faq->id }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ Str::limit($faq->answer, 80) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $faq->status_badge_class }}">
                                            {{ $faq->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $faq->sort_order }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.faqs.show', $faq) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.faqs.edit', $faq) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-{{ $faq->status === 'active' ? 'warning' : 'success' }}" 
                                                    onclick="toggleStatus({{ $faq->id }})"
                                                    title="{{ $faq->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $faq->status === 'active' ? 'eye-slash' : 'eye' }}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteFaq({{ $faq->id }})"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-question-circle fa-3x mb-3"></i>
                                            <h5>No FAQs found</h5>
                                            <p>Start by creating your first FAQ.</p>
                                            <a href="{{ route('admin.faqs.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Create FAQ
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($faqs->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $faqs->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle FAQ status
function toggleStatus(faqId) {
    if (confirm('Are you sure you want to change the status of this FAQ?')) {
        fetch(`/admin/faqs/${faqId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating FAQ status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating FAQ status');
        });
    }
}

// Delete FAQ
function deleteFaq(faqId) {
    if (confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/faqs/${faqId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
