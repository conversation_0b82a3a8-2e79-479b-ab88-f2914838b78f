<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!-- ========== Meta Tags ========== -->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="author" content="Pescot Agro Industry Limited">
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Pescot Agro Industry Limited - Total Solution Provider for Soil Health and Crop Productivity'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', 'agriculture, fertilizers, organic fertilizers, inorganic fertilizers, soil health, crop productivity, farming, Nigeria'); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $__env->yieldContent('og_title', 'Pescot Agro Industry Limited'); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('og_description', 'Total Solution Provider for Soil Health and Crop Productivity'); ?>">
    <meta property="og:image" content="<?php echo $__env->yieldContent('og_image', asset('assets/img/logo/logo-black.png')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $__env->yieldContent('twitter_title', 'Pescot Agro Industry Limited'); ?>">
    <meta name="twitter:description" content="<?php echo $__env->yieldContent('twitter_description', 'Total Solution Provider for Soil Health and Crop Productivity'); ?>">
    <meta name="twitter:image" content="<?php echo $__env->yieldContent('twitter_image', asset('assets/img/logo/logo-black.png')); ?>">

    <!-- ======== Page title ============ -->
    <title><?php echo $__env->yieldContent('title', 'Pescot Agro Industry Limited - Total Solution Provider for Soil Health and Crop Productivity'); ?></title>

    <!--<< Favcion >>-->
    <link rel="shortcut icon" href="<?php echo e(asset('assets/img/logo/favcion.png')); ?>">

    <!--<< Bootstrap min.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>">
    <!--<< All Min Css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/all.min.css')); ?>">
    <!--<< Animate.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/animate.css')); ?>">
    <!--<< Magnific Popup.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/magnific-popup.css')); ?>">
    <!--<< MeanMenu.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/meanmenu.css')); ?>">
    <!--<< Swiper Bundle.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/swiper-bundle.min.css')); ?>">
    <!--<< Nice Select.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/nice-select.css')); ?>">
    <!--<< Main.css >>-->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/main.css')); ?>">

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body>
    <!-- Preloader Start -->
    <div id="preloader" class="preloader">
        <div class="box d-grid gap-4">
            <span class="man-pre m-auto">
                <img src="<?php echo e(asset('assets/img/banner/preloader.png')); ?>" alt="img">
            </span>
            <span class="p1-clr fz-40 fw-bold text-center d-block">
                Pescot Agro
            </span>
        </div>
    </div>

    <!-- Scroll To Top Start-->
    <button class="scrollToTop d-none d-md-flex d-center" aria-label="scroll Bar Button"><i
            class="mat-icon fas fa-angle-double-up"></i></button>
    <!-- Scroll To Top End -->

    <?php echo $__env->make('partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php echo $__env->yieldContent('content'); ?>

    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!--<< All JS Plugins >>-->
    <script src="<?php echo e(asset('assets/js/jquery-3.7.1.min.js')); ?>"></script>
    <!--<< Viewport Js >>-->
    <script src="<?php echo e(asset('assets/js/viewport.jquery.js')); ?>"></script>
    <!--<< Bootstrap Js >>-->
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--<< Nice Select Js >>-->
    <script src="<?php echo e(asset('assets/js/jquery.nice-select.min.js')); ?>"></script>
    <!--<< Waypoints Js >>-->
    <script src="<?php echo e(asset('assets/js/jquery.waypoints.js')); ?>"></script>
    <!--<< Counterup Js >>-->
    <script src="<?php echo e(asset('assets/js/jquery.counterup.min.js')); ?>"></script>
    <!--<< Swiper Slider Js >>-->
    <script src="<?php echo e(asset('assets/js/swiper-bundle.min.js')); ?>"></script>
    <!--<< MeanMenu Js >>-->
    <script src="<?php echo e(asset('assets/js/jquery.meanmenu.min.js')); ?>"></script>
    <!--<< Magnific Popup Js >>-->
    <script src="<?php echo e(asset('assets/js/jquery.magnific-popup.min.js')); ?>"></script>
    <!--<< Filter Mixtup Js >>-->
    <script src="<?php echo e(asset('assets/js/mixitup.min.js')); ?>"></script>
    <!--<< Wow Animation Js >>-->
    <script src="<?php echo e(asset('assets/js/wow.min.js')); ?>"></script>
    <!--<< Main.js >>-->
    <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/layouts/app.blade.php ENDPATH**/ ?>