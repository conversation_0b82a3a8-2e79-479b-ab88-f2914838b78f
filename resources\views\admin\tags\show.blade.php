@extends('layouts.admin')

@section('title', 'Tag Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Tag: {{ $tag->name }}</h3>
                    <div>
                        <a href="{{ route('admin.tags.edit', $tag) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.tags.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tags
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Tag Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Tag Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="150">Name:</th>
                                            <td><strong>{{ $tag->name }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Slug:</th>
                                            <td><code>{{ $tag->slug }}</code></td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <span class="badge badge-{{ $tag->status === 'active' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($tag->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Sort Order:</th>
                                            <td><span class="badge badge-info">{{ $tag->sort_order }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Posts Count:</th>
                                            <td><span class="badge badge-primary">{{ $tag->posts->count() }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Created:</th>
                                            <td>{{ $tag->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>Updated:</th>
                                            <td>{{ $tag->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Description -->
                            @if($tag->description)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Description</h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0">{{ $tag->description }}</p>
                                </div>
                            </div>
                            @endif

                            <!-- SEO Information -->
                            @if($tag->meta_title || $tag->meta_description)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Information</h5>
                                </div>
                                <div class="card-body">
                                    @if($tag->meta_title)
                                    <div class="mb-3">
                                        <strong>Meta Title:</strong><br>
                                        <span class="text-muted">{{ $tag->meta_title }}</span>
                                    </div>
                                    @endif
                                    @if($tag->meta_description)
                                    <div>
                                        <strong>Meta Description:</strong><br>
                                        <span class="text-muted">{{ $tag->meta_description }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endif

                            <!-- Associated Posts -->
                            <div class="card mt-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">Associated Posts ({{ $tag->posts->count() }})</h5>
                                    @if($tag->posts->count() > 0)
                                    <a href="{{ route('admin.posts.index', ['tag' => $tag->id]) }}" class="btn btn-sm btn-primary">
                                        Manage Posts
                                    </a>
                                    @endif
                                </div>
                                <div class="card-body">
                                    @if($tag->posts->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Category</th>
                                                        <th>Author</th>
                                                        <th>Status</th>
                                                        <th>Published</th>
                                                        <th>Views</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($tag->posts()->with(['category', 'author'])->latest()->take(10)->get() as $post)
                                                    <tr>
                                                        <td>
                                                            <a href="{{ route('admin.posts.show', $post) }}" class="text-decoration-none">
                                                                {{ Str::limit($post->title, 40) }}
                                                            </a>
                                                            @if($post->featured)
                                                                <i class="fas fa-star text-warning ml-1" title="Featured"></i>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            @if($post->category)
                                                                <a href="{{ route('admin.categories.show', $post->category) }}" class="badge badge-secondary text-decoration-none">
                                                                    {{ $post->category->name }}
                                                                </a>
                                                            @else
                                                                <span class="text-muted">No Category</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <small>{{ $post->author->name ?? 'Unknown' }}</small>
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-{{ $post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning') }}">
                                                                {{ ucfirst($post->status) }}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            @if($post->published_at)
                                                                <small>{{ $post->published_at->format('M d, Y') }}</small>
                                                            @else
                                                                <span class="text-muted">Not published</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-info">{{ $post->views ?? 0 }}</span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ route('admin.posts.show', $post) }}" class="btn btn-xs btn-info" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-xs btn-warning" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        @if($tag->posts->count() > 10)
                                        <div class="text-center mt-3">
                                            <a href="{{ route('admin.posts.index', ['tag' => $tag->id]) }}" class="btn btn-outline-primary">
                                                View All {{ $tag->posts->count() }} Posts
                                            </a>
                                        </div>
                                        @endif
                                    @else
                                        <div class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                                <h6>No posts found</h6>
                                                <p>This tag is not associated with any posts yet.</p>
                                                <a href="{{ route('admin.posts.create') }}" class="btn btn-primary">
                                                    <i class="fas fa-plus"></i> Create Post
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.tags.edit', $tag) }}" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> Edit Tag
                                        </a>
                                        
                                        <button type="button" class="btn btn-{{ $tag->status === 'active' ? 'secondary' : 'success' }} btn-sm" 
                                                onclick="toggleStatus({{ $tag->id }})">
                                            <i class="fas fa-{{ $tag->status === 'active' ? 'eye-slash' : 'eye' }}"></i> 
                                            {{ $tag->status === 'active' ? 'Deactivate' : 'Activate' }}
                                        </button>
                                        
                                        @if($tag->status === 'active')
                                        <a href="#" class="btn btn-info btn-sm" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> View on Site
                                        </a>
                                        @endif
                                        
                                        @if($tag->posts->count() === 0)
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteTag({{ $tag->id }})">
                                            <i class="fas fa-trash"></i> Delete Tag
                                        </button>
                                        @else
                                        <button type="button" class="btn btn-danger btn-sm" disabled title="Cannot delete tag with associated posts">
                                            <i class="fas fa-trash"></i> Delete Tag
                                        </button>
                                        <small class="text-muted mt-1 d-block">
                                            Remove all posts from this tag before deleting.
                                        </small>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-right">
                                                <h4 class="text-primary">{{ $tag->posts->count() }}</h4>
                                                <small class="text-muted">Total Posts</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-success">{{ $tag->posts()->where('status', 'published')->count() }}</h4>
                                            <small class="text-muted">Published</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-right">
                                                <h5 class="text-warning">{{ $tag->posts()->where('featured', true)->count() }}</h5>
                                                <small class="text-muted">Featured</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="text-info">{{ $tag->posts()->where('status', 'draft')->count() }}</h5>
                                            <small class="text-muted">Drafts</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tag Usage -->
                            @if($tag->posts->count() > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Usage Analytics</h5>
                                </div>
                                <div class="card-body">
                                    @php
                                        $totalViews = $tag->posts->sum('views');
                                        $avgViews = $tag->posts->count() > 0 ? round($totalViews / $tag->posts->count()) : 0;
                                        $recentPosts = $tag->posts()->where('created_at', '>=', now()->subDays(30))->count();
                                    @endphp
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <th>Total Views:</th>
                                            <td><span class="badge badge-primary">{{ number_format($totalViews) }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Avg. Views:</th>
                                            <td><span class="badge badge-info">{{ number_format($avgViews) }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Recent Posts:</th>
                                            <td><span class="badge badge-success">{{ $recentPosts }}</span></td>
                                        </tr>
                                        <tr>
                                            <th>Last Used:</th>
                                            <td>
                                                @if($tag->posts->count() > 0)
                                                    {{ $tag->posts()->latest()->first()->created_at->diffForHumans() }}
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            @endif

                            <!-- Related Tags -->
                            @php
                                $relatedTags = collect();
                                if($tag->posts->count() > 0) {
                                    $postIds = $tag->posts->pluck('id');
                                    $relatedTags = \App\Models\Tag::whereHas('posts', function($query) use ($postIds) {
                                        $query->whereIn('posts.id', $postIds);
                                    })->where('id', '!=', $tag->id)->withCount('posts')->orderBy('posts_count', 'desc')->take(5)->get();
                                }
                            @endphp
                            @if($relatedTags->count() > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Related Tags</h5>
                                </div>
                                <div class="card-body">
                                    @foreach($relatedTags as $relatedTag)
                                        <a href="{{ route('admin.tags.show', $relatedTag) }}" class="badge badge-secondary mr-1 mb-1 text-decoration-none">
                                            {{ $relatedTag->name }} ({{ $relatedTag->posts_count }})
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <!-- Recent Activity -->
                            @if($tag->posts->count() > 0)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        @foreach($tag->posts()->latest()->take(3)->get() as $post)
                                        <div class="timeline-item mb-3">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <h6 class="mb-1">
                                                    <a href="{{ route('admin.posts.show', $post) }}" class="text-decoration-none">
                                                        {{ Str::limit($post->title, 30) }}
                                                    </a>
                                                </h6>
                                                <small class="text-muted">
                                                    {{ $post->created_at->diffForHumans() }}
                                                </small>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this tag?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 0.375rem;
    top: 1rem;
    width: 1px;
    height: calc(100% - 0.5rem);
    background-color: #dee2e6;
}

.timeline-content {
    padding-top: 0.125rem;
}
</style>
@endpush

@push('scripts')
<script>
// Toggle status
function toggleStatus(tagId) {
    $.ajax({
        url: `/admin/tags/${tagId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

// Delete tag
function deleteTag(tagId) {
    $('#deleteForm').attr('action', `/admin/tags/${tagId}`);
    $('#deleteModal').modal('show');
}
</script>
@endpush