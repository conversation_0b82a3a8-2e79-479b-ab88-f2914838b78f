<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    /**
     * Display general settings
     */
    public function general()
    {
        $settings = Setting::getByGroup('general');
        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Update general settings
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'tagline' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'favicon' => 'nullable|image|mimes:ico,png|max:1024',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $oldLogo = Setting::get('logo');
            if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                Storage::disk('public')->delete($oldLogo);
            }
            $logoPath = $request->file('logo')->store('settings', 'public');
            Setting::set('logo', $logoPath, 'file', 'general');
        }

        // Handle favicon upload
        if ($request->hasFile('favicon')) {
            $oldFavicon = Setting::get('favicon');
            if ($oldFavicon && Storage::disk('public')->exists($oldFavicon)) {
                Storage::disk('public')->delete($oldFavicon);
            }
            $faviconPath = $request->file('favicon')->store('settings', 'public');
            Setting::set('favicon', $faviconPath, 'file', 'general');
        }

        Setting::set('site_name', $request->site_name, 'text', 'general');
        Setting::set('tagline', $request->tagline, 'text', 'general');

        return redirect()->back()->with('success', 'General settings updated successfully.');
    }

    /**
     * Display SEO settings
     */
    public function seo()
    {
        $settings = Setting::getByGroup('seo');
        return view('admin.settings.seo', compact('settings'));
    }

    /**
     * Update SEO settings
     */
    public function updateSeo(Request $request)
    {
        $request->validate([
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'google_analytics' => 'nullable|string',
            'google_tag_manager' => 'nullable|string',
        ]);

        Setting::set('meta_title', $request->meta_title, 'text', 'seo');
        Setting::set('meta_description', $request->meta_description, 'textarea', 'seo');
        Setting::set('meta_keywords', $request->meta_keywords, 'text', 'seo');
        Setting::set('google_analytics', $request->google_analytics, 'text', 'seo');
        Setting::set('google_tag_manager', $request->google_tag_manager, 'text', 'seo');

        return redirect()->back()->with('success', 'SEO settings updated successfully.');
    }

    /**
     * Display contact settings
     */
    public function contact()
    {
        $settings = Setting::getByGroup('contact');
        return view('admin.settings.contact', compact('settings'));
    }

    /**
     * Update contact settings
     */
    public function updateContact(Request $request)
    {
        $request->validate([
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'working_hours' => 'nullable|string|max:255',
        ]);

        Setting::set('phone', $request->phone, 'text', 'contact');
        Setting::set('email', $request->email, 'email', 'contact');
        Setting::set('address', $request->address, 'textarea', 'contact');
        Setting::set('working_hours', $request->working_hours, 'text', 'contact');

        return redirect()->back()->with('success', 'Contact settings updated successfully.');
    }

    /**
     * Display social media settings
     */
    public function social()
    {
        $settings = Setting::getByGroup('social');
        return view('admin.settings.social', compact('settings'));
    }

    /**
     * Update social media settings
     */
    public function updateSocial(Request $request)
    {
        $request->validate([
            'facebook' => 'nullable|url',
            'twitter' => 'nullable|url',
            'instagram' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'youtube' => 'nullable|url',
        ]);

        Setting::set('facebook', $request->facebook, 'url', 'social');
        Setting::set('twitter', $request->twitter, 'url', 'social');
        Setting::set('instagram', $request->instagram, 'url', 'social');
        Setting::set('linkedin', $request->linkedin, 'url', 'social');
        Setting::set('youtube', $request->youtube, 'url', 'social');

        return redirect()->back()->with('success', 'Social media settings updated successfully.');
    }

    /**
     * Display SMTP settings
     */
    public function smtp()
    {
        $settings = Setting::getByGroup('smtp');
        return view('admin.settings.smtp', compact('settings'));
    }

    /**
     * Update SMTP settings
     */
    public function updateSmtp(Request $request)
    {
        $request->validate([
            'smtp_host' => 'nullable|string|max:255',
            'smtp_port' => 'nullable|integer',
            'smtp_username' => 'nullable|string|max:255',
            'smtp_password' => 'nullable|string|max:255',
            'smtp_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'nullable|email',
            'mail_from_name' => 'nullable|string|max:255',
        ]);

        Setting::set('smtp_host', $request->smtp_host, 'text', 'smtp');
        Setting::set('smtp_port', $request->smtp_port, 'number', 'smtp');
        Setting::set('smtp_username', $request->smtp_username, 'text', 'smtp');
        Setting::set('smtp_password', $request->smtp_password, 'password', 'smtp');
        Setting::set('smtp_encryption', $request->smtp_encryption, 'select', 'smtp');
        Setting::set('mail_from_address', $request->mail_from_address, 'email', 'smtp');
        Setting::set('mail_from_name', $request->mail_from_name, 'text', 'smtp');

        return redirect()->back()->with('success', 'SMTP settings updated successfully.');
    }

    /**
     * Display reCAPTCHA settings
     */
    public function recaptcha()
    {
        $settings = Setting::getByGroup('recaptcha');
        return view('admin.settings.recaptcha', compact('settings'));
    }

    /**
     * Update reCAPTCHA settings
     */
    public function updateRecaptcha(Request $request)
    {
        $request->validate([
            'recaptcha_site_key' => 'nullable|string|max:255',
            'recaptcha_secret_key' => 'nullable|string|max:255',
            'recaptcha_enabled' => 'boolean',
        ]);

        Setting::set('recaptcha_site_key', $request->recaptcha_site_key, 'text', 'recaptcha');
        Setting::set('recaptcha_secret_key', $request->recaptcha_secret_key, 'text', 'recaptcha');
        Setting::set('recaptcha_enabled', $request->boolean('recaptcha_enabled'), 'boolean', 'recaptcha');

        return redirect()->back()->with('success', 'reCAPTCHA settings updated successfully.');
    }

    /**
     * Display newsletter popup settings
     */
    public function newsletter()
    {
        $settings = Setting::getByGroup('newsletter');
        return view('admin.settings.newsletter', compact('settings'));
    }

    /**
     * Update newsletter popup settings
     */
    public function updateNewsletter(Request $request)
    {
        $request->validate([
            'newsletter_enabled' => 'boolean',
            'popup_delay' => 'nullable|integer|min:0',
            'popup_title' => 'nullable|string|max:255',
            'popup_message' => 'nullable|string|max:500',
            'popup_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle popup image upload
        if ($request->hasFile('popup_image')) {
            $oldImage = Setting::get('popup_image');
            if ($oldImage && Storage::disk('public')->exists($oldImage)) {
                Storage::disk('public')->delete($oldImage);
            }
            $imagePath = $request->file('popup_image')->store('settings', 'public');
            Setting::set('popup_image', $imagePath, 'file', 'newsletter');
        }

        Setting::set('newsletter_enabled', $request->boolean('newsletter_enabled'), 'boolean', 'newsletter');
        Setting::set('popup_delay', $request->popup_delay ?? 5, 'number', 'newsletter');
        Setting::set('popup_title', $request->popup_title, 'text', 'newsletter');
        Setting::set('popup_message', $request->popup_message, 'textarea', 'newsletter');

        return redirect()->back()->with('success', 'Newsletter popup settings updated successfully.');
    }
}
