@extends('layouts.admin')

@section('title', 'Edit Comment')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Comment</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.comments.show', $comment) }}" class="btn btn-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('admin.comments.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Comments
                        </a>
                    </div>
                </div>
                <form action="{{ route('admin.comments.update', $comment) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Comment Content -->
                                <div class="form-group">
                                    <label for="comment" class="required">Comment</label>
                                    <textarea class="form-control @error('comment') is-invalid @enderror"
                                              id="comment" name="comment" rows="6" required
                                              placeholder="Enter the comment content...">{{ old('comment', $comment->comment) }}</textarea>
                                    @error('comment')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        <span id="commentCount">{{ strlen($comment->comment) }}</span> characters
                                    </small>
                                </div>

                                <!-- Author Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name" class="required">Author Name</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                                   id="name" name="name" value="{{ old('name', $comment->name) }}" required
                                                   placeholder="Enter author name" {{ $comment->user_id ? 'readonly' : '' }}>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            @if($comment->user_id)
                                                <small class="form-text text-muted">
                                                    <i class="fas fa-info-circle"></i> This comment is associated with a registered user.
                                                </small>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email" class="required">Email</label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                   id="email" name="email" value="{{ old('email', $comment->email) }}" required
                                                   placeholder="Enter email address" {{ $comment->user_id ? 'readonly' : '' }}>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="website">Website</label>
                                    <input type="url" class="form-control @error('website') is-invalid @enderror"
                                           id="website" name="website" value="{{ old('website', $comment->website) }}"
                                           placeholder="https://example.com">
                                    @error('website')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Post Selection -->
                                <div class="form-group">
                                    <label for="post_id" class="required">Post</label>
                                    <select class="form-control @error('post_id') is-invalid @enderror"
                                            id="post_id" name="post_id" required>
                                        <option value="">Select a post...</option>
                                        @foreach($posts as $post)
                                            <option value="{{ $post->id }}" {{ old('post_id', $comment->post_id) == $post->id ? 'selected' : '' }}>
                                                {{ $post->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('post_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Parent Comment (Reply) -->
                                <div class="form-group">
                                    <label for="parent_id">Reply to Comment</label>
                                    <select class="form-control @error('parent_id') is-invalid @enderror"
                                            id="parent_id" name="parent_id">
                                        <option value="">This is a new comment (not a reply)</option>
                                        @foreach($availableParents as $parent)
                                            <option value="{{ $parent->id }}" {{ old('parent_id', $comment->parent_id) == $parent->id ? 'selected' : '' }}>
                                                {{ $parent->name }}: {{ Str::limit($parent->comment, 50) }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('parent_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($comment->parent_id)
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle"></i> This comment is currently a reply.
                                        </small>
                                    @endif
                                </div>

                                <!-- User Association -->
                                <div class="form-group">
                                    <label for="user_id">Associate with User</label>
                                    <select class="form-control @error('user_id') is-invalid @enderror"
                                            id="user_id" name="user_id">
                                        <option value="">No user association (guest comment)</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('user_id', $comment->user_id) == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        If associated with a user, the name and email will be taken from the user account.
                                    </small>
                                </div>

                                <!-- Replies Section -->
                                @if($comment->replies->count() > 0)
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-comments"></i> Replies ({{ $comment->replies->count() }})
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        @foreach($comment->replies as $reply)
                                        <div class="border-left border-primary pl-3 mb-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <strong>{{ $reply->name }}</strong>
                                                    <span class="badge badge-{{ $reply->status === 'approved' ? 'success' : ($reply->status === 'pending' ? 'warning' : 'danger') }}">
                                                        {{ ucfirst($reply->status) }}
                                                    </span>
                                                    <small class="text-muted d-block">{{ $reply->created_at->format('M d, Y H:i') }}</small>
                                                </div>
                                                <div>
                                                    <a href="{{ route('admin.comments.edit', $reply) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                </div>
                                            </div>
                                            <p class="mt-2 mb-0">{{ $reply->comment }}</p>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-4">
                                <!-- Publishing Options -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Publishing Options</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="status" class="required">Status</label>
                                            <select class="form-control @error('status') is-invalid @enderror"
                                                    id="status" name="status" required>
                                                <option value="pending" {{ old('status', $comment->status) === 'pending' ? 'selected' : '' }}>Pending Review</option>
                                                <option value="approved" {{ old('status', $comment->status) === 'approved' ? 'selected' : '' }}>Approved</option>
                                                <option value="rejected" {{ old('status', $comment->status) === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="created_at">Comment Date</label>
                                            <input type="datetime-local" class="form-control @error('created_at') is-invalid @enderror"
                                                   id="created_at" name="created_at"
                                                   value="{{ old('created_at', $comment->created_at->format('Y-m-d\TH:i')) }}">
                                            @error('created_at')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Quick Actions -->
                                        <div class="form-group">
                                            <label>Quick Actions</label>
                                            <div class="btn-group-vertical d-block">
                                                @if($comment->status !== 'approved')
                                                    <button type="button" class="btn btn-success btn-sm mb-1" onclick="changeStatus('approved')">
                                                        <i class="fas fa-check"></i> Approve
                                                    </button>
                                                @endif
                                                @if($comment->status !== 'rejected')
                                                    <button type="button" class="btn btn-danger btn-sm mb-1" onclick="changeStatus('rejected')">
                                                        <i class="fas fa-times"></i> Reject
                                                    </button>
                                                @endif
                                                @if($comment->status !== 'pending')
                                                    <button type="button" class="btn btn-warning btn-sm mb-1" onclick="changeStatus('pending')">
                                                        <i class="fas fa-clock"></i> Mark as Pending
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Technical Information -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Technical Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="ip_address">IP Address</label>
                                            <input type="text" class="form-control @error('ip_address') is-invalid @enderror"
                                                   id="ip_address" name="ip_address"
                                                   value="{{ old('ip_address', $comment->ip_address) }}"
                                                   placeholder="127.0.0.1">
                                            @error('ip_address')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="user_agent">User Agent</label>
                                            <textarea class="form-control @error('user_agent') is-invalid @enderror"
                                                      id="user_agent" name="user_agent" rows="3"
                                                      placeholder="Browser user agent string...">{{ old('user_agent', $comment->user_agent) }}</textarea>
                                            @error('user_agent')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Comment Statistics -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Statistics</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border-right">
                                                    <h4 class="mb-0">{{ $comment->replies->count() }}</h4>
                                                    <small class="text-muted">Replies</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <h4 class="mb-0">{{ $comment->created_at->diffForHumans() }}</h4>
                                                <small class="text-muted">Age</small>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="text-center">
                                            <small class="text-muted">
                                                Created: {{ $comment->created_at->format('M d, Y H:i') }}<br>
                                                Updated: {{ $comment->updated_at->format('M d, Y H:i') }}
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Preview -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Preview</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="commentPreview" class="border rounded p-3 bg-light">
                                            <div class="d-flex align-items-start">
                                                <div class="avatar-placeholder bg-secondary rounded-circle d-flex align-items-center justify-content-center text-white mr-3" style="width: 40px; height: 40px; min-width: 40px;">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-1">
                                                        <strong id="previewName">{{ $comment->name }}</strong>
                                                        <small class="text-muted ml-2" id="previewDate">{{ $comment->created_at->format('M d, Y H:i') }}</small>
                                                    </div>
                                                    <div id="previewComment">{{ $comment->comment }}</div>
                                                    <div id="previewWebsite" class="mt-1" style="{{ $comment->website ? '' : 'display: none;' }}">
                                                        <small><a href="{{ $comment->website }}" target="_blank" class="text-info">Visit Website</a></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Comment
                                </button>
                                <a href="{{ route('admin.comments.show', $comment) }}" class="btn btn-info ml-2">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{{ route('admin.comments.index') }}" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <button type="button" class="btn btn-danger" onclick="deleteComment()">
                                    <i class="fas fa-trash"></i> Delete Comment
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this comment?</p>
                @if($comment->replies->count() > 0)
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> This will also delete {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }} to this comment.
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.comments.destroy', $comment) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.required::after {
    content: ' *';
    color: #e74c3c;
}

.avatar-placeholder {
    font-size: 1.2rem;
}

#commentPreview {
    min-height: 100px;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
}

.border-left {
    border-left: 3px solid #2678a1db !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('#post_id, #parent_id, #user_id').select2({
        width: '100%'
    });

    // Character counter for comment
    $('#comment').on('input', function() {
        const length = $(this).val().length;
        $('#commentCount').text(length);
        updatePreview();
    });

    // Update preview on input changes
    $('#name, #comment, #website').on('input', updatePreview);
    $('#created_at').on('change', updatePreview);

    // Auto-fill name and email when user is selected
    $('#user_id').on('change', function() {
        const userId = $(this).val();
        if (userId) {
            const selectedOption = $(this).find('option:selected');
            const text = selectedOption.text();
            const matches = text.match(/^(.+) \((.+)\)$/);

            if (matches) {
                $('#name').val(matches[1]).prop('readonly', true);
                $('#email').val(matches[2]).prop('readonly', true);
            }
        } else {
            $('#name, #email').prop('readonly', false);
        }
        updatePreview();
    });

    // Update preview function
    function updatePreview() {
        const name = $('#name').val() || 'Author Name';
        const comment = $('#comment').val() || 'Comment content will appear here...';
        const website = $('#website').val();
        const createdAt = $('#created_at').val();

        $('#previewName').text(name);
        $('#previewComment').text(comment);

        if (website) {
            $('#previewWebsite').show().find('a').attr('href', website);
        } else {
            $('#previewWebsite').hide();
        }

        if (createdAt) {
            const date = new Date(createdAt);
            $('#previewDate').text(date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }));
        }
    }

    // Form validation
    $('form').on('submit', function(e) {
        let isValid = true;
        const requiredFields = ['comment', 'name', 'email', 'post_id', 'status'];

        requiredFields.forEach(function(field) {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });

    // Remove validation errors on input
    $('.form-control').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
});

// Quick status change
function changeStatus(status) {
    if (confirm(`Are you sure you want to mark this comment as ${status}?`)) {
        $('#status').val(status);
        $('form').submit();
    }
}

// Delete comment
function deleteComment() {
    $('#deleteModal').modal('show');
}
</script>
@endpush
