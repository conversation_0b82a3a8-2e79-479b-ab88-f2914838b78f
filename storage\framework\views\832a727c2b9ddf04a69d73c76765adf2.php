<?php $__env->startSection('title', 'Testimonials Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Testimonials Management</h3>
                    <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Testimonial
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.testimonials.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" name="search" class="form-control" placeholder="Search testimonials..." value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="<?php echo e(route('admin.testimonials.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Testimonials Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Testimonial</th>
                                    <th>Rating</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($testimonial->client_image): ?>
                                                <img src="<?php echo e(asset('storage/' . $testimonial->client_image)); ?>" 
                                                     alt="<?php echo e($testimonial->client_name); ?>" 
                                                     class="rounded-circle me-3" 
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    <?php echo e(strtoupper(substr($testimonial->client_name, 0, 2))); ?>

                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($testimonial->client_name); ?></h6>
                                                <small class="text-muted"><?php echo e($testimonial->client_position); ?></small>
                                                <?php if($testimonial->client_company): ?>
                                                    <br><small class="text-muted"><?php echo e($testimonial->client_company); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?php echo e(Str::limit($testimonial->testimonial, 100)); ?></span>
                                        <?php if($testimonial->is_featured): ?>
                                            <br><span class="badge badge-warning">Featured</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($testimonial->rating): ?>
                                            <div class="text-warning">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo e($i <= $testimonial->rating ? '' : '-o'); ?>"></i>
                                                <?php endfor; ?>
                                                <br><small class="text-muted"><?php echo e($testimonial->rating); ?>/5</small>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">No rating</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo e($testimonial->status_badge_class); ?>">
                                            <?php echo e($testimonial->status_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.testimonials.show', $testimonial)); ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.testimonials.edit', $testimonial)); ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-<?php echo e($testimonial->status === 'active' ? 'warning' : 'success'); ?>" 
                                                    onclick="toggleStatus(<?php echo e($testimonial->id); ?>)"
                                                    title="<?php echo e($testimonial->status === 'active' ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($testimonial->status === 'active' ? 'eye-slash' : 'eye'); ?>"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteTestimonial(<?php echo e($testimonial->id); ?>)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-quote-left fa-3x mb-3"></i>
                                            <h5>No testimonials found</h5>
                                            <p>Start by adding your first testimonial.</p>
                                            <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Add Testimonial
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($testimonials->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($testimonials->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle testimonial status
function toggleStatus(testimonialId) {
    if (confirm('Are you sure you want to change the status of this testimonial?')) {
        fetch(`/admin/testimonials/${testimonialId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating testimonial status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating testimonial status');
        });
    }
}

// Delete testimonial
function deleteTestimonial(testimonialId) {
    if (confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/testimonials/${testimonialId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/testimonials/index.blade.php ENDPATH**/ ?>