<?php $__env->startSection('title', 'Posts Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Posts Management</h3>
                    <a href="<?php echo e(route('admin.posts.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Post
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="GET" action="<?php echo e(route('admin.posts.index')); ?>" class="form-inline">
                                <div class="form-group mr-3">
                                    <input type="text" name="search" class="form-control" placeholder="Search posts..." 
                                           value="<?php echo e(request('search')); ?>">
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="category" class="form-control">
                                        <option value="">All Categories</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>Published</option>
                                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Draft</option>
                                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="featured" class="form-control">
                                        <option value="">All Posts</option>
                                        <option value="1" <?php echo e(request('featured') == '1' ? 'selected' : ''); ?>>Featured Only</option>
                                        <option value="0" <?php echo e(request('featured') == '0' ? 'selected' : ''); ?>>Non-Featured</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="author" class="form-control">
                                        <option value="">All Authors</option>
                                        <?php $__currentLoopData = $authors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $author): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($author->id); ?>" <?php echo e(request('author') == $author->id ? 'selected' : ''); ?>>
                                                <?php echo e($author->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-secondary mr-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                
                                <a href="<?php echo e(route('admin.posts.index')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form id="bulkActionForm" method="POST" action="<?php echo e(route('admin.posts.bulk-action')); ?>">
                                <?php echo csrf_field(); ?>
                                <div class="form-inline">
                                    <select name="action" class="form-control mr-2" required>
                                        <option value="">Bulk Actions</option>
                                        <option value="publish">Publish</option>
                                        <option value="draft">Move to Draft</option>
                                        <option value="pending">Move to Pending</option>
                                        <option value="feature">Mark as Featured</option>
                                        <option value="unfeature">Remove Featured</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                        Apply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Posts Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Author</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th>Published</th>
                                    <th>Views</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_posts[]" value="<?php echo e($post->id); ?>" class="post-checkbox">
                                    </td>
                                    <td>
                                        <?php if($post->featured_image): ?>
                                            <img src="<?php echo e($post->featuredImageUrl); ?>" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('admin.posts.show', $post)); ?>" class="text-decoration-none font-weight-bold">
                                            <?php echo e(Str::limit($post->title, 50)); ?>

                                        </a>
                                        <br>
                                        <small class="text-muted"><?php echo e($post->slug); ?></small>
                                    </td>
                                    <td>
                                        <?php if($post->category): ?>
                                            <span class="badge badge-info"><?php echo e($post->category->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">No Category</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($post->author->name ?? 'Unknown'); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning')); ?>">
                                            <?php echo e(ucfirst($post->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-<?php echo e($post->featured ? 'warning' : 'outline-secondary'); ?>" 
                                                onclick="toggleFeatured(<?php echo e($post->id); ?>)" title="<?php echo e($post->featured ? 'Remove from Featured' : 'Mark as Featured'); ?>">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    </td>
                                    <td>
                                        <?php if($post->published_at): ?>
                                            <?php echo e($post->published_at->format('M d, Y')); ?>

                                        <?php else: ?>
                                            <span class="text-muted">Not Published</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-light"><?php echo e($post->views ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.posts.show', $post)); ?>" class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.posts.edit', $post)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-<?php echo e($post->status === 'published' ? 'secondary' : 'success'); ?>" 
                                                    onclick="toggleStatus(<?php echo e($post->id); ?>)" 
                                                    title="<?php echo e($post->status === 'published' ? 'Unpublish' : 'Publish'); ?>">
                                                <i class="fas fa-<?php echo e($post->status === 'published' ? 'eye-slash' : 'eye'); ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deletePost(<?php echo e($post->id); ?>)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No posts found.</p>
                                        <a href="<?php echo e(route('admin.posts.create')); ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Create First Post
                                        </a>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($posts->hasPages()): ?>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted mb-0">
                                Showing <?php echo e($posts->firstItem()); ?> to <?php echo e($posts->lastItem()); ?> of <?php echo e($posts->total()); ?> results
                            </p>
                        </div>
                        <div>
                            <?php echo e($posts->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this post? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').change(function() {
        $('.post-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Individual checkbox change
    $('.post-checkbox').change(function() {
        if ($('.post-checkbox:checked').length === $('.post-checkbox').length) {
            $('#selectAll').prop('checked', true);
        } else {
            $('#selectAll').prop('checked', false);
        }
    });

    // Bulk action form submission
    $('#bulkActionForm').submit(function(e) {
        var selectedPosts = $('.post-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedPosts.length === 0) {
            e.preventDefault();
            alert('Please select at least one post.');
            return false;
        }

        // Add selected posts to form
        selectedPosts.forEach(function(postId) {
            $("<input>").attr({
                type: "hidden",
                name: "selected_posts[]",
                value: postId
            }).appendTo('#bulkActionForm');
        });
    });
});

function toggleStatus(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

function toggleFeatured(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-featured`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating featured status');
            }
        },
        error: function() {
            alert('Error updating featured status');
        }
    });
}

function deletePost(postId) {
    $('#deleteForm').attr('action', `/admin/posts/${postId}`);
    $('#deleteModal').modal('show');
}

function confirmBulkAction() {
    var action = $('select[name="action"]').val();
    if (action === 'delete') {
        return confirm('Are you sure you want to delete the selected posts? This action cannot be undone.');
    }
    return true;
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/posts/index.blade.php ENDPATH**/ ?>