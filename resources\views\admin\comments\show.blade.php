@extends('layouts.admin')

@section('title', 'View Comment')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Comment Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.comments.edit', $comment) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.comments.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Comments
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Left Column - Comment Content -->
                        <div class="col-md-8">
                            <!-- Comment Display -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-comment"></i> Comment Content
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="comment-display border rounded p-4 bg-light">
                                        <div class="d-flex align-items-start">
                                            <div class="avatar-placeholder bg-{{ $comment->status === 'approved' ? 'success' : ($comment->status === 'pending' ? 'warning' : 'danger') }} rounded-circle d-flex align-items-center justify-content-center text-white mr-3" style="width: 50px; height: 50px; min-width: 50px;">
                                                @if($comment->user_id)
                                                    <i class="fas fa-user-check"></i>
                                                @else
                                                    <i class="fas fa-user"></i>
                                                @endif
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-items-center mb-2">
                                                    <h5 class="mb-0 mr-2">{{ $comment->name }}</h5>
                                                    <span class="badge badge-{{ $comment->status === 'approved' ? 'success' : ($comment->status === 'pending' ? 'warning' : 'danger') }}">
                                                        {{ ucfirst($comment->status) }}
                                                    </span>
                                                    @if($comment->user_id)
                                                        <span class="badge badge-info ml-1">Registered User</span>
                                                    @endif
                                                </div>
                                                <div class="text-muted mb-2">
                                                    <i class="fas fa-envelope"></i> {{ $comment->email }}
                                                    @if($comment->website)
                                                        | <i class="fas fa-globe"></i> <a href="{{ $comment->website }}" target="_blank" class="text-info">{{ $comment->website }}</a>
                                                    @endif
                                                    <br>
                                                    <i class="fas fa-clock"></i> {{ $comment->created_at->format('F d, Y \a\t H:i') }}
                                                    @if($comment->ip_address)
                                                        | <i class="fas fa-map-marker-alt"></i> {{ $comment->ip_address }}
                                                    @endif
                                                </div>
                                                <div class="comment-content">
                                                    {!! nl2br(e($comment->comment)) !!}
                                                </div>
                                                @if($comment->parent_id)
                                                    <div class="mt-3 p-3 bg-white border-left border-primary">
                                                        <small class="text-muted"><i class="fas fa-reply"></i> In reply to:</small>
                                                        <div class="mt-1">
                                                            <strong>{{ $comment->parent->name }}</strong>
                                                            <small class="text-muted">({{ $comment->parent->created_at->format('M d, Y') }})</small>
                                                        </div>
                                                        <div class="text-muted">{{ Str::limit($comment->parent->comment, 100) }}</div>
                                                        <a href="{{ route('admin.comments.show', $comment->parent) }}" class="btn btn-sm btn-outline-primary mt-2">
                                                            <i class="fas fa-eye"></i> View Parent Comment
                                                        </a>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Post Information -->
                            @if($comment->post)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-file-alt"></i> Related Post
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        @if($comment->post->featured_image)
                                            <img src="{{ asset('storage/' . $comment->post->featured_image) }}"
                                                 alt="{{ $comment->post->title }}"
                                                 class="img-thumbnail mr-3"
                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                        @endif
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="{{ route('admin.posts.show', $comment->post) }}" class="text-decoration-none">
                                                    {{ $comment->post->title }}
                                                </a>
                                            </h6>
                                            <p class="text-muted mb-2">{{ Str::limit($comment->post->excerpt, 100) }}</p>
                                            <div class="d-flex align-items-center text-sm text-muted">
                                                <span class="mr-3">
                                                    <i class="fas fa-user"></i> {{ $comment->post->author->name ?? 'Unknown' }}
                                                </span>
                                                <span class="mr-3">
                                                    <i class="fas fa-calendar"></i> {{ $comment->post->published_at ? $comment->post->published_at->format('M d, Y') : 'Draft' }}
                                                </span>
                                                <span class="mr-3">
                                                    <i class="fas fa-comments"></i> {{ $comment->post->comments->count() }} comments
                                                </span>
                                                <span>
                                                    <i class="fas fa-eye"></i> {{ $comment->post->views ?? 0 }} views
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Replies Section -->
                            @if($comment->replies->count() > 0)
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-comments"></i> Replies ({{ $comment->replies->count() }})
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @foreach($comment->replies as $reply)
                                    <div class="reply-item border-left border-primary pl-4 mb-4">
                                        <div class="d-flex align-items-start">
                                            <div class="avatar-placeholder bg-{{ $reply->status === 'approved' ? 'success' : ($reply->status === 'pending' ? 'warning' : 'danger') }} rounded-circle d-flex align-items-center justify-content-center text-white mr-3" style="width: 40px; height: 40px; min-width: 40px;">
                                                @if($reply->user_id)
                                                    <i class="fas fa-user-check"></i>
                                                @else
                                                    <i class="fas fa-user"></i>
                                                @endif
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <div class="d-flex align-items-center mb-1">
                                                            <strong class="mr-2">{{ $reply->name }}</strong>
                                                            <span class="badge badge-{{ $reply->status === 'approved' ? 'success' : ($reply->status === 'pending' ? 'warning' : 'danger') }} badge-sm">
                                                                {{ ucfirst($reply->status) }}
                                                            </span>
                                                            @if($reply->user_id)
                                                                <span class="badge badge-info badge-sm ml-1">Registered</span>
                                                            @endif
                                                        </div>
                                                        <div class="text-muted small mb-2">
                                                            <i class="fas fa-envelope"></i> {{ $reply->email }}
                                                            | <i class="fas fa-clock"></i> {{ $reply->created_at->format('M d, Y H:i') }}
                                                        </div>
                                                    </div>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('admin.comments.show', $reply) }}" class="btn btn-sm btn-outline-info" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('admin.comments.edit', $reply) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        @if($reply->status === 'pending')
                                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="changeReplyStatus({{ $reply->id }}, 'approved')" title="Approve">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="reply-content">
                                                    {!! nl2br(e($reply->comment)) !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Right Column - Actions & Info -->
                        <div class="col-md-4">
                            <!-- Quick Actions -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group-vertical d-block">
                                        <a href="{{ route('admin.comments.edit', $comment) }}" class="btn btn-warning btn-block mb-2">
                                            <i class="fas fa-edit"></i> Edit Comment
                                        </a>

                                        @if($comment->status !== 'approved')
                                            <button type="button" class="btn btn-success btn-block mb-2" onclick="changeStatus('approved')">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        @endif

                                        @if($comment->status !== 'rejected')
                                            <button type="button" class="btn btn-danger btn-block mb-2" onclick="changeStatus('rejected')">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        @endif

                                        @if($comment->status !== 'pending')
                                            <button type="button" class="btn btn-warning btn-block mb-2" onclick="changeStatus('pending')">
                                                <i class="fas fa-clock"></i> Mark as Pending
                                            </button>
                                        @endif

                                        @if($comment->post)
                                            <a href="{{ route('posts.show', $comment->post->slug) }}" target="_blank" class="btn btn-info btn-block mb-2">
                                                <i class="fas fa-external-link-alt"></i> View on Site
                                            </a>
                                        @endif

                                        <button type="button" class="btn btn-outline-danger btn-block" onclick="deleteComment()">
                                            <i class="fas fa-trash"></i> Delete Comment
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Comment Information -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Comment Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge badge-{{ $comment->status === 'approved' ? 'success' : ($comment->status === 'pending' ? 'warning' : 'danger') }}">
                                                    {{ ucfirst($comment->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Author:</strong></td>
                                            <td>{{ $comment->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td>{{ $comment->email }}</td>
                                        </tr>
                                        @if($comment->website)
                                        <tr>
                                            <td><strong>Website:</strong></td>
                                            <td><a href="{{ $comment->website }}" target="_blank" class="text-info">{{ $comment->website }}</a></td>
                                        </tr>
                                        @endif
                                        @if($comment->user_id)
                                        <tr>
                                            <td><strong>User:</strong></td>
                                            <td>
                                                <a href="{{ route('admin.users.show', $comment->user) }}" class="text-info">
                                                    {{ $comment->user->name }}
                                                </a>
                                            </td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td><strong>Post:</strong></td>
                                            <td>
                                                @if($comment->post)
                                                    <a href="{{ route('admin.posts.show', $comment->post) }}" class="text-info">
                                                        {{ Str::limit($comment->post->title, 30) }}
                                                    </a>
                                                @else
                                                    <span class="text-muted">Post deleted</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @if($comment->parent_id)
                                        <tr>
                                            <td><strong>Reply to:</strong></td>
                                            <td>
                                                <a href="{{ route('admin.comments.show', $comment->parent) }}" class="text-info">
                                                    {{ Str::limit($comment->parent->comment, 30) }}
                                                </a>
                                            </td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td>{{ $comment->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Updated:</strong></td>
                                            <td>{{ $comment->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Technical Information -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Technical Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>IP Address:</strong></td>
                                            <td>{{ $comment->ip_address ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>User Agent:</strong></td>
                                            <td>
                                                @if($comment->user_agent)
                                                    <small class="text-muted">{{ Str::limit($comment->user_agent, 50) }}</small>
                                                    <button type="button" class="btn btn-xs btn-outline-info ml-1" onclick="showUserAgent()">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                @else
                                                    N/A
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Comment ID:</strong></td>
                                            <td><code>{{ $comment->id }}</code></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="border-right">
                                                <h4 class="mb-0 text-primary">{{ $comment->replies->count() }}</h4>
                                                <small class="text-muted">Replies</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="mb-0 text-info">{{ strlen($comment->comment) }}</h4>
                                            <small class="text-muted">Characters</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <small class="text-muted">
                                            Age: {{ $comment->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this comment?</p>
                @if($comment->replies->count() > 0)
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> This will also delete {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }} to this comment.
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="{{ route('admin.comments.destroy', $comment) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- User Agent Modal -->
<div class="modal fade" id="userAgentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Agent Information</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Full User Agent String:</label>
                    <textarea class="form-control" rows="4" readonly>{{ $comment->user_agent }}</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-placeholder {
    font-size: 1.2rem;
}

.comment-display {
    background: #f8f9fa;
}

.comment-content {
    font-size: 1rem;
    line-height: 1.6;
    color: #495057;
}

.reply-item {
    background: #fff;
    border-radius: 0.25rem;
    padding: 1rem;
}

.reply-content {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #6c757d;
}

.border-left {
    border-left: 3px solid #2678a1db !important;
}

.badge-sm {
    font-size: 0.7rem;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
}
</style>
@endpush

@push('scripts')
<script>
// Change comment status
function changeStatus(status) {
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);

    if (!confirm(`Are you sure you want to mark this comment as ${statusText}?`)) {
        return;
    }

    $.ajax({
        url: `/admin/comments/{{ $comment->id }}/${status}`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating comment status');
            }
        },
        error: function() {
            alert('Error updating comment status');
        }
    });
}

// Change reply status
function changeReplyStatus(replyId, status) {
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);

    if (!confirm(`Are you sure you want to mark this reply as ${statusText}?`)) {
        return;
    }

    $.ajax({
        url: `/admin/comments/${replyId}/${status}`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating reply status');
            }
        },
        error: function() {
            alert('Error updating reply status');
        }
    });
}

// Delete comment
function deleteComment() {
    $('#deleteModal').modal('show');
}

// Show user agent
function showUserAgent() {
    $('#userAgentModal').modal('show');
}
</script>
@endpush
