@extends('layouts.admin')

@section('title', 'Admin Users Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Admin Users</h3>
                    <a href="{{ route('admin.admins.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Admin
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.admins.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="Search admins..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="role" class="form-control">
                                    <option value="">All Roles</option>
                                    <option value="super_admin" {{ request('role') == 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                                    <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                                    <option value="moderator" {{ request('role') == 'moderator' ? 'selected' : '' }}>Moderator</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-secondary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.admins.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <select class="form-select me-2" id="bulkAction" style="width: auto;">
                                    <option value="">Bulk Actions</option>
                                    <option value="activate">Activate</option>
                                    <option value="deactivate">Deactivate</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button class="btn btn-outline-primary" id="applyBulkAction">Apply</button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                Showing {{ $admins->firstItem() ?? 0 }} to {{ $admins->lastItem() ?? 0 }} of {{ $admins->total() }} admins
                            </small>
                        </div>
                    </div>

                    <!-- Admins Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>Admin</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($admins as $admin)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="admin-checkbox" value="{{ $admin->id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                @if($admin->avatar)
                                                    <img src="{{ asset('storage/' . $admin->avatar) }}" 
                                                         alt="{{ $admin->name }}" 
                                                         class="rounded-circle" 
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                @else
                                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold" 
                                                         style="width: 40px; height: 40px;">
                                                        {{ strtoupper(substr($admin->name, 0, 2)) }}
                                                    </div>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $admin->name }}</h6>
                                                <small class="text-muted">ID: {{ $admin->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $admin->email }}</span>
                                        @if($admin->email_verified_at)
                                            <i class="fas fa-check-circle text-success ms-1" title="Email Verified"></i>
                                        @else
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="Email Not Verified"></i>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $admin->role_badge_class }}">
                                            {{ $admin->role_label }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $admin->status_badge_class }}">
                                            {{ $admin->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($admin->last_login_at)
                                            <span title="{{ $admin->last_login_at->format('Y-m-d H:i:s') }}">
                                                {{ $admin->last_login_at->diffForHumans() }}
                                            </span>
                                        @else
                                            <span class="text-muted">Never</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.admins.show', $admin) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.admins.edit', $admin) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($admin->id !== auth()->id())
                                                <button class="btn btn-sm btn-outline-{{ $admin->status === 'active' ? 'warning' : 'success' }}" 
                                                        onclick="toggleStatus({{ $admin->id }})"
                                                        title="{{ $admin->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                                    <i class="fas fa-{{ $admin->status === 'active' ? 'user-slash' : 'user-check' }}"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteAdmin({{ $admin->id }})"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @else
                                                <span class="badge bg-info">Current User</span>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-users-cog fa-3x mb-3"></i>
                                            <h5>No admin users found</h5>
                                            <p>Start by creating your first admin user.</p>
                                            <a href="{{ route('admin.admins.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>Create Admin
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($admins->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $admins->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.admin-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Toggle admin status
function toggleStatus(adminId) {
    if (confirm('Are you sure you want to change the status of this admin?')) {
        fetch(`/admin/admins/${adminId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating admin status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating admin status');
        });
    }
}

// Delete admin
function deleteAdmin(adminId) {
    if (confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/admins/${adminId}`;
        form.innerHTML = `
            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
            <input type="hidden" name="_method" value="DELETE">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk actions
document.getElementById('applyBulkAction').addEventListener('click', function() {
    const action = document.getElementById('bulkAction').value;
    const selectedAdmins = Array.from(document.querySelectorAll('.admin-checkbox:checked')).map(cb => cb.value);
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    if (selectedAdmins.length === 0) {
        alert('Please select at least one admin');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedAdmins.length} admin(s)?`)) {
        fetch('/admin/admins/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                admins: selectedAdmins
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error performing bulk action');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error performing bulk action');
        });
    }
});
</script>
@endpush
@endsection
