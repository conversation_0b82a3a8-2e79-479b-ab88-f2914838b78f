@extends('layouts.admin')

@section('title', 'Category Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Category: {{ $category->name }}</h3>
                    <div>
                        <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Categories
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="150">Name:</th>
                                            <td>{{ $category->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Slug:</th>
                                            <td><code>{{ $category->slug }}</code></td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                <span class="badge badge-{{ $category->status === 'active' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($category->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Sort Order:</th>
                                            <td>{{ $category->sort_order }}</td>
                                        </tr>
                                        <tr>
                                            <th>Posts Count:</th>
                                            <td>
                                                <span class="badge badge-info">{{ $category->posts->count() }}</span>
                                                @if($category->posts->count() > 0)
                                                    <a href="#posts-section" class="btn btn-sm btn-outline-primary ml-2">View Posts</a>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Created:</th>
                                            <td>{{ $category->created_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>Updated:</th>
                                            <td>{{ $category->updated_at->format('M d, Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Description -->
                            @if($category->description)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Description</h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ $category->description }}</p>
                                </div>
                            </div>
                            @endif

                            <!-- SEO Information -->
                            @if($category->meta_title || $category->meta_description)
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Information</h5>
                                </div>
                                <div class="card-body">
                                    @if($category->meta_title)
                                    <div class="mb-3">
                                        <strong>Meta Title:</strong><br>
                                        <span class="text-muted">{{ $category->meta_title }}</span>
                                    </div>
                                    @endif
                                    @if($category->meta_description)
                                    <div>
                                        <strong>Meta Description:</strong><br>
                                        <span class="text-muted">{{ $category->meta_description }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>

                        <div class="col-md-4">
                            <!-- Category Image -->
                            @if($category->image)
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Category Image</h5>
                                </div>
                                <div class="card-body text-center">
                                    <img src="{{ $category->imageUrl }}" class="img-fluid rounded" style="max-height: 300px;">
                                </div>
                            </div>
                            @endif

                            <!-- Quick Actions -->
                            <div class="card {{ $category->image ? 'mt-3' : '' }}">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> Edit Category
                                        </a>
                                        
                                        <button type="button" class="btn btn-{{ $category->status === 'active' ? 'secondary' : 'success' }} btn-sm" 
                                                onclick="toggleStatus({{ $category->id }})">
                                            <i class="fas fa-{{ $category->status === 'active' ? 'eye-slash' : 'eye' }}"></i> 
                                            {{ $category->status === 'active' ? 'Deactivate' : 'Activate' }}
                                        </button>
                                        
                                        @if($category->posts->count() === 0)
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteCategory({{ $category->id }})">
                                            <i class="fas fa-trash"></i> Delete Category
                                        </button>
                                        @else
                                        <button type="button" class="btn btn-danger btn-sm" disabled title="Cannot delete category with posts">
                                            <i class="fas fa-trash"></i> Delete Category
                                        </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Posts Section -->
                    @if($category->posts->count() > 0)
                    <div class="row mt-4" id="posts-section">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Posts in this Category ({{ $category->posts->count() }})</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Author</th>
                                                    <th>Status</th>
                                                    <th>Published</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($category->posts->take(10) as $post)
                                                <tr>
                                                    <td>
                                                        <a href="{{ route('admin.posts.show', $post) }}" class="text-decoration-none">
                                                            {{ $post->title }}
                                                        </a>
                                                        @if($post->featured)
                                                            <span class="badge badge-warning ml-1">Featured</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $post->author->name ?? 'Unknown' }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning') }}">
                                                            {{ ucfirst($post->status) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($post->published_at)
                                                            {{ $post->published_at->format('M d, Y') }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('admin.posts.show', $post) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                    @if($category->posts->count() > 10)
                                    <div class="text-center mt-3">
                                        <a href="{{ route('admin.posts.index', ['category' => $category->id]) }}" class="btn btn-primary">
                                            View All Posts ({{ $category->posts->count() }})
                                        </a>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this category? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleStatus(categoryId) {
    $.ajax({
        url: `/admin/categories/${categoryId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

function deleteCategory(categoryId) {
    $('#deleteForm').attr('action', `/admin/categories/${categoryId}`);
    $('#deleteModal').modal('show');
}
</script>
@endpush