<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Faq::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('question', 'like', '%' . $request->search . '%')
                  ->orWhere('answer', 'like', '%' . $request->search . '%')
                  ->orWhere('category', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } else {
                $query->where('status', $request->status);
            }
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by featured
        if ($request->filled('featured')) {
            if ($request->featured === '1') {
                $query->featured();
            } else {
                $query->where('featured', false);
            }
        }

        $faqs = $query->ordered()->paginate(15);
        $categories = Faq::distinct()->pluck('category')->filter();

        return view('admin.faqs.index', compact('faqs', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Faq::distinct()->pluck('category')->filter();
        return view('admin.faqs.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Set default sort order if not provided
        if (empty($validated['sort_order'])) {
            $validated['sort_order'] = Faq::max('sort_order') + 1;
        }

        Faq::create($validated);

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Faq $faq)
    {
        return view('admin.faqs.show', compact('faq'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Faq $faq)
    {
        $categories = Faq::distinct()->pluck('category')->filter();
        return view('admin.faqs.edit', compact('faq', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Faq $faq)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'nullable|string|max:100',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        $faq->update($validated);

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Faq $faq)
    {
        $faq->delete();

        return redirect()->route('admin.faqs.index')
                        ->with('success', 'FAQ deleted successfully.');
    }

    /**
     * Toggle FAQ status
     */
    public function toggleStatus(Faq $faq)
    {
        $faq->update([
            'status' => $faq->status === 'active' ? 'inactive' : 'active'
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'FAQ status updated successfully.',
            'status' => $faq->status
        ]);
    }

    /**
     * Toggle FAQ featured status
     */
    public function toggleFeatured(Faq $faq)
    {
        $faq->update([
            'featured' => !$faq->featured
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'FAQ featured status updated successfully.',
            'featured' => $faq->featured
        ]);
    }

    /**
     * Update FAQ sort order
     */
    public function updateSortOrder(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:faqs,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($validated['items'] as $item) {
            Faq::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'FAQ order updated successfully.'
        ]);
    }

    /**
     * Bulk action for FAQs
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete,update_category',
            'faq_ids' => 'required|array',
            'faq_ids.*' => 'exists:faqs,id',
            'category' => 'required_if:action,update_category|nullable|string|max:100'
        ]);

        $faqs = Faq::whereIn('id', $validated['faq_ids']);
        
        switch ($validated['action']) {
            case 'activate':
                $faqs->update(['status' => 'active']);
                $message = 'FAQs activated successfully.';
                break;
            case 'deactivate':
                $faqs->update(['status' => 'inactive']);
                $message = 'FAQs deactivated successfully.';
                break;
            case 'feature':
                $faqs->update(['featured' => true]);
                $message = 'FAQs marked as featured successfully.';
                break;
            case 'unfeature':
                $faqs->update(['featured' => false]);
                $message = 'FAQs unmarked as featured successfully.';
                break;
            case 'update_category':
                $faqs->update(['category' => $validated['category']]);
                $message = 'FAQ category updated successfully.';
                break;
            case 'delete':
                $faqs->delete();
                $message = 'FAQs deleted successfully.';
                break;
        }

        return redirect()->route('admin.faqs.index')
                        ->with('success', $message);
    }

    /**
     * Duplicate FAQ
     */
    public function duplicate(Faq $faq)
    {
        $newFaq = $faq->replicate();
        $newFaq->question = $faq->question . ' (Copy)';
        $newFaq->status = 'inactive';
        $newFaq->featured = false;
        $newFaq->sort_order = Faq::max('sort_order') + 1;
        $newFaq->save();

        return redirect()->route('admin.faqs.edit', $newFaq)
                        ->with('success', 'FAQ duplicated successfully.');
    }
}
