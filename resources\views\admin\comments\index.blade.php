@extends('layouts.admin')

@section('title', 'Comments Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Comments Management</h3>
                    <a href="{{ route('admin.comments.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Comment
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="{{ route('admin.comments.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ request('search') }}" placeholder="Search by name, email, or comment...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="post">Post</label>
                                    <select class="form-control" id="post" name="post">
                                        <option value="">All Posts</option>
                                        @foreach($posts as $post)
                                            <option value="{{ $post->id }}" {{ request('post') == $post->id ? 'selected' : '' }}>
                                                {{ Str::limit($post->title, 40) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="sort">Sort By</label>
                                    <select class="form-control" id="sort" name="sort">
                                        <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Date</option>
                                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                                        <option value="status" {{ request('sort') === 'status' ? 'selected' : '' }}>Status</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-search"></i> Filter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-comments"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Comments</span>
                                    <span class="info-box-number">{{ $stats['total'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Approved</span>
                                    <span class="info-box-number">{{ $stats['approved'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Pending</span>
                                    <span class="info-box-number">{{ $stats['pending'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Rejected</span>
                                    <span class="info-box-number">{{ $stats['rejected'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <form id="bulkForm" method="POST" action="{{ route('admin.comments.bulk-action') }}">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="action" class="form-control" required>
                                        <option value="">Select Bulk Action</option>
                                        <option value="approve">Approve</option>
                                        <option value="reject">Reject</option>
                                        <option value="pending">Mark as Pending</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted">{{ $comments->total() }} total comments</span>
                            </div>
                        </div>

                        <!-- Comments Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Author</th>
                                        <th>Comment</th>
                                        <th>Post</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($comments as $comment)
                                    <tr class="{{ $comment->status === 'pending' ? 'table-warning' : ($comment->status === 'rejected' ? 'table-danger' : '') }}">
                                        <td>
                                            <input type="checkbox" name="selected_ids[]" value="{{ $comment->id }}" class="comment-checkbox">
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $comment->name }}</strong>
                                                @if($comment->user_id)
                                                    <span class="badge badge-info">Registered</span>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $comment->email }}</small>
                                            @if($comment->website)
                                                <br><small><a href="{{ $comment->website }}" target="_blank" class="text-info">{{ $comment->website }}</a></small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="comment-preview">
                                                {{ Str::limit($comment->comment, 100) }}
                                            </div>
                                            @if($comment->parent_id)
                                                <small class="text-muted">
                                                    <i class="fas fa-reply"></i> Reply to: {{ Str::limit($comment->parent->comment ?? '', 30) }}
                                                </small>
                                            @endif
                                            @if($comment->replies->count() > 0)
                                                <br><small class="text-info">
                                                    <i class="fas fa-comments"></i> {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($comment->post)
                                                <a href="{{ route('admin.posts.show', $comment->post) }}" class="text-decoration-none">
                                                    {{ Str::limit($comment->post->title, 30) }}
                                                </a>
                                            @else
                                                <span class="text-muted">Post deleted</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if($comment->status === 'approved')
                                                    <button type="button" class="btn btn-xs btn-success" onclick="changeStatus({{ $comment->id }}, 'pending')" title="Mark as Pending">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @elseif($comment->status === 'pending')
                                                    <button type="button" class="btn btn-xs btn-warning" onclick="changeStatus({{ $comment->id }}, 'approved')" title="Approve">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-xs btn-danger" onclick="changeStatus({{ $comment->id }}, 'approved')" title="Approve">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $comment->created_at->format('M d, Y') }}</small>
                                            <br><small class="text-muted">{{ $comment->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.comments.show', $comment) }}" class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.comments.edit', $comment) }}" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($comment->status === 'pending')
                                                    <button type="button" class="btn btn-sm btn-success" onclick="changeStatus({{ $comment->id }}, 'approved')" title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="changeStatus({{ $comment->id }}, 'rejected')" title="Reject">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteComment({{ $comment->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-comments fa-3x mb-3"></i>
                                                <h5>No comments found</h5>
                                                <p>No comments match your current filters.</p>
                                                @if(request()->hasAny(['search', 'status', 'post']))
                                                    <a href="{{ route('admin.comments.index') }}" class="btn btn-primary">
                                                        <i class="fas fa-refresh"></i> Clear Filters
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    @if($comments->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $comments->firstItem() }} to {{ $comments->lastItem() }} of {{ $comments->total() }} results
                            </p>
                        </div>
                        <div>
                            {{ $comments->appends(request()->query())->links() }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this comment?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This will also delete all replies to this comment.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-box-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
}

.comment-preview {
    max-width: 300px;
    word-wrap: break-word;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
// Select All functionality
$('#selectAll').change(function() {
    $('.comment-checkbox').prop('checked', this.checked);
});

$('.comment-checkbox').change(function() {
    if (!this.checked) {
        $('#selectAll').prop('checked', false);
    } else if ($('.comment-checkbox:checked').length === $('.comment-checkbox').length) {
        $('#selectAll').prop('checked', true);
    }
});

// Bulk action confirmation
function confirmBulkAction() {
    const selectedComments = $('.comment-checkbox:checked').length;
    const action = $('select[name="action"]').val();
    
    if (selectedComments === 0) {
        alert('Please select at least one comment.');
        return false;
    }
    
    if (action === '') {
        alert('Please select an action.');
        return false;
    }
    
    const actionText = action === 'delete' ? 'delete' : action;
    return confirm(`Are you sure you want to ${actionText} ${selectedComments} selected comment(s)?`);
}

// Change comment status
function changeStatus(commentId, status) {
    const statusText = status.charAt(0).toUpperCase() + status.slice(1);
    
    if (!confirm(`Are you sure you want to mark this comment as ${statusText}?`)) {
        return;
    }
    
    $.ajax({
        url: `/admin/comments/${commentId}/${status}`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating comment status');
            }
        },
        error: function() {
            alert('Error updating comment status');
        }
    });
}

// Delete comment
function deleteComment(commentId) {
    $('#deleteForm').attr('action', `/admin/comments/${commentId}`);
    $('#deleteModal').modal('show');
}

// Auto-submit form on filter change
$('#status, #post, #sort').change(function() {
    $(this).closest('form').submit();
});

// Clear search on escape
$('#search').keyup(function(e) {
    if (e.keyCode === 27) { // Escape key
        $(this).val('');
    }
});

// Initialize Select2 for post filter
$('#post').select2({
    placeholder: 'Select a post...',
    allowClear: true,
    width: '100%'
});
</script>
@endpush