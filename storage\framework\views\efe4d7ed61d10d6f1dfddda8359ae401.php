<?php $__env->startSection('title', 'Newsletter Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Newsletter Management</h3>
                    <div>
                        <a href="<?php echo e(route('admin.newsletters.create')); ?>" class="btn btn-primary mr-2">
                            <i class="fas fa-plus"></i> Add Subscriber
                        </a>
                        <a href="<?php echo e(route('admin.newsletters.export')); ?>" class="btn btn-success">
                            <i class="fas fa-download"></i> Export
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" action="<?php echo e(route('admin.newsletters.index')); ?>" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">Search</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="<?php echo e(request('search')); ?>" placeholder="Search by email or name...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                        <option value="unsubscribed" <?php echo e(request('status') === 'unsubscribed' ? 'selected' : ''); ?>>Unsubscribed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="verified">Verification</label>
                                    <select class="form-control" id="verified" name="verified">
                                        <option value="">All</option>
                                        <option value="1" <?php echo e(request('verified') === '1' ? 'selected' : ''); ?>>Verified</option>
                                        <option value="0" <?php echo e(request('verified') === '0' ? 'selected' : ''); ?>>Unverified</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="date_range">Date Range</label>
                                    <select class="form-control" id="date_range" name="date_range">
                                        <option value="">All Time</option>
                                        <option value="today" <?php echo e(request('date_range') === 'today' ? 'selected' : ''); ?>>Today</option>
                                        <option value="week" <?php echo e(request('date_range') === 'week' ? 'selected' : ''); ?>>This Week</option>
                                        <option value="month" <?php echo e(request('date_range') === 'month' ? 'selected' : ''); ?>>This Month</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="sort">Sort By</label>
                                    <select class="form-control" id="sort" name="sort">
                                        <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Date Subscribed</option>
                                        <option value="email" <?php echo e(request('sort') === 'email' ? 'selected' : ''); ?>>Email</option>
                                        <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                                        <option value="status" <?php echo e(request('sort') === 'status' ? 'selected' : ''); ?>>Status</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Subscribers</span>
                                    <span class="info-box-number"><?php echo e($stats['total'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Active</span>
                                    <span class="info-box-number"><?php echo e($stats['active'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-envelope"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Unverified</span>
                                    <span class="info-box-number"><?php echo e($stats['unverified'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-danger"><i class="fas fa-user-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Unsubscribed</span>
                                    <span class="info-box-number"><?php echo e($stats['unsubscribed'] ?? 0); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <form id="bulkForm" method="POST" action="<?php echo e(route('admin.newsletters.bulk-action')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="action" class="form-control" required>
                                        <option value="">Select Bulk Action</option>
                                        <option value="activate">Activate</option>
                                        <option value="deactivate">Deactivate</option>
                                        <option value="verify">Mark as Verified</option>
                                        <option value="unverify">Mark as Unverified</option>
                                        <option value="unsubscribe">Unsubscribe</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <span class="text-muted"><?php echo e($newsletters->total()); ?> total subscribers</span>
                            </div>
                        </div>

                        <!-- Newsletters Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Subscriber Info</th>
                                        <th>Status</th>
                                        <th>Verification</th>
                                        <th>Subscribed Date</th>
                                        <th>Last Activity</th>
                                        <th width="150">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $newsletters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newsletter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="<?php echo e(!$newsletter->is_verified ? 'table-warning' : ''); ?>">
                                        <td>
                                            <input type="checkbox" name="selected_ids[]" value="<?php echo e($newsletter->id); ?>" class="newsletter-checkbox">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="avatar-circle">
                                                        <?php echo e(strtoupper(substr($newsletter->email, 0, 1))); ?>

                                                    </div>
                                                </div>
                                                <div>
                                                    <?php if($newsletter->name): ?>
                                                        <strong><?php echo e($newsletter->name); ?></strong>
                                                        <br>
                                                    <?php endif; ?>
                                                    <span class="text-muted"><?php echo e($newsletter->email); ?></span>
                                                    <?php if($newsletter->preferences): ?>
                                                        <br><small class="text-info"><?php echo e(implode(', ', json_decode($newsletter->preferences, true) ?? [])); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <span class="badge badge-<?php echo e($newsletter->status === 'active' ? 'success' : ($newsletter->status === 'inactive' ? 'warning' : 'danger')); ?>">
                                                    <?php echo e(ucfirst($newsletter->status)); ?>

                                                </span>
                                            </div>
                                            <div class="mt-1">
                                                <button type="button" class="btn btn-sm btn-outline-<?php echo e($newsletter->status === 'active' ? 'warning' : 'success'); ?>"
                                                        onclick="toggleStatus(<?php echo e($newsletter->id); ?>)">
                                                    <?php echo e($newsletter->status === 'active' ? 'Deactivate' : 'Activate'); ?>

                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($newsletter->is_verified): ?>
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check"></i> Verified
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-clock"></i> Unverified
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if(!$newsletter->is_verified): ?>
                                                <div class="mt-1">
                                                    <button type="button" class="btn btn-sm btn-outline-success"
                                                            onclick="verifySubscriber(<?php echo e($newsletter->id); ?>)">
                                                        Verify
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($newsletter->created_at->format('M d, Y')); ?></small>
                                            <br><small class="text-muted"><?php echo e($newsletter->created_at->format('H:i')); ?></small>
                                            <br><small class="text-info"><?php echo e($newsletter->created_at->diffForHumans()); ?></small>
                                        </td>
                                        <td>
                                            <?php if($newsletter->last_activity_at): ?>
                                                <small><?php echo e($newsletter->last_activity_at->format('M d, Y')); ?></small>
                                                <br><small class="text-muted"><?php echo e($newsletter->last_activity_at->diffForHumans()); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">No activity</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.newsletters.show', $newsletter)); ?>" class="btn btn-sm btn-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.newsletters.edit', $newsletter)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-<?php echo e($newsletter->status === 'active' ? 'warning' : 'success'); ?>"
                                                        onclick="toggleStatus(<?php echo e($newsletter->id); ?>)"
                                                        title="<?php echo e($newsletter->status === 'active' ? 'Deactivate' : 'Activate'); ?>">
                                                    <i class="fas fa-<?php echo e($newsletter->status === 'active' ? 'pause' : 'play'); ?>"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteNewsletter(<?php echo e($newsletter->id); ?>)" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-envelope fa-3x mb-3"></i>
                                                <h5>No subscribers found</h5>
                                                <p>No newsletter subscribers match your current filters.</p>
                                                <?php if(request()->hasAny(['search', 'status', 'verified', 'date_range'])): ?>
                                                    <a href="<?php echo e(route('admin.newsletters.index')); ?>" class="btn btn-primary">
                                                        <i class="fas fa-refresh"></i> Clear Filters
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    <?php if($newsletters->hasPages()): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="text-muted mb-0">
                                Showing <?php echo e($newsletters->firstItem()); ?> to <?php echo e($newsletters->lastItem()); ?> of <?php echo e($newsletters->total()); ?> results
                            </p>
                        </div>
                        <div>
                            <?php echo e($newsletters->appends(request()->query())->links()); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this newsletter subscription?</p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.25rem;
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
    margin-bottom: 1rem;
}

.info-box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 0.25rem;
    color: #fff;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.info-box-content {
    flex: 1;
}

.info-box-text {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-box-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #2678a1db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Select All functionality
$('#selectAll').change(function() {
    $('.newsletter-checkbox').prop('checked', this.checked);
});

$('.newsletter-checkbox').change(function() {
    if (!this.checked) {
        $('#selectAll').prop('checked', false);
    } else if ($('.newsletter-checkbox:checked').length === $('.newsletter-checkbox').length) {
        $('#selectAll').prop('checked', true);
    }
});

// Bulk action confirmation
function confirmBulkAction() {
    const selectedNewsletters = $('.newsletter-checkbox:checked').length;
    const action = $('select[name="action"]').val();

    if (selectedNewsletters === 0) {
        alert('Please select at least one subscriber.');
        return false;
    }

    if (action === '') {
        alert('Please select an action.');
        return false;
    }

    const actionText = action.replace('_', ' ');
    return confirm(`Are you sure you want to ${actionText} ${selectedNewsletters} selected subscriber(s)?`);
}

// Toggle newsletter status
function toggleStatus(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating newsletter status');
            }
        },
        error: function() {
            alert('Error updating newsletter status');
        }
    });
}

// Verify subscriber
function verifySubscriber(newsletterId) {
    $.ajax({
        url: `/admin/newsletters/${newsletterId}/verify`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error verifying subscriber');
            }
        },
        error: function() {
            alert('Error verifying subscriber');
        }
    });
}

// Delete newsletter
function deleteNewsletter(newsletterId) {
    $('#deleteForm').attr('action', `/admin/newsletters/${newsletterId}`);
    $('#deleteModal').modal('show');
}

// Auto-submit form on filter change
$('#status, #verified, #date_range, #sort').change(function() {
    $(this).closest('form').submit();
});

// Clear search on escape
$('#search').keyup(function(e) {
    if (e.keyCode === 27) { // Escape key
        $(this).val('');
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\websites\pescot\resources\views/admin/newsletters/index.blade.php ENDPATH**/ ?>