/**
 * Settings JavaScript Module
 * Provides utilities for working with application settings on the frontend
 */

(function(window) {
    'use strict';

    // Settings utility object
    const Settings = {
        /**
         * Get a setting value by key
         * @param {string} key - Setting key
         * @param {*} defaultValue - Default value if setting not found
         * @returns {*} Setting value
         */
        get: function(key, defaultValue = null) {
            return window.appSettings && window.appSettings[key] !== undefined 
                ? window.appSettings[key] 
                : defaultValue;
        },

        /**
         * Check if a setting exists
         * @param {string} key - Setting key
         * @returns {boolean}
         */
        has: function(key) {
            return window.appSettings && window.appSettings[key] !== undefined;
        },

        /**
         * Get all settings
         * @returns {object} All settings
         */
        all: function() {
            return window.appSettings || {};
        },

        /**
         * Apply theme settings to the page
         */
        applyTheme: function() {
            if (!window.themeSettings) return;

            const root = document.documentElement;
            const theme = window.themeSettings;

            // Apply CSS custom properties
            Object.keys(theme).forEach(key => {
                const cssVar = '--' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
                if (theme[key]) {
                    root.style.setProperty(cssVar, theme[key]);
                }
            });
        },

        /**
         * Update social media links
         */
        updateSocialLinks: function() {
            if (!window.socialLinks) return;

            const socialLinks = window.socialLinks;
            
            // Update footer social links
            Object.keys(socialLinks).forEach(platform => {
                const links = document.querySelectorAll(`a[href*="${platform}"]`);
                links.forEach(link => {
                    if (socialLinks[platform]) {
                        link.href = socialLinks[platform];
                        link.style.display = '';
                    } else {
                        link.style.display = 'none';
                    }
                });
            });
        },

        /**
         * Update site information
         */
        updateSiteInfo: function() {
            if (!window.siteInfo) return;

            const siteInfo = window.siteInfo;

            // Update page title if needed
            if (siteInfo.name && !document.title.includes(siteInfo.name)) {
                document.title = siteInfo.name + ' - ' + document.title;
            }

            // Update logo if present
            if (siteInfo.logo) {
                const logos = document.querySelectorAll('img[alt*="logo"], .logo img');
                logos.forEach(logo => {
                    logo.src = siteInfo.logo;
                    logo.alt = siteInfo.name;
                });
            }

            // Update favicon if present
            if (siteInfo.favicon) {
                const favicon = document.querySelector('link[rel="shortcut icon"]');
                if (favicon) {
                    favicon.href = siteInfo.favicon;
                }
            }
        },

        /**
         * Initialize settings on page load
         */
        init: function() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.applyTheme();
                    this.updateSocialLinks();
                    this.updateSiteInfo();
                });
            } else {
                this.applyTheme();
                this.updateSocialLinks();
                this.updateSiteInfo();
            }
        },

        /**
         * Refresh settings from server
         * @param {function} callback - Callback function
         */
        refresh: function(callback) {
            fetch('/admin/settings/api')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.appSettings = data.settings;
                        this.init();
                        if (callback) callback(null, data.settings);
                    } else {
                        if (callback) callback(new Error('Failed to refresh settings'));
                    }
                })
                .catch(error => {
                    console.error('Error refreshing settings:', error);
                    if (callback) callback(error);
                });
        },

        /**
         * Get theme color by name
         * @param {string} colorName - Color name (primary, secondary, accent, etc.)
         * @returns {string} Color value
         */
        getThemeColor: function(colorName) {
            const colorKey = 'theme_' + colorName + '_color';
            return this.get(colorKey);
        },

        /**
         * Check if maintenance mode is enabled
         * @returns {boolean}
         */
        isMaintenanceMode: function() {
            return this.get('site_maintenance_mode', false);
        },

        /**
         * Get social media URL by platform
         * @param {string} platform - Social media platform
         * @returns {string|null} URL or null
         */
        getSocialUrl: function(platform) {
            return window.socialLinks ? window.socialLinks[platform] : null;
        }
    };

    // Auto-initialize when script loads
    Settings.init();

    // Expose Settings to global scope
    window.Settings = Settings;

    // Also expose as a shorter alias
    window.settings = Settings;

})(window);

// Example usage:
// Settings.get('site_name', 'Default Site Name')
// Settings.getThemeColor('primary')
// Settings.getSocialUrl('facebook')
// Settings.refresh(function(error, settings) { ... })