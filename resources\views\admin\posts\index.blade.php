@extends('layouts.admin')

@section('title', 'Posts Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Posts Management</h3>
                    <a href="{{ route('admin.posts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Post
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="GET" action="{{ route('admin.posts.index') }}" class="form-inline">
                                <div class="form-group mr-3">
                                    <input type="text" name="search" class="form-control" placeholder="Search posts..." 
                                           value="{{ request('search') }}">
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="category" class="form-control">
                                        <option value="">All Categories</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="featured" class="form-control">
                                        <option value="">All Posts</option>
                                        <option value="1" {{ request('featured') == '1' ? 'selected' : '' }}>Featured Only</option>
                                        <option value="0" {{ request('featured') == '0' ? 'selected' : '' }}>Non-Featured</option>
                                    </select>
                                </div>
                                
                                <div class="form-group mr-3">
                                    <select name="admin_id" class="form-control">
                                        <option value="">All Authors</option>
                                        @foreach($admins as $admin)
                                            <option value="{{ $admin->id }}" {{ request('admin_id') == $admin->id ? 'selected' : '' }}>
                                                {{ $admin->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-secondary mr-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                
                                <a href="{{ route('admin.posts.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form id="bulkActionForm" method="POST" action="{{ route('admin.posts.bulk-action') }}">
                                @csrf
                                <div class="form-inline">
                                    <select name="action" class="form-control mr-2" required>
                                        <option value="">Bulk Actions</option>
                                        <option value="publish">Publish</option>
                                        <option value="draft">Move to Draft</option>
                                        <option value="pending">Move to Pending</option>
                                        <option value="feature">Mark as Featured</option>
                                        <option value="unfeature">Remove Featured</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                        Apply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Posts Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="30">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Author</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th>Published</th>
                                    <th>Views</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($posts as $post)
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_posts[]" value="{{ $post->id }}" class="post-checkbox">
                                    </td>
                                    <td>
                                        @if($post->featured_image)
                                            <img src="{{ $post->featuredImageUrl }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.posts.show', $post) }}" class="text-decoration-none font-weight-bold">
                                            {{ Str::limit($post->title, 50) }}
                                        </a>
                                        <br>
                                        <small class="text-muted">{{ $post->slug }}</small>
                                    </td>
                                    <td>
                                        @if($post->category)
                                            <span class="badge badge-info">{{ $post->category->name }}</span>
                                        @else
                                            <span class="text-muted">No Category</span>
                                        @endif
                                    </td>
                                    <td>{{ $post->author->name ?? 'Unknown' }}</td>
                                    <td>
                                        <span class="badge badge-{{ $post->status === 'published' ? 'success' : ($post->status === 'draft' ? 'secondary' : 'warning') }}">
                                            {{ ucfirst($post->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-{{ $post->featured ? 'warning' : 'outline-secondary' }}" 
                                                onclick="toggleFeatured({{ $post->id }})" title="{{ $post->featured ? 'Remove from Featured' : 'Mark as Featured' }}">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    </td>
                                    <td>
                                        @if($post->published_at)
                                            {{ $post->published_at->format('M d, Y') }}
                                        @else
                                            <span class="text-muted">Not Published</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-light">{{ $post->views ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.posts.show', $post) }}" class="btn btn-sm btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.posts.edit', $post) }}" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-{{ $post->status === 'published' ? 'secondary' : 'success' }}" 
                                                    onclick="toggleStatus({{ $post->id }})" 
                                                    title="{{ $post->status === 'published' ? 'Unpublish' : 'Publish' }}">
                                                <i class="fas fa-{{ $post->status === 'published' ? 'eye-slash' : 'eye' }}"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deletePost({{ $post->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No posts found.</p>
                                        <a href="{{ route('admin.posts.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Create First Post
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($posts->hasPages())
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <p class="text-muted mb-0">
                                Showing {{ $posts->firstItem() }} to {{ $posts->lastItem() }} of {{ $posts->total() }} results
                            </p>
                        </div>
                        <div>
                            {{ $posts->appends(request()->query())->links() }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this post? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Select all checkbox
    $('#selectAll').change(function() {
        $('.post-checkbox').prop('checked', $(this).prop('checked'));
    });

    // Individual checkbox change
    $('.post-checkbox').change(function() {
        if ($('.post-checkbox:checked').length === $('.post-checkbox').length) {
            $('#selectAll').prop('checked', true);
        } else {
            $('#selectAll').prop('checked', false);
        }
    });

    // Bulk action form submission
    $('#bulkActionForm').submit(function(e) {
        var selectedPosts = $('.post-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedPosts.length === 0) {
            e.preventDefault();
            alert('Please select at least one post.');
            return false;
        }

        // Add selected posts to form
        selectedPosts.forEach(function(postId) {
            $("<input>").attr({
                type: "hidden",
                name: "selected_posts[]",
                value: postId
            }).appendTo('#bulkActionForm');
        });
    });
});

function toggleStatus(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        },
        error: function() {
            alert('Error updating status');
        }
    });
}

function toggleFeatured(postId) {
    $.ajax({
        url: `/admin/posts/${postId}/toggle-featured`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error updating featured status');
            }
        },
        error: function() {
            alert('Error updating featured status');
        }
    });
}

function deletePost(postId) {
    $('#deleteForm').attr('action', `/admin/posts/${postId}`);
    $('#deleteModal').modal('show');
}

function confirmBulkAction() {
    var action = $('select[name="action"]').val();
    if (action === 'delete') {
        return confirm('Are you sure you want to delete the selected posts? This action cannot be undone.');
    }
    return true;
}
</script>
@endpush