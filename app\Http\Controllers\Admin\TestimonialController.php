<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TestimonialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Testimonial::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('position', 'like', '%' . $request->search . '%')
                  ->orWhere('company', 'like', '%' . $request->search . '%')
                  ->orWhere('testimonial', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } else {
                $query->where('status', $request->status);
            }
        }

        // Filter by featured
        if ($request->filled('featured')) {
            if ($request->featured === '1') {
                $query->featured();
            } else {
                $query->where('featured', false);
            }
        }

        // Filter by rating
        if ($request->filled('rating')) {
            $query->byRating($request->rating);
        }

        // Filter by high rated (4+ stars)
        if ($request->filled('high_rated') && $request->high_rated === '1') {
            $query->highRated();
        }

        $testimonials = $query->ordered()->paginate(15);

        return view('admin.testimonials.index', compact('testimonials'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.testimonials.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'testimonial' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('testimonials', 'public');
        }

        // Set default sort order if not provided
        if (empty($validated['sort_order'])) {
            $validated['sort_order'] = Testimonial::max('sort_order') + 1;
        }

        Testimonial::create($validated);

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Testimonial $testimonial)
    {
        return view('admin.testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Testimonial $testimonial)
    {
        return view('admin.testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Testimonial $testimonial)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'testimonial' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'status' => 'required|in:active,inactive',
            'featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0'
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($testimonial->image && Storage::disk('public')->exists($testimonial->image)) {
                Storage::disk('public')->delete($testimonial->image);
            }
            $validated['image'] = $request->file('image')->store('testimonials', 'public');
        }

        $testimonial->update($validated);

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Testimonial $testimonial)
    {
        // Delete image if exists
        if ($testimonial->image && Storage::disk('public')->exists($testimonial->image)) {
            Storage::disk('public')->delete($testimonial->image);
        }

        $testimonial->delete();

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial deleted successfully.');
    }

    /**
     * Toggle testimonial status
     */
    public function toggleStatus(Testimonial $testimonial)
    {
        $testimonial->update([
            'status' => $testimonial->status === 'active' ? 'inactive' : 'active'
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Testimonial status updated successfully.',
            'status' => $testimonial->status
        ]);
    }

    /**
     * Toggle testimonial featured status
     */
    public function toggleFeatured(Testimonial $testimonial)
    {
        $testimonial->update([
            'featured' => !$testimonial->featured
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Testimonial featured status updated successfully.',
            'featured' => $testimonial->featured
        ]);
    }

    /**
     * Update testimonial sort order
     */
    public function updateSortOrder(Request $request)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:testimonials,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($validated['items'] as $item) {
            Testimonial::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Testimonial order updated successfully.'
        ]);
    }

    /**
     * Bulk action for testimonials
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete,update_rating',
            'testimonial_ids' => 'required|array',
            'testimonial_ids.*' => 'exists:testimonials,id',
            'rating' => 'required_if:action,update_rating|integer|min:1|max:5'
        ]);

        $testimonials = Testimonial::whereIn('id', $validated['testimonial_ids']);
        
        switch ($validated['action']) {
            case 'activate':
                $testimonials->update(['status' => 'active']);
                $message = 'Testimonials activated successfully.';
                break;
            case 'deactivate':
                $testimonials->update(['status' => 'inactive']);
                $message = 'Testimonials deactivated successfully.';
                break;
            case 'feature':
                $testimonials->update(['featured' => true]);
                $message = 'Testimonials marked as featured successfully.';
                break;
            case 'unfeature':
                $testimonials->update(['featured' => false]);
                $message = 'Testimonials unmarked as featured successfully.';
                break;
            case 'update_rating':
                $testimonials->update(['rating' => $validated['rating']]);
                $message = 'Testimonial ratings updated successfully.';
                break;
            case 'delete':
                // Delete images before deleting testimonials
                $testimonialModels = $testimonials->get();
                foreach ($testimonialModels as $testimonial) {
                    if ($testimonial->image && Storage::disk('public')->exists($testimonial->image)) {
                        Storage::disk('public')->delete($testimonial->image);
                    }
                }
                $testimonials->delete();
                $message = 'Testimonials deleted successfully.';
                break;
        }

        return redirect()->route('admin.testimonials.index')
                        ->with('success', $message);
    }

    /**
     * Duplicate testimonial
     */
    public function duplicate(Testimonial $testimonial)
    {
        $newTestimonial = $testimonial->replicate();
        $newTestimonial->name = $testimonial->name . ' (Copy)';
        $newTestimonial->status = 'inactive';
        $newTestimonial->featured = false;
        $newTestimonial->sort_order = Testimonial::max('sort_order') + 1;
        
        // Copy image if exists
        if ($testimonial->image && Storage::disk('public')->exists($testimonial->image)) {
            $extension = pathinfo($testimonial->image, PATHINFO_EXTENSION);
            $newImagePath = 'testimonials/' . uniqid() . '.' . $extension;
            Storage::disk('public')->copy($testimonial->image, $newImagePath);
            $newTestimonial->image = $newImagePath;
        }
        
        $newTestimonial->save();

        return redirect()->route('admin.testimonials.edit', $newTestimonial)
                        ->with('success', 'Testimonial duplicated successfully.');
    }
}
