<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\Post;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Comment::with(['post', 'parent', 'replies']);

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('comment', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by post
        if ($request->filled('post_id')) {
            $query->where('post_id', $request->post_id);
        }

        // Filter parent comments only
        if ($request->filled('parent_only')) {
            $query->parent();
        }

        $comments = $query->orderBy('created_at', 'desc')->paginate(15);
        $posts = Post::published()->get(['id', 'title']);

        return view('admin.comments.index', compact('comments', 'posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $posts = Post::published()->get(['id', 'title']);
        return view('admin.comments.create', compact('posts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'post_id' => 'required|exists:posts,id',
            'parent_id' => 'nullable|exists:comments,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website' => 'nullable|url|max:255',
            'comment' => 'required|string',
            'status' => 'required|in:pending,approved,rejected',
            'ip_address' => 'nullable|ip',
            'user_agent' => 'nullable|string'
        ]);

        // Set IP and User Agent if not provided
        if (empty($validated['ip_address'])) {
            $validated['ip_address'] = $request->ip();
        }
        if (empty($validated['user_agent'])) {
            $validated['user_agent'] = $request->userAgent();
        }

        Comment::create($validated);

        return redirect()->route('admin.comments.index')
                        ->with('success', 'Comment created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Comment $comment)
    {
        $comment->load(['post', 'parent', 'replies']);
        return view('admin.comments.show', compact('comment'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Comment $comment)
    {
        $posts = Post::published()->get(['id', 'title']);
        $parentComments = Comment::where('post_id', $comment->post_id)
                                ->whereNull('parent_id')
                                ->where('id', '!=', $comment->id)
                                ->get(['id', 'name', 'comment']);
        
        return view('admin.comments.edit', compact('comment', 'posts', 'parentComments'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Comment $comment)
    {
        $validated = $request->validate([
            'post_id' => 'required|exists:posts,id',
            'parent_id' => 'nullable|exists:comments,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'website' => 'nullable|url|max:255',
            'comment' => 'required|string',
            'status' => 'required|in:pending,approved,rejected'
        ]);

        $comment->update($validated);

        return redirect()->route('admin.comments.index')
                        ->with('success', 'Comment updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Comment $comment)
    {
        // Delete all replies first
        $comment->replies()->delete();
        
        $comment->delete();

        return redirect()->route('admin.comments.index')
                        ->with('success', 'Comment deleted successfully.');
    }

    /**
     * Approve comment
     */
    public function approve(Comment $comment)
    {
        $comment->update(['status' => 'approved']);
        
        return response()->json([
            'success' => true,
            'message' => 'Comment approved successfully.',
            'status' => 'approved'
        ]);
    }

    /**
     * Reject comment
     */
    public function reject(Comment $comment)
    {
        $comment->update(['status' => 'rejected']);
        
        return response()->json([
            'success' => true,
            'message' => 'Comment rejected successfully.',
            'status' => 'rejected'
        ]);
    }

    /**
     * Mark as pending
     */
    public function pending(Comment $comment)
    {
        $comment->update(['status' => 'pending']);
        
        return response()->json([
            'success' => true,
            'message' => 'Comment marked as pending.',
            'status' => 'pending'
        ]);
    }

    /**
     * Bulk action for comments
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'comment_ids' => 'required|array',
            'comment_ids.*' => 'exists:comments,id'
        ]);

        $comments = Comment::whereIn('id', $validated['comment_ids']);
        
        switch ($validated['action']) {
            case 'approve':
                $comments->update(['status' => 'approved']);
                $message = 'Comments approved successfully.';
                break;
            case 'reject':
                $comments->update(['status' => 'rejected']);
                $message = 'Comments rejected successfully.';
                break;
            case 'delete':
                $comments->delete();
                $message = 'Comments deleted successfully.';
                break;
        }

        return redirect()->route('admin.comments.index')
                        ->with('success', $message);
    }
}
