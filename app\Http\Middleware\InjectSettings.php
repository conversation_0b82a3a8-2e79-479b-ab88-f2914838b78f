<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SettingsService;
use Symfony\Component\HttpFoundation\Response;

class InjectSettings
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Try to initialize settings if they don't exist
        try {
            $settingsCount = \App\Models\Setting::count();
            if ($settingsCount === 0) {
                $this->settingsService->initializeDefaults();
            }
        } catch (\Exception $e) {
            // Silently fail if settings table doesn't exist yet (during migrations)
        }
        
        $response = $next($request);

        // Only inject settings for HTML responses
        if ($response->headers->get('Content-Type') &&
            strpos($response->headers->get('Content-Type'), 'text/html') !== false) {

            try {
                $publicSettings = $this->settingsService->getPublicSettings();
                $settingsScript = $this->generateSettingsScript($publicSettings);

                $content = $response->getContent();

                // Inject before closing head tag
                $content = str_replace('</head>', $settingsScript . '</head>', $content);

                $response->setContent($content);
            } catch (\Exception $e) {
                // If there's an error getting settings, log it but don't crash the application
                \Illuminate\Support\Facades\Log::error('Error injecting settings: ' . $e->getMessage());
            }
        }

        return $response;
    }

    /**
     * Generate JavaScript settings script
     */
    private function generateSettingsScript($settings)
    {
        $settingsJson = json_encode($settings, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);

        return "
    <script>
        window.appSettings = {$settingsJson};

        // Helper function to get setting value
        window.getSetting = function(key, defaultValue) {
            return window.appSettings[key] !== undefined ? window.appSettings[key] : defaultValue;
        };

        // Helper function to check if setting exists
        window.hasSetting = function(key) {
            return window.appSettings[key] !== undefined;
        };

        // Helper function to get site info
        window.getSiteInfo = function() {
            return {
                name: window.getSetting('site_name', 'My Site'),
                description: window.getSetting('site_description', ''),
                logo: window.getSetting('site_logo', ''),
                favicon: window.getSetting('site_favicon', ''),
                contactEmail: window.getSetting('contact_email', ''),
                contactPhone: window.getSetting('contact_phone', '')
            };
        };

        // Helper function to get theme settings
        window.getThemeSettings = function() {
            return {
                primaryColor: window.getSetting('primary_color', '#2678a1db'),
                secondaryColor: window.getSetting('secondary_color', '#6c757d'),
                fontFamily: window.getSetting('font_family', 'Inter, sans-serif'),
                darkMode: window.getSetting('enable_dark_mode', false)
            };
        };

        // Helper function to get social media links
        window.getSocialLinks = function() {
            return {
                facebook: window.getSetting('facebook_url', ''),
                twitter: window.getSetting('twitter_url', ''),
                instagram: window.getSetting('instagram_url', ''),
                linkedin: window.getSetting('linkedin_url', ''),
                youtube: window.getSetting('youtube_url', '')
            };
        };

        // Apply theme settings to CSS variables
        if (window.getThemeSettings) {
            const theme = window.getThemeSettings();
            const root = document.documentElement;

            root.style.setProperty('--primary-color', theme.primaryColor);
            root.style.setProperty('--secondary-color', theme.secondaryColor);
            root.style.setProperty('--font-family', theme.fontFamily);

            if (theme.darkMode) {
                root.classList.add('dark-mode');
            }
        }
    </script>
";
    }
}
