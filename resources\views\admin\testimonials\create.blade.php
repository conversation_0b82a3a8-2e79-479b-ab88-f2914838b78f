@extends('layouts.admin')

@section('title', 'Add New Testimonial')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Add New Testimonial</h3>
                    <a href="{{ route('admin.testimonials.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Testimonials
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.testimonials.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Testimonial Content -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Testimonial Content</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="testimonial" class="form-label">Testimonial *</label>
                                            <textarea class="form-control @error('testimonial') is-invalid @enderror" 
                                                      id="testimonial" name="testimonial" rows="6" required>{{ old('testimonial') }}</textarea>
                                            @error('testimonial')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="rating" class="form-label">Rating</label>
                                            <select class="form-control @error('rating') is-invalid @enderror" 
                                                    id="rating" name="rating">
                                                <option value="">No Rating</option>
                                                <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>1 Star</option>
                                                <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>2 Stars</option>
                                                <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>3 Stars</option>
                                                <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>4 Stars</option>
                                                <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>5 Stars</option>
                                            </select>
                                            @error('rating')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Client Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Client Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="client_name" class="form-label">Client Name *</label>
                                                    <input type="text" class="form-control @error('client_name') is-invalid @enderror" 
                                                           id="client_name" name="client_name" value="{{ old('client_name') }}" required>
                                                    @error('client_name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="client_position" class="form-label">Position/Title</label>
                                                    <input type="text" class="form-control @error('client_position') is-invalid @enderror" 
                                                           id="client_position" name="client_position" value="{{ old('client_position') }}">
                                                    @error('client_position')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="client_company" class="form-label">Company</label>
                                                    <input type="text" class="form-control @error('client_company') is-invalid @enderror" 
                                                           id="client_company" name="client_company" value="{{ old('client_company') }}">
                                                    @error('client_company')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="client_location" class="form-label">Location</label>
                                                    <input type="text" class="form-control @error('client_location') is-invalid @enderror" 
                                                           id="client_location" name="client_location" value="{{ old('client_location') }}">
                                                    @error('client_location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status *</label>
                                            <select class="form-control @error('status') is-invalid @enderror" 
                                                    id="status" name="status" required>
                                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" 
                                                       name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Testimonial
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Client Image -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5>Client Photo</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <input type="file" class="form-control @error('client_image') is-invalid @enderror" 
                                                   id="client_image" name="client_image" accept="image/*">
                                            @error('client_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="text-muted">Max size: 2MB. Formats: JPG, PNG, GIF</small>
                                        </div>
                                        
                                        <!-- Preview -->
                                        <div id="image-preview" class="text-center" style="display: none;">
                                            <img id="preview-image" src="" alt="Preview" 
                                                 class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('admin.testimonials.index') }}" class="btn btn-secondary me-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Create Testimonial
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Image preview
document.getElementById('client_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-image').src = e.target.result;
            document.getElementById('image-preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('image-preview').style.display = 'none';
    }
});

// Rating visual feedback
document.getElementById('rating').addEventListener('change', function() {
    const rating = this.value;
    const ratingDisplay = document.getElementById('rating-display');
    
    if (ratingDisplay) {
        ratingDisplay.remove();
    }
    
    if (rating) {
        const stars = '★'.repeat(parseInt(rating)) + '☆'.repeat(5 - parseInt(rating));
        const display = document.createElement('div');
        display.id = 'rating-display';
        display.className = 'mt-2 text-warning';
        display.innerHTML = `<span style="font-size: 1.2em;">${stars}</span>`;
        this.parentNode.appendChild(display);
    }
});
</script>
@endpush
@endsection
